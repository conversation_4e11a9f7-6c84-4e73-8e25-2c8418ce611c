-- 潜孔施工数据视图

-- 创建视图：按日统计潜孔施工数据（总体）
CREATE OR REPLACE VIEW vdata_down_hole_drilling_daily_total_stats AS
SELECT 
    operation_date,
    SUM(progress_meters) AS total_progress_meters
FROM 
    data_down_hole_drilling
GROUP BY 
    operation_date
ORDER BY 
    operation_date;

-- 创建视图：按日期和项目部门统计潜孔施工数据
CREATE OR REPLACE VIEW vdata_down_hole_drilling_daily_department_stats AS
SELECT 
    d.operation_date,
    d.project_department_id,
    pd.project_department_name,
    SUM(d.progress_meters) AS total_progress_meters
FROM 
    data_down_hole_drilling d
JOIN
    base_project_department pd ON d.project_department_id = pd.project_department_id
GROUP BY
    d.operation_date,
    d.project_department_id,
    pd.project_department_name
ORDER BY 
    d.operation_date, d.project_department_id;

-- 创建视图：按日期和采场统计潜孔施工数据
CREATE OR REPLACE VIEW vdata_down_hole_drilling_daily_stope_stats AS
SELECT 
    d.operation_date,
    d.stope_id,
    s.stope_name,
    SUM(d.progress_meters) AS total_progress_meters
FROM 
    data_down_hole_drilling d
JOIN
    base_stope s ON d.stope_id = s.stope_id
GROUP BY
    d.operation_date,
    d.stope_id,
    s.stope_name
ORDER BY 
    d.operation_date, d.stope_id;

-- 创建视图：按日期和工作面统计潜孔施工数据
CREATE OR REPLACE VIEW vdata_down_hole_drilling_daily_working_face_stats AS
SELECT 
    d.operation_date,
    d.working_face_id,
    wf.working_face_name,
    SUM(d.progress_meters) AS total_progress_meters
FROM 
    data_down_hole_drilling d
JOIN
    base_working_face wf ON d.working_face_id = wf.working_face_id
GROUP BY
    d.operation_date,
    d.working_face_id,
    wf.working_face_name
ORDER BY 
    d.operation_date, d.working_face_id;

-- 创建视图：按日期和时段统计潜孔施工数据
CREATE OR REPLACE VIEW vdata_down_hole_drilling_daily_period_stats AS
SELECT 
    d.operation_date,
    d.working_period_id,
    wp.working_period_name,
    SUM(d.progress_meters) AS total_progress_meters
FROM 
    data_down_hole_drilling d
JOIN
    base_working_period wp ON d.working_period_id = wp.working_period_id
GROUP BY
    d.operation_date,
    d.working_period_id,
    wp.working_period_name
ORDER BY 
    d.operation_date, d.working_period_id;

-- 创建视图：按月统计潜孔施工数据（月定义为前一个月的29号到当月28号）
CREATE OR REPLACE VIEW vdata_down_hole_drilling_monthly_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    SUM(progress_meters) AS total_progress_meters
FROM 
    data_down_hole_drilling
CROSS JOIN LATERAL get_financial_month(operation_date) AS fm
GROUP BY 
    fm.financial_year, fm.financial_month
ORDER BY 
    year, month;

-- 创建视图：按自然周统计潜孔施工数据
CREATE OR REPLACE VIEW vdata_down_hole_drilling_nature_weekly_stats AS
SELECT 
    EXTRACT(YEAR FROM operation_date) AS year,
    EXTRACT(WEEK FROM operation_date) AS week_number,
    MIN(operation_date) AS week_start_date,
    MAX(operation_date) AS week_end_date,
    SUM(progress_meters) AS total_progress_meters
FROM 
    data_down_hole_drilling
GROUP BY 
    EXTRACT(YEAR FROM operation_date),
    EXTRACT(WEEK FROM operation_date)
ORDER BY 
    year, week_number;

-- 创建视图：按周统计潜孔施工数据（周四到周三为一周）
CREATE OR REPLACE VIEW vdata_down_hole_drilling_weekly_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number,
    wk.week_start_date,
    wk.week_end_date,
    SUM(progress_meters) AS total_progress_meters
FROM 
    data_down_hole_drilling
CROSS JOIN LATERAL get_week_thu_to_wed(operation_date) AS wk
GROUP BY 
    wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date
ORDER BY 
    year, week_number;

-- 创建视图：按年统计潜孔施工数据（年定义为上一年12月29号到当年12月28号）
CREATE OR REPLACE VIEW vdata_down_hole_drilling_yearly_stats AS
SELECT 
    fy.financial_year AS year,
    SUM(progress_meters) AS total_progress_meters
FROM 
    data_down_hole_drilling
CROSS JOIN LATERAL get_financial_year(operation_date) AS fy
GROUP BY 
    fy.financial_year
ORDER BY 
    year;
