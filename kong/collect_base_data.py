#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
收集基础数据脚本 - Kong模块（修正版）
从extracted_data.csv中提取项目部门、工作面（中段）、采场等基础数据，并生成SQL插入语句
"""

import pandas as pd
import os
import re
import glob

def collect_working_faces_from_file(csv_file):
    """从单个CSV文件中收集工作面（中段）名称"""
    if not os.path.exists(csv_file):
        print(f"警告: 找不到文件 {csv_file}")
        return []
    
    try:
        # 读取CSV文件，跳过前三行（标题）
        df = pd.read_csv(csv_file, encoding='utf-8', header=None, skiprows=3)
        
        # 中段名称在第七列（索引为6）
        if len(df.columns) < 7:
            print(f"警告: {csv_file} 格式不正确，缺少中段名称列")
            return []
        
        # 获取第七列数据（中段名称）
        working_face_names = df.iloc[:, 6].dropna().unique()
        
        # 过滤掉不是中段名称的行
        valid_working_face_names = []
        for name in working_face_names:
            name_str = str(name).strip()
            # 只保留包含"m"的中段名称，排除空值和无关内容
            if name_str and name_str != 'nan' and name_str.startswith('-'):
                # 排除包含特定关键词的行
                if not any(keyword in name_str for keyword in ["小计", "合计", "总计", "月计划"]):
                    if len(name_str) > 2:
                        valid_working_face_names.append(name_str)
        
        return valid_working_face_names
    
    except Exception as e:
        print(f"处理CSV文件 {csv_file} 时出错: {str(e)}")
        return []

def collect_stope_and_working_face_from_file(csv_file):
    """从单个CSV文件中收集采场名称和对应的工作面"""
    if not os.path.exists(csv_file):
        return []
    
    try:
        # 读取CSV文件，跳过前三行（标题）
        df = pd.read_csv(csv_file, encoding='utf-8', header=None, skiprows=3)
        if len(df.columns) < 8:
            return []
        
        stope_working_face_pairs = []
        
        # 同时获取中段（第7列）和采场（第8列）
        for index, row in df.iterrows():
            working_face = str(row.iloc[6]).strip() if pd.notna(row.iloc[6]) else ''
            stope_name = str(row.iloc[7]).strip() if pd.notna(row.iloc[7]) else ''
            
            # 过滤有效的中段和采场组合
            if (working_face and working_face != 'nan' and working_face.startswith('-') and
                stope_name and stope_name != 'nan' and len(stope_name) > 1):
                
                # 排除包含特定关键词的行
                if not any(keyword in working_face for keyword in ["小计", "合计", "总计", "月计划"]):
                    if not any(keyword in stope_name for keyword in ["采场", "合计", "总计", "小计", "月计划", "单位"]):
                        if not stope_name.isdigit():
                            stope_working_face_pairs.append((stope_name, working_face))
        
        return stope_working_face_pairs
    except Exception as e:
        print(f"处理CSV文件 {csv_file} 时出错: {str(e)}")
        return []

def collect_all_base_data():
    """收集所有基础数据"""
    csv_files = glob.glob('extracted_data*.csv')
    if not csv_files:
        print("错误: 找不到任何extracted_data*.csv文件")
        return None, None, None
    
    print(f"找到 {len(csv_files)} 个CSV文件: {csv_files}")
    
    all_working_faces = set()
    all_stope_working_face_pairs = set()  # 采场和工作面的配对
    
    for csv_file in csv_files:
        working_faces = collect_working_faces_from_file(csv_file)
        stope_pairs = collect_stope_and_working_face_from_file(csv_file)
        
        all_working_faces.update(working_faces)
        all_stope_working_face_pairs.update(stope_pairs)
        
        print(f"从 {csv_file} 中收集到 {len(working_faces)} 个工作面, {len(stope_pairs)} 个采场-工作面配对")
    
    unique_working_faces = sorted(list(all_working_faces))
    unique_stope_pairs = sorted(list(all_stope_working_face_pairs))
    
    print(f"\n总共收集到 {len(unique_working_faces)} 个工作面（中段）:")
    for name in unique_working_faces:
        print(f"  - {name}")
    
    print(f"\n总共收集到 {len(unique_stope_pairs)} 个采场-工作面配对:")
    for stope_name, working_face in unique_stope_pairs:
        print(f"  - {stope_name} -> {working_face}")
    
    return unique_working_faces, unique_stope_pairs

def map_department_name(department_name):
    """映射部门名称到标准名称"""
    mapping = {
        '采矿厂': '采矿厂',
        '湖南涟邵': '湖南涟邵建设有限公司',
        '中矿': '中矿金业股份有限公司'
    }
    return mapping.get(department_name, department_name)

def find_working_face_for_stope(stope_name, working_faces):
    """为采场找到对应的工作面名称"""
    # 根据采场名称的特点匹配对应的工作面（中段）
    # 这里需要根据实际业务逻辑来定义匹配规则
    # 暂时返回第一个工作面名称
    if working_faces:
        return working_faces[0]
    return None

def generate_department_inserts(departments):
    """生成项目部门插入语句"""
    if not departments:
        return []
    
    sql_inserts = []
    for dept in departments:
        mapped_name = map_department_name(dept)
        sql = f"INSERT INTO base_project_department (project_department_name) VALUES ('{mapped_name}') ON CONFLICT (project_department_name) DO NOTHING;"
        sql_inserts.append(sql)
    
    return sql_inserts

def generate_working_face_inserts(working_faces):
    """生成工作面插入语句"""
    if not working_faces:
        return []
    
    sql_inserts = []
    for face_name in working_faces:
        sql = f"INSERT INTO base_working_face (working_face_name) VALUES ('{face_name}') ON CONFLICT (working_face_name) DO NOTHING;"
        sql_inserts.append(sql)
    
    return sql_inserts

def generate_stope_inserts(stope_working_face_pairs):
    """生成采场插入语句"""
    if not stope_working_face_pairs:
        return []
    
    sql_inserts = []
    for stope_name, working_face_name in stope_working_face_pairs:
        # 为了避免采场名称冲突，使用采场名称+工作面作为唯一标识
        unique_stope_name = f"{stope_name}#{working_face_name}"
        sql = f"INSERT INTO base_stope (stope_name, working_face_id) VALUES ('{unique_stope_name}', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '{working_face_name}')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;"
        sql_inserts.append(sql)
    
    return sql_inserts

def generate_working_period_inserts():
    """生成工作时段插入语句"""
    working_periods = [
        (1, "0-8时"),
        (2, "8-20时"),
        (3, "20-0时")
    ]
    
    sql_inserts = []
    for period_id, period_name in working_periods:
        sql = f"INSERT INTO base_working_period (working_period_id, working_period_name) VALUES ({period_id}, '{period_name}') ON CONFLICT (working_period_id) DO NOTHING;"
        sql_inserts.append(sql)
    
    return sql_inserts

if __name__ == "__main__":
    working_faces, stope_pairs = collect_all_base_data()
    
    if working_faces is not None and stope_pairs is not None:
        working_face_sql = generate_working_face_inserts(working_faces)
        stope_sql = generate_stope_inserts(stope_pairs)
        working_period_sql = generate_working_period_inserts()
        
        output_file = "kong_base_data.sql"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("-- Kong模块基础数据插入语句\n\n")
            
            f.write("\n-- 工作时段基础数据\n")
            for sql in working_period_sql:
                f.write(sql + "\n")
            
            f.write("\n-- 工作面基础数据（对应中段）\n")
            for sql in working_face_sql:
                f.write(sql + "\n")
            
            f.write("\n-- 采场基础数据\n")
            for sql in stope_sql:
                f.write(sql + "\n")
        
        total_sql = len(working_face_sql) + len(stope_sql) + len(working_period_sql)
        print(f"\n已生成基础数据SQL插入语句到 {output_file}")
        print(f"总计: {len(working_period_sql)} 个时段, {len(working_face_sql)} 个工作面, {len(stope_sql)} 个采场")
        print(f"共 {total_sql} 条SQL语句")
    else:
        print("未能收集到基础数据")
