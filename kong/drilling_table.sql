-- 钻孔施工数据表（合并中深孔和潜孔）
CREATE TABLE IF NOT EXISTS data_drilling (
    id bigserial PRIMARY KEY,
    operation_date date NOT NULL,
    project_department_id bigint NOT NULL,
    stope_id bigint NOT NULL,
    working_face_id bigint NOT NULL,
    working_period_id bigint NOT NULL,
    drilling_type CHAR(1) NOT NULL, -- 钻孔类型：1-潜孔，2-中深孔
    progress_meters decimal(10,2) DEFAULT 0,
    
    -- 审计字段
    create_by bigint,
    create_time timestamp DEFAULT CURRENT_TIMESTAMP,
    update_by bigint,
    update_time timestamp DEFAULT CURRENT_TIMESTAMP,
    
    -- 约束
    CONSTRAINT fk_drilling_project_department FOREIGN KEY (project_department_id) REFERENCES base_project_department(project_department_id),
    CONSTRAINT fk_drilling_stope FOREIGN KEY (stope_id) REFERENCES base_stope(stope_id),
    CONSTRAINT fk_drilling_working_face FOREIGN KEY (working_face_id) REFERENCES base_working_face(working_face_id),
    CONSTRAINT fk_drilling_working_period FOREIGN KEY (working_period_id) REFERENCES base_working_period(working_period_id),
);

-- 添加表注释
COMMENT ON TABLE data_drilling IS '钻孔施工数据表（包含潜孔和中深孔）';
COMMENT ON COLUMN data_drilling.id IS '记录ID';
COMMENT ON COLUMN data_drilling.operation_date IS '施工日期';
COMMENT ON COLUMN data_drilling.project_department_id IS '项目部门ID';
COMMENT ON COLUMN data_drilling.stope_id IS '采场ID';
COMMENT ON COLUMN data_drilling.working_face_id IS '工作面ID';
COMMENT ON COLUMN data_drilling.working_period_id IS '工作时段ID';
COMMENT ON COLUMN data_drilling.drilling_type IS '钻孔类型：1-潜孔，2-中深孔';
COMMENT ON COLUMN data_drilling.progress_meters IS '进尺米数';
COMMENT ON COLUMN data_drilling.create_by IS '创建人';
COMMENT ON COLUMN data_drilling.create_time IS '创建时间';
COMMENT ON COLUMN data_drilling.update_by IS '更新人';
COMMENT ON COLUMN data_drilling.update_time IS '更新时间';

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_drilling_operation_date ON data_drilling(operation_date);
CREATE INDEX IF NOT EXISTS idx_drilling_project_department ON data_drilling(project_department_id);
CREATE INDEX IF NOT EXISTS idx_drilling_stope ON data_drilling(stope_id);
CREATE INDEX IF NOT EXISTS idx_drilling_working_face ON data_drilling(working_face_id);
CREATE INDEX IF NOT EXISTS idx_drilling_working_period ON data_drilling(working_period_id);
CREATE INDEX IF NOT EXISTS idx_drilling_type ON data_drilling(drilling_type);
CREATE INDEX IF NOT EXISTS idx_drilling_date_type ON data_drilling(operation_date, drilling_type);
