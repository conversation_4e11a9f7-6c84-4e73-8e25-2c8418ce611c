#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
生成SQL插入语句脚本
从CSV文件生成PostgreSQL数据库的INSERT语句 - 钻孔施工数据
自动从数据库查询采场ID、项目部门ID、工作面ID
"""

import pandas as pd
import re
from datetime import datetime
import os
import argparse
from decimal import Decimal, getcontext
import psycopg2
from psycopg2 import sql

# 设置decimal精度
getcontext().prec = 10  # 设置精度为10位

# 定义工作时段ID映射
WORKING_PERIOD_MAP = {
    '0-8': 1,
    '8-20': 2,
    '20-0': 3
}

# 定义钻孔类型映射
DRILLING_TYPE_MAP = {
    '中深孔': '2',
    '潜孔': '1',
    '潜孔（扩井孔）': '1'
}

def parse_date(date_str):
    """解析中文日期格式，转换为标准日期格式"""
    if not isinstance(date_str, str):
        return None
    try:
        # 格式1：12.31 （月.日）
        month_day_match = re.search(r'(\d+)\.(\d+)', date_str)
        if month_day_match:
            month = int(month_day_match.group(1))
            day = int(month_day_match.group(2))
            # 假设年份是2024年
            return f"2024-{month:02d}-{day:02d}"
        
        # 格式2：1月1日 （中文格式）
        chinese_date_match = re.search(r'(\d+)月(\d+)日', date_str)
        if chinese_date_match:
            month = int(chinese_date_match.group(1))
            day = int(chinese_date_match.group(2))
            # 假设年份是2025年
            return f"2025-{month:02d}-{day:02d}"
            
    except Exception as e:
        print(f"解析日期出错: {str(e)}")
    return None

def safe_value(value, default=0, as_decimal=True):
    """安全获取值，处理NaN和None，并确保返回数字类型"""
    if pd.isna(value) or value is None:
        return Decimal(str(default)) if as_decimal else default
    
    try:
        # 如果是字符串，尝试提取数字
        if isinstance(value, str):
            # 移除非数字字符（除了小数点和负号）
            cleaned = re.sub(r'[^\d.-]', '', value.strip())
            if cleaned == '' or cleaned == '-':
                return Decimal(str(default)) if as_decimal else default
            value = float(cleaned)
        
        return Decimal(str(value)) if as_decimal else float(value)
    except (ValueError, TypeError):
        return Decimal(str(default)) if as_decimal else default

def connect_to_database(host='**********', port=5432, dbname='lxbi', user='postgres', password='admin321.'):
    """连接到PostgreSQL数据库"""
    try:
        conn = psycopg2.connect(
            host=host,
            port=port,
            database=dbname,
            user=user,
            password=password
        )
        print("数据库连接成功")
        return conn
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None

def get_stope_id(conn, stope_name):
    """根据采场名称查询采场ID"""
    if not conn or not stope_name:
        return None
    
    try:
        cursor = conn.cursor()
        # 查询采场ID
        cursor.execute(
            "SELECT stope_id FROM base_stope WHERE stope_name = %s AND is_delete = 0",
            (stope_name,)
        )
        result = cursor.fetchone()
        cursor.close()
        
        if result:
            return result[0]
        else:
            print(f"未找到采场: {stope_name}")
            return None
    except Exception as e:
        print(f"查询采场ID出错: {e}")
        return None

def map_department_name(department_name):
    """映射部门名称到数据库中的名称"""
    mapping = {
        '采矿厂': '自营',
        '湖南涟邵': '涟邵建工',
        '中矿': '中矿建设'
    }
    return mapping.get(department_name, department_name)

def get_project_department_id(conn, department_name):
    """从base_project_department表中查询项目部门ID"""
    if not conn or not department_name:
        return None
    
    try:
        # 映射部门名称
        mapped_name = map_department_name(department_name)
        
        cursor = conn.cursor()
        # 查询项目部门ID
        cursor.execute(
            "SELECT project_department_id FROM base_project_department WHERE project_department_name = %s AND is_delete = 0",
            (mapped_name,)
        )
        result = cursor.fetchone()
        cursor.close()
        
        if result:
            return result[0]
        else:
            print(f"未找到项目部门: {mapped_name}")
            return None
    except Exception as e:
        print(f"查询项目部门ID出错: {e}")
        return None

def get_working_face_id(conn, stope_id):
    """根据采场ID查询工作面ID（取第一个有效的工作面）"""
    if not conn or not stope_id:
        return None
    
    try:
        cursor = conn.cursor()
        # 查询该采场下的工作面ID
        cursor.execute(
            "SELECT wf.working_face_id FROM base_working_face wf JOIN base_stope s ON wf.working_face_id = s.working_face_id WHERE s.stope_id = %s AND wf.is_delete = 0 LIMIT 1",
            (stope_id,)
        )
        result = cursor.fetchone()
        cursor.close()
        
        if result:
            return result[0]
        else:
            # 如果没有找到，返回默认值1
            print(f"未找到采场ID {stope_id} 对应的工作面，使用默认值1")
            return 1
    except Exception as e:
        print(f"查询工作面ID出错: {e}")
        return 1

def generate_sql_inserts(csv_file, output_file=None, encoding='utf-8', db_connect=True):
    """从CSV文件生成SQL插入语句"""
    print(f"读取CSV文件: {csv_file}")
    
    try:
        # 连接数据库
        conn = None
        if db_connect:
            conn = connect_to_database()
            if not conn:
                print("警告: 无法连接到数据库，将使用默认ID值")
        
        # 读取CSV文件，跳过前两行（标题）
        df = pd.read_csv(csv_file, header=None, skiprows=2, encoding=encoding)
        
        print("分析CSV文件结构...")
        
        # 初始化处理数据列表
        processed_data_list = []
        
        processed_rows = 0
        skipped_rows = 0
        
        # 用于向前填充
        current_date = None
        current_department = None
        current_drilling_type = None
        
        # 遍历CSV数据
        for index, row in df.iterrows():
            # 检查是否为标题行或无效行，如果是则跳过
            row_str = str(row[0]) if not pd.isna(row[0]) else ""
            stope_str = str(row[7]) if len(row) > 7 and not pd.isna(row[7]) else ""
            
            # 跳过包含标题信息的行
            if any(keyword in row_str for keyword in ["采场", "单位", "合计", "小计"]) or \
               any(keyword in stope_str for keyword in ["采场", "单位", "合计", "小计"]):
                skipped_rows += 1
                continue
            
            # 日期列（第一列）
            date_str = row[0]
            if not pd.isna(date_str) and isinstance(date_str, str):
                current_date = parse_date(date_str)
            
            # 部门列（第二列）
            if not pd.isna(row[1]) and isinstance(row[1], str) and row[1].strip():
                current_department = row[1].strip()
            
            # 设备类型列（第四列）
            if not pd.isna(row[3]) and isinstance(row[3], str) and row[3].strip():
                drilling_type = row[3].strip()
                if drilling_type in DRILLING_TYPE_MAP:
                    current_drilling_type = drilling_type
            
            # 如果没有有效的日期、部门或钻孔类型，跳过该行
            if not current_date or not current_department or not current_drilling_type:
                skipped_rows += 1
                continue
            
            # 中段列（第七列）和采场列（第八列）
            working_face_name = row[6] if not pd.isna(row[6]) and isinstance(row[6], str) else None
            stope_name = row[7] if not pd.isna(row[7]) and isinstance(row[7], str) else None
            
            # 检查采场名称和工作面名称是否有效
            if not stope_name or stope_name.strip() == '' or stope_name.strip() == '小计':
                skipped_rows += 1
                continue
            
            if not working_face_name or working_face_name.strip() == '':
                skipped_rows += 1
                continue
            
            # 构建采场名称#工作面名称的格式
            full_stope_name = f"{stope_name.strip()}#{working_face_name.strip()}"
            
            # 进尺数据列（9,10,11列）
            progress_0_8 = safe_value(row[9], 0)  # 第9列是0-8时
            progress_8_20 = safe_value(row[10], 0) # 第10列是8-20时
            progress_20_0 = safe_value(row[11], 0) # 第11列是20-0时
            
            # 处理三个时间段
            for period, progress in [('0-8', progress_0_8), ('8-20', progress_8_20), ('20-0', progress_20_0)]:
                if progress == 0:
                    continue
                
                working_period_id = WORKING_PERIOD_MAP[period]
                drilling_type = DRILLING_TYPE_MAP[current_drilling_type]
                
                # 存储处理后的数据
                processed_data = {
                    'department': current_department,
                    'stope_name': full_stope_name,
                    'working_period_id': working_period_id,
                    'operation_date': current_date,
                    'progress_meters': progress,
                    'drilling_type': drilling_type
                }
                processed_data_list.append(processed_data)
                processed_rows += 1
        
        # 生成SQL插入语句
        drilling_inserts = []
        
        # 缓存已查询过的采场ID、部门ID和工作面ID，避免重复查询
        stope_id_cache = {}
        department_id_cache = {}
        working_face_id_cache = {}
        
        for data in processed_data_list:
            stope_name = data['stope_name']
            department_name = data['department']
            operation_date = data['operation_date']
            working_period_id = data['working_period_id']
            progress_meters = data['progress_meters']
            drilling_type = data['drilling_type']
            
            # 查询采场ID
            stope_id = None
            if stope_name in stope_id_cache:
                stope_id = stope_id_cache[stope_name]
            elif conn:
                stope_id = get_stope_id(conn, stope_name)
                stope_id_cache[stope_name] = stope_id
            
            # 查询项目部门ID
            project_department_id = None
            if department_name in department_id_cache:
                project_department_id = department_id_cache[department_name]
            elif conn and department_name:
                project_department_id = get_project_department_id(conn, department_name)
                department_id_cache[department_name] = project_department_id
            
            # 查询工作面ID
            working_face_id = None
            if stope_id and stope_id in working_face_id_cache:
                working_face_id = working_face_id_cache[stope_id]
            elif conn and stope_id:
                working_face_id = get_working_face_id(conn, stope_id)
                working_face_id_cache[stope_id] = working_face_id
            
            # 如果没找到ID，跳过
            if not stope_id or not project_department_id or not working_face_id:
                print(f"跳过记录: 采场 '{stope_name}' 或部门 '{department_name}' 未找到ID")
                continue
            
            # 生成统一的INSERT语句到data_drilling表
            sql = f"INSERT INTO data_drilling (operation_date, project_department_id, stope_id, working_face_id, working_period_id, drilling_type, progress_meters) VALUES ('{operation_date}', {project_department_id}, {stope_id}, {working_face_id}, {working_period_id}, '{drilling_type}', {progress_meters});"
            drilling_inserts.append(sql)
        
        # 写入SQL文件
        if not output_file:
            output_file = "kong_dml.sql"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("-- 钻孔施工数据插入语句（合并表）\n")
            f.write("-- drilling_type: '1'=潜孔, '2'=中深孔\n\n")
            for sql in drilling_inserts:
                f.write(sql + "\n")
        
        print(f"成功生成 {len(drilling_inserts)} 条钻孔施工数据SQL插入语句到 {output_file}")
        print(f"处理行数: {processed_rows}, 跳过行数: {skipped_rows}")
        
        # 关闭数据库连接
        if conn:
            conn.close()
        
        return True
    
    except Exception as e:
        print(f"处理过程中出错: {e}")
        return False

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='生成钻孔施工数据的SQL插入语句')
    parser.add_argument('csv_file', help='CSV文件路径')
    parser.add_argument('-o', '--output', help='输出SQL文件路径', default='kong_dml.sql')
    parser.add_argument('--encoding', help='CSV文件编码', default='utf-8')
    parser.add_argument('--no-db', action='store_true', help='不连接数据库，使用默认ID值')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.csv_file):
        print(f"错误: CSV文件 {args.csv_file} 不存在")
        exit(1)
    
    success = generate_sql_inserts(
        csv_file=args.csv_file,
        output_file=args.output,
        encoding=args.encoding,
        db_connect=not args.no_db
    )
    
    if success:
        print("SQL插入语句生成完成！")
    else:
        print("SQL插入语句生成失败！")
        exit(1)
