-- Kong模块基础数据插入语句（修正版）

-- 项目部门基础数据
INSERT INTO base_project_department (project_department_name) VALUES ('中矿金业股份有限公司') ON CONFLICT (project_department_name) DO NOTHING;
INSERT INTO base_project_department (project_department_name) VALUES ('湖南涟邵建设有限公司') ON CONFLICT (project_department_name) DO NOTHING;
INSERT INTO base_project_department (project_department_name) VALUES ('采矿厂') ON CONFLICT (project_department_name) DO NOTHING;

-- 工作时段基础数据
INSERT INTO base_working_period (working_period_id, working_period_name) VALUES (1, '0-8时') ON CONFLICT (working_period_id) DO NOTHING;
INSERT INTO base_working_period (working_period_id, working_period_name) VALUES (2, '8-20时') ON CONFLICT (working_period_id) DO NOTHING;
INSERT INTO base_working_period (working_period_id, working_period_name) VALUES (3, '20-0时') ON CONFLICT (working_period_id) DO NOTHING;

-- 工作面基础数据（对应中段）
INSERT INTO base_working_face (working_face_name) VALUES ('-1020m/-992m') ON CONFLICT (working_face_name) DO NOTHING;
INSERT INTO base_working_face (working_face_name) VALUES ('-960m') ON CONFLICT (working_face_name) DO NOTHING;
INSERT INTO base_working_face (working_face_name) VALUES ('-992m') ON CONFLICT (working_face_name) DO NOTHING;
INSERT INTO base_working_face (working_face_name) VALUES ('-992m/-960m') ON CONFLICT (working_face_name) DO NOTHING;

-- 采场基础数据
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-12-2(补孔)#-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-15-3#-1020m/-992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m/-992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-15-3#-992m/-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m/-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-15-7#-1020m/-992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m/-992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-15-7#-992m/-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m/-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-16-1#-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-16-5#-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-5-6(扩井)#-992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-7-1/2#-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-20-1#-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-20-5#-1020m/-992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m/-992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-20-5#-992m/-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m/-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-22-9#-1020m/-992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m/-992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-22-9#-992m/-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m/-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-5-2#-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-5-9#-1020m/-992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m/-992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-5-9#-992m/-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m/-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
