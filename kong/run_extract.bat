python "c:\Users\<USER>\doc\5\extract_excel.py" "c:\Users\<USER>\doc\5\统计\2025年1月生产及建设统计表.xlsx" --start-row 109 --end-row 141 --start-col A --end-col K --output "C:\Users\<USER>\doc\5\kong\extracted_data1.xlsx"

python c:\Users\<USER>\doc\5\kong\generate_sql_inserts.py c:\Users\<USER>\doc\5\kong\extracted_data1.csv -o c:\Users\<USER>\doc\5\kong\dml1.sql

python "c:\Users\<USER>\doc\5\extract_excel.py" "c:\Users\<USER>\doc\5\统计\2025年2月生产及建设统计表.xlsx" --start-row 115 --end-row 147 --start-col A --end-col K --output "C:\Users\<USER>\doc\5\kong\extracted_data2.xlsx"

python c:\Users\<USER>\doc\5\kong\generate_sql_inserts.py c:\Users\<USER>\doc\5\kong\extracted_data2.csv -o c:\Users\<USER>\doc\5\kong\dml2.sql

python "c:\Users\<USER>\doc\5\extract_excel.py" "c:\Users\<USER>\doc\5\统计\2025年3月份生产及建设统计表.xlsx" --start-row 133 --end-row 167 --start-col A --end-col K --output "C:\Users\<USER>\doc\5\kong\extracted_data3.xlsx"

python c:\Users\<USER>\doc\5\kong\generate_sql_inserts.py c:\Users\<USER>\doc\5\kong\extracted_data3.csv -o c:\Users\<USER>\doc\5\kong\dml3.sql

python "c:\Users\<USER>\doc\5\extract_excel.py" "c:\Users\<USER>\doc\5\统计\2025年4月生产及建设统计表.xlsx" --start-row 133 --end-row 167 --start-col A --end-col K --output "C:\Users\<USER>\doc\5\kong\extracted_data4.xlsx"

python c:\Users\<USER>\doc\5\kong\generate_sql_inserts.py c:\Users\<USER>\doc\5\kong\extracted_data4.csv -o c:\Users\<USER>\doc\5\kong\dml4.sql

python "c:\Users\<USER>\doc\5\extract_excel.py" "c:\Users\<USER>\doc\5\统计\2025年5月份生产及建设统计表.xlsx" --start-row 144 --end-row 178 --start-col A --end-col K --output "C:\Users\<USER>\doc\5\kong\extracted_data5.xlsx"

python c:\Users\<USER>\doc\5\kong\generate_sql_inserts.py c:\Users\<USER>\doc\5\kong\extracted_data5.csv -o c:\Users\<USER>\doc\5\kong\dml5.sql

python "c:\Users\<USER>\doc\5\extract_excel.py" "c:\Users\<USER>\doc\5\统计\2025年6月份生产及建设统计表.xlsx" --start-row 135 --end-row 171 --start-col A --end-col K --output "C:\Users\<USER>\doc\5\kong\extracted_data6.xlsx"

python c:\Users\<USER>\doc\5\kong\generate_sql_inserts.py c:\Users\<USER>\doc\5\kong\extracted_data6.csv -o c:\Users\<USER>\doc\5\kong\dml6.sql

python "c:\Users\<USER>\doc\5\extract_excel.py" "c:\Users\<USER>\doc\5\统计\2025年7月份生产及建设统计表.xlsx" --start-row 135 --end-row 171 --start-col A --end-col K --output "C:\Users\<USER>\doc\5\kong\extracted_data7.xlsx"

python c:\Users\<USER>\doc\5\kong\generate_sql_inserts.py c:\Users\<USER>\doc\5\kong\extracted_data7.csv -o c:\Users\<USER>\doc\5\kong\dml7.sql


python c:\Users\<USER>\doc\5\kong\collect_stope_names.py


-1020m(\d)-(\d+-\d+(/\d+)?)采场

$1#-$2