-- Kong模块基础数据插入语句


-- 工作时段基础数据
INSERT INTO base_working_period (working_period_id, working_period_name) VALUES (1, '0-8时') ON CONFLICT (working_period_id) DO NOTHING;
INSERT INTO base_working_period (working_period_id, working_period_name) VALUES (2, '8-20时') ON CONFLICT (working_period_id) DO NOTHING;
INSERT INTO base_working_period (working_period_id, working_period_name) VALUES (3, '20-0时') ON CONFLICT (working_period_id) DO NOTHING;

-- 工作面基础数据（对应中段）
INSERT INTO base_working_face (working_face_name) VALUES ('-1020m') ON CONFLICT (working_face_name) DO NOTHING;
INSERT INTO base_working_face (working_face_name) VALUES ('-1020m/-1060m') ON CONFLICT (working_face_name) DO NOTHING;
INSERT INTO base_working_face (working_face_name) VALUES ('-1020m/-992m') ON CONFLICT (working_face_name) DO NOTHING;
INSERT INTO base_working_face (working_face_name) VALUES ('-922m') ON CONFLICT (working_face_name) DO NOTHING;
INSERT INTO base_working_face (working_face_name) VALUES ('-960m') ON CONFLICT (working_face_name) DO NOTHING;
INSERT INTO base_working_face (working_face_name) VALUES ('-960m/-992m') ON CONFLICT (working_face_name) DO NOTHING;
INSERT INTO base_working_face (working_face_name) VALUES ('-960m/1020m') ON CONFLICT (working_face_name) DO NOTHING;
INSERT INTO base_working_face (working_face_name) VALUES ('-960m/992m') ON CONFLICT (working_face_name) DO NOTHING;
INSERT INTO base_working_face (working_face_name) VALUES ('-992m') ON CONFLICT (working_face_name) DO NOTHING;
INSERT INTO base_working_face (working_face_name) VALUES ('-992m/-960m') ON CONFLICT (working_face_name) DO NOTHING;
INSERT INTO base_working_face (working_face_name) VALUES ('-992m/1020m') ON CONFLICT (working_face_name) DO NOTHING;

-- 采场基础数据
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-12-2(补孔)#-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-15-1#-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-15-2#-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-15-2#-960m/992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m/992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-15-3#-1020m/-992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m/-992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-15-3#-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-15-3#-960m/992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m/992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-15-3#-992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-15-3#-992m/-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m/-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-15-3#-992m/1020m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m/1020m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-15-7#-1020m/-992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m/-992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-15-7#-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-15-7#-960m/992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m/992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-15-7#-992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-15-7#-992m/-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m/-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-15-7#-992m/1020m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m/1020m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-16-1#-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-16-1/2#-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-16-5#-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-1溜井#-1020m/-1060m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m/-1060m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-2-1#-960m/992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m/992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-2-1#-992m/1020m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m/1020m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-20-1#-1020m/-992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m/-992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-20-1#-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-20-1#-960m/992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m/992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-20-1扩井孔#-992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-20-3#-1020m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-20-3#-1020m/-992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m/-992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-20-3#-960m/992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m/992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-20-3#-992m/-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m/-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-20-3#-992m/1020m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m/1020m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-26-5#-1020m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-26-9#-1020m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-26-9#-1020m/-992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m/-992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-5-6(扩井)#-992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-7-1/2#-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-8-3#-960m/1020m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m/1020m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('16-1#-960m/1020m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m/1020m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-11-2#-1020m/-992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m/-992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-11-2#-992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-11-2(扩井孔)#-992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-12-7#-1020m/-992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m/-992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-12-7#-960m/992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m/992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-12-7#-992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-12-7#-992m/1020m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m/1020m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-2-3#-960m/992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m/992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-2-3#-992m/-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m/-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-2-3#-992m/1020m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m/1020m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-2-7#-960m/992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m/992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-2-7#-992m/1020m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m/1020m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-20-1#-1020m/-992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m/-992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-20-1#-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-20-2#-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-20-2#-960m/992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m/992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-20-3#-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-20-5#-1020m/-992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m/-992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-20-5#-922m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-922m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-20-5#-960m/992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m/992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-20-5#-992m/-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m/-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-20-9#-1020m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-20-9#-1020m/-992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m/-992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-20-9#-960m/-992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m/-992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-20-9#-960m/992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m/992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-20-9#-992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-20-9#-992m/1020m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m/1020m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-22-2#-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-22-5#-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-22-5#-992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-22-5#-992m/1020m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m/1020m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-22-9#-1020m/-992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m/-992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-22-9#-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-22-9#-992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-22-9#-992m/-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m/-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-22-9#-992m/1020m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m/1020m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-4-1#-1020m/-992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m/-992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-4-1#-992m/-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m/-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-5-2#-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-5-5#-1020m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-5-5#-1020m/-992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m/-992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-5-5#-992m/1020m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m/1020m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-5-5(扩井孔)#-992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-5-9#-1020m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-5-9#-1020m/-992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m/-992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-5-9#-960m/992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m/992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-5-9#-992m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-5-9#-992m/-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m/-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-5-9#-992m/1020m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m/1020m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-7溜井#-1020m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('4-1溜井#-1020m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('4-2溜井#-1020m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('措施溜井#-1020m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('措施溜井（扩井）#-960m', (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m')) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
