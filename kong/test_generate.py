#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试生成SQL插入语句脚本
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from generate_sql_inserts import generate_sql_inserts

def test_generate():
    """测试生成SQL插入语句"""
    print("开始测试生成SQL插入语句...")
    
    # 测试文件路径
    csv_file = "extracted_data1.csv"
    output_file = "test_output.sql"
    
    # 检查CSV文件是否存在
    if not os.path.exists(csv_file):
        print(f"错误: CSV文件 {csv_file} 不存在")
        return False
    
    # 生成SQL插入语句（不连接数据库）
    success = generate_sql_inserts(
        csv_file=csv_file,
        output_file=output_file,
        encoding='utf-8',
        db_connect=False  # 不连接数据库，使用默认值测试
    )
    
    if success:
        print(f"测试成功！生成的SQL文件: {output_file}")
        
        # 读取并显示生成的SQL文件的前几行
        if os.path.exists(output_file):
            with open(output_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                print(f"\n生成的SQL文件内容（前10行）:")
                for i, line in enumerate(lines[:10], 1):
                    print(f"{i}: {line.rstrip()}")
        
        return True
    else:
        print("测试失败！")
        return False

if __name__ == "__main__":
    test_generate()
