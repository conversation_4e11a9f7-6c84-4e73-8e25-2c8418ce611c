-- Kong模块基础数据插入语句

-- 项目部门基础数据
INSERT INTO base_project_department (project_department_name) VALUES ('中矿金业股份有限公司') ON CONFLICT (project_department_name) DO NOTHING;
INSERT INTO base_project_department (project_department_name) VALUES ('湖南涟邵建设有限公司') ON CONFLICT (project_department_name) DO NOTHING;
INSERT INTO base_project_department (project_department_name) VALUES ('采矿厂') ON CONFLICT (project_department_name) DO NOTHING;

-- 工作时段基础数据
INSERT INTO base_working_period (working_period_id, working_period_name) VALUES (1, '0-8时') ON CONFLICT (working_period_id) DO NOTHING;
INSERT INTO base_working_period (working_period_id, working_period_name) VALUES (2, '8-20时') ON CONFLICT (working_period_id) DO NOTHING;
INSERT INTO base_working_period (working_period_id, working_period_name) VALUES (3, '20-0时') ON CONFLICT (working_period_id) DO NOTHING;

-- 工作面基础数据
INSERT INTO base_working_face (working_face_id, working_face_name) VALUES (1, '0号区域工作面') ON CONFLICT (working_face_id) DO NOTHING;
INSERT INTO base_working_face (working_face_id, working_face_name) VALUES (2, '2号区域工作面') ON CONFLICT (working_face_id) DO NOTHING;
INSERT INTO base_working_face (working_face_id, working_face_name) VALUES (3, '其他工作面') ON CONFLICT (working_face_id) DO NOTHING;

-- 采场基础数据
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-12-2(补孔)', 1) ON CONFLICT (stope_name) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-15-3', 1) ON CONFLICT (stope_name) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-15-7', 1) ON CONFLICT (stope_name) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-16-1', 1) ON CONFLICT (stope_name) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-16-5', 1) ON CONFLICT (stope_name) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-5-6(扩井)', 1) ON CONFLICT (stope_name) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0-7-1/2', 1) ON CONFLICT (stope_name) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-20-1', 2) ON CONFLICT (stope_name) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-20-5', 2) ON CONFLICT (stope_name) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-22-9', 2) ON CONFLICT (stope_name) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-5-2', 2) ON CONFLICT (stope_name) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-5-2（计划外）', 2) ON CONFLICT (stope_name) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2-5-9', 2) ON CONFLICT (stope_name) DO NOTHING;
