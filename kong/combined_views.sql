-- 综合统计视图（中深孔+潜孔）

-- 创建视图：按日统计所有钻孔施工数据（中深孔+潜孔）
CREATE OR REPLACE VIEW vdata_all_drilling_daily_stats AS
SELECT 
    operation_date,
    '中深孔' AS drilling_category,
    SUM(progress_meters) AS total_progress_meters
FROM 
    data_deep_hole_drilling
GROUP BY 
    operation_date
UNION ALL
SELECT 
    operation_date,
    '潜孔' AS drilling_category,
    SUM(progress_meters) AS total_progress_meters
FROM 
    data_down_hole_drilling
GROUP BY 
    operation_date
ORDER BY 
    operation_date, drilling_category;

-- 创建视图：按月统计所有钻孔施工数据（中深孔+潜孔）
CREATE OR REPLACE VIEW vdata_all_drilling_monthly_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    '中深孔' AS drilling_category,
    SUM(progress_meters) AS total_progress_meters
FROM 
    data_deep_hole_drilling
CROSS JOIN LATERAL get_financial_month(operation_date) AS fm
GROUP BY 
    fm.financial_year, fm.financial_month
UNION ALL
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    '潜孔' AS drilling_category,
    SUM(progress_meters) AS total_progress_meters
FROM 
    data_down_hole_drilling
CROSS JOIN LATERAL get_financial_month(operation_date) AS fm
GROUP BY 
    fm.financial_year, fm.financial_month
ORDER BY 
    year, month, drilling_category;

-- 创建视图：按周统计所有钻孔施工数据（中深孔+潜孔）
CREATE OR REPLACE VIEW vdata_all_drilling_weekly_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number,
    wk.week_start_date,
    wk.week_end_date,
    '中深孔' AS drilling_category,
    SUM(progress_meters) AS total_progress_meters
FROM 
    data_deep_hole_drilling
CROSS JOIN LATERAL get_week_thu_to_wed(operation_date) AS wk
GROUP BY 
    wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date
UNION ALL
SELECT 
    wk.week_year AS year,
    wk.week_number,
    wk.week_start_date,
    wk.week_end_date,
    '潜孔' AS drilling_category,
    SUM(progress_meters) AS total_progress_meters
FROM 
    data_down_hole_drilling
CROSS JOIN LATERAL get_week_thu_to_wed(operation_date) AS wk
GROUP BY 
    wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date
ORDER BY 
    year, week_number, drilling_category;

-- 创建视图：按年统计所有钻孔施工数据（中深孔+潜孔）
CREATE OR REPLACE VIEW vdata_all_drilling_yearly_stats AS
SELECT 
    fy.financial_year AS year,
    '中深孔' AS drilling_category,
    SUM(progress_meters) AS total_progress_meters
FROM 
    data_deep_hole_drilling
CROSS JOIN LATERAL get_financial_year(operation_date) AS fy
GROUP BY 
    fy.financial_year
UNION ALL
SELECT 
    fy.financial_year AS year,
    '潜孔' AS drilling_category,
    SUM(progress_meters) AS total_progress_meters
FROM 
    data_down_hole_drilling
CROSS JOIN LATERAL get_financial_year(operation_date) AS fy
GROUP BY 
    fy.financial_year
ORDER BY 
    year, drilling_category;
