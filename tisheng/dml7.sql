-- 插入提取数据到PostgreSQL数据库
-- 生成日期: 2025-07-18 21:34:46

-- 清空现有数据（如果需要）
-- TRUNCATE TABLE data_mine_hoisting CASCADE;

-- 插入提升和故障数据（使用新的合并表结构）
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-29', 318, 47, 106, 6044.67, '井下待料', '5:18:00', '6:05:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-29', 55, 0, 21, 1216.11, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-29', 20, 55, 4, 232.33, '井下待料', '7:20:00', '8:15:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-29', 225, 210, 114, 6316.04, '主井维修', '13:00:00', '16:30:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-29', 150, 0, 62, 3386.28, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-29', 300, 0, 113, 6335.01, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-30', 420, 0, 166, 9563.15, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-30', 360, 240, 132, 7565.96, '主井检修', '13:00:00', '17:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-30', 45, 8, 17, 978.07, '选厂s2皮带故障', '17:45:00', '17:53:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-30', 67, 0, 27, 1551.34, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-30', 300, 0, 115, 6648.8, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-07-01', 420, 0, 157, 9072.64, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-01', 60, 660, 22, 1243.56, '停机检修', '8:00:00', '19:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-07-01', 0, 300, 0, 0.0, '停机检修', '19:00:00', '0:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-07-02', 0, 420, 0, 0.0, '主井计划检修', '0:00:00', '7:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-02', 325, 35, 122, 6776.37, '选维修厂', '7:00:00', '7:35:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-02', 145, 50, 69, 3813.29, '主井检修', '13:00:00', '13:50:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-02', 85, 60, 40, 2174.64, '主井箕斗开焊', '16:35:00', '17:35:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-07-02', 300, 0, 111, 6379.61, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-07-03', 420, 0, 166, 9569.02, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-03', 60, 240, 20, 1147.26, '主井检修', '8:00:00', '12:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-03', 102, 68, 39, 2254.13, '井下待料', '13:42:00', '14:50:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-03', 173, 42, 66, 3800.67, '选厂堵料', '17:43:00', '18:25:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-03', 35, 0, 15, 871.44, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-07-03', 115, 48, 45, 2481.44, '井下待料', '20:55:00', '21:43:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-07-03', 137, 0, 56, 3083.76, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-07-04', 110, 130, 42, 2342.88, '井下待料', '1:50:00', '4:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-07-04', 180, 0, 64, 3902.1, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-04', 180, 250, 66, 3744.67, '主井检修', '10:00:00', '14:10:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-04', 290, 0, 115, 6478.57, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-07-04', 300, 0, 116, 6633.25, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-07-05', 22, 70, 7, 396.57, '井下待料', '0:22:00', '1:32:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-07-05', 183, 145, 70, 4041.03, '选厂堵料', '4:35:00', '7:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-05', 180, 60, 71, 3966.99, '选厂堵料', '7:00:00', '8:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-05', 160, 320, 72, 3984.21, '主井检修', '11:00:00', '16:20:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-07-05', 300, 0, 126, 6978.94, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-07-06', 305, 115, 116, 6821.85, '选厂堵料', '5:05:00', '7:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-06', 317, 63, 122, 6999.5, '选厂堵料', '7:00:00', '8:03:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-06', 115, 190, 44, 2517.01, '主井检修', '13:20:00', '16:30:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-06', 20, 15, 8, 466.37, '选厂紧磁辊螺丝', '18:25:00', '18:40:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-07-06', 300, 0, 124, 6864.7, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-07-07', 220, 53, 76, 4725.38, '选厂堵料', '3:40:00', '4:33:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-07-07', 147, 0, 57, 3218.26, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-07', 120, 220, 43, 2424.71, '主井检修', '9:00:00', '12:40:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-07', 216, 24, 63, 3542.79, '电机过热', '16:16:00', '16:40:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-07', 140, 0, 61, 3457.74, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-07-07', 270, 30, 87, 4961.22, '箕斗油缸打压', '23:30:00', '0:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-07-08', 210, 60, 80, 4349.97, '主井主箕斗油缸爆管加压', '0:00:00', '1:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-07-08', 0, 150, 0, 0.0, '选厂堵料', '4:30:00', '7:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-08', 155, 60, 58, 3213.56, '选厂堵料', '7:00:00', '8:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-08', 95, 400, 38, 2102.87, '主井检修', '8:00:00', '14:40:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-08', 0, 10, 0, 0.0, '焊下料口选厂', '17:15:00', '17:25:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-07-08', 300, 0, 111, 6180.15, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-07-09', 380, 40, 147, 8202.23, '选厂料仓满', '6:20:00', '7:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-09', 175, 60, 62, 3411.79, '选厂料仓满', '7:00:00', '8:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-09', 260, 210, 97, 5372.42, '主井检修', '8:00:00', '11:30:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-09', 0, 15, 0, 0.0, '选厂堵料', '14:25:00', '14:40:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-07-09', 90, 50, 31, 1707.58, '井下待料', '20:35:00', '21:25:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-07-09', 155, 0, 58, 3149.1, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-07-10', 420, 0, 163, 9070.93, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-10', 25, 35, 8, 386.23, '井下待料', '7:25:00', '8:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-10', 430, 230, 141, 7949.06, '主井检修', '8:00:00', '11:50:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-07-10', 300, 0, 87, 4736.97, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-07-11', 420, 0, 162, 9018.43, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-11', 125, 225, 47, 2584.31, '主井检修', '9:05:00', '12:50:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-11', 370, 0, 133, 7371.38, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-07-11', 300, 0, 115, 6366.88, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-07-12', 420, 0, 168, 9196.68, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-12', 120, 600, 51, 2837.25, '因选厂更换圆锥配重护板，主井非计划检修', '9:00:00', '19:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-07-12', 50, 105, 16, 900.73, '选厂更换圆锥配重护板，主井非计划检修', '19:00:00', '20:45:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-07-12', 12, 115, 3, 144.32, '选厂更换圆锥配重护板，主井非计划检修', '21:35:00', '23:30:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-07-12', 0, 18, 0, 0.0, '井下1200处理副斗开关', '23:42:00', '24:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-07-13', 237, 21, 89, 4739.12, '井下1200处理副斗开关', '0:00:00', '0:21:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-07-13', 95, 67, 39, 2148.38, '主井挂钩故障', '4:18:00', '5:25:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-13', 360, 240, 135, 7443.54, '主井检修', '13:00:00', '17:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-13', 120, 0, 46, 2523.36, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-07-13', 300, 0, 118, 6541.28, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-07-14', 420, 0, 160, 8944.75, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-14', 120, 240, 45, 2497.88, '主井检修', '9:00:00', '13:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-14', 80, 280, 29, 1590.42, '主井安全挂钩故障', '14:20:00', '19:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-07-14', 0, 300, 0, 0.0, 'c', '19:00:00', '0:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-07-15', 0, 420, 0, 0.0, '处理主井安全挂钩故障', '0:00:00', '7:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-15', 60, 380, 9, 496.01, '处理主井安全挂钩故障', '7:00:00', '13:20:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-15', 195, 85, 70, 3852.66, '主井箕斗开关故障', '14:20:00', '15:45:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-07-15', 300, 0, 101, 5573.43, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-07-16', 420, 0, 143, 7931.3, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-16', 360, 240, 119, 6582.89, '主井检修', '13:00:00', '17:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-16', 120, 0, 42, 2310.68, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-07-16', 150, 10, 53, 2949.93, '选厂堵料', '21:30:00', '21:40:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-07-16', 140, 0, 60, 3266.53, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-07-17', 420, 0, 146, 8135.84, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-17', 180, 24, 57, 3131.05, '选厂堵料', '10:00:00', '10:24:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-17', 36, 240, 20, 1105.32, '主井检修', '11:00:00', '15:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-17', 140, 50, 50, 2794.09, '选厂堵料', '17:20:00', '18:10:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-07-17', 50, 0, 19, 1022.09, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-07-17', 300, 0, 118, 6493.88, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-07-18', 420, 0, 158, 8701.41, NULL, NULL, NULL, 'admin', 'admin');