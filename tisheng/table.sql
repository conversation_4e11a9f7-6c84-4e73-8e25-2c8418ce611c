-- 矿井提升数据表（合并提升数据和故障记录）
CREATE TABLE IF NOT EXISTS data_mine_hoisting
(
    id    bigserial primary key,
    working_period_id   bigint NOT NULL,
    operation_date      date NOT NULL,
    operation_time      integer,          -- 运行时间（分钟）
    fault_time          integer,          -- 故障时长（分钟）
    buckets             integer,          -- 提升斗数
    weight              numeric(10,2),    -- 提升量（吨）
    fault_reason        varchar(255),     -- 故障原因
    fault_start_time    time,             -- 故障开始时间
    fault_end_time      time,             -- 故障结束时间
    create_by           varchar(64),
    create_time         timestamp(6) DEFAULT now(),
    update_by           varchar(64),
    update_time         timestamp(6) DEFAULT now(),
    FOREIGN KEY (working_period_id) REFERENCES base_working_period(working_period_id)
);

COMMENT ON TABLE data_mine_hoisting IS '矿井提升数据表';
COMMENT ON COLUMN data_mine_hoisting.id IS '提升数据ID';
COMMENT ON COLUMN data_mine_hoisting.working_period_id IS '作业时段ID';
COMMENT ON COLUMN data_mine_hoisting.operation_date IS '作业日期';
COMMENT ON COLUMN data_mine_hoisting.operation_time IS '运行时间（分钟）';
COMMENT ON COLUMN data_mine_hoisting.fault_time IS '故障时长（分钟）';
COMMENT ON COLUMN data_mine_hoisting.buckets IS '提升斗数';
COMMENT ON COLUMN data_mine_hoisting.weight IS '提升量（吨）';
COMMENT ON COLUMN data_mine_hoisting.fault_reason IS '故障原因';
COMMENT ON COLUMN data_mine_hoisting.fault_start_time IS '故障开始时间';
COMMENT ON COLUMN data_mine_hoisting.fault_end_time IS '故障结束时间';
COMMENT ON COLUMN data_mine_hoisting.create_by IS '创建人';
COMMENT ON COLUMN data_mine_hoisting.create_time IS '创建时间';
COMMENT ON COLUMN data_mine_hoisting.update_by IS '更新人';
COMMENT ON COLUMN data_mine_hoisting.update_time IS '更新时间';

-- 创建非唯一索引以提高查询性能（允许每个日期每个时段有多条记录）
CREATE INDEX idx_data_mine_hoisting_date_period 
ON data_mine_hoisting (operation_date, working_period_id);

CREATE INDEX idx_data_mine_hoisting_date 
ON data_mine_hoisting (operation_date);

CREATE INDEX idx_data_mine_hoisting_period 
ON data_mine_hoisting (working_period_id);
