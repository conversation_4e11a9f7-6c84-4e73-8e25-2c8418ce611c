-- 插入提取数据到PostgreSQL数据库
-- 生成日期: 2025-07-07 18:56:00

-- 清空现有数据（如果需要）
-- TRUNCATE TABLE data_mine_hoisting CASCADE;

-- 插入提升和故障数据（使用新的合并表结构）
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-05-29', 320, 100, 120, 6702.53, '停产检修', '5:20:00', '7:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-05-29', 0, 720, 0, 0.0, '停机检修', '7:00:00', '19:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-05-29', 0, 300, 0, 0.0, '停机检修', '19:00:00', '0:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-05-30', 0, 420, 0, 0.0, '停机检修', '0:00:00', '7:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-05-30', 0, 720, 0, 0.0, '停机检修', '7:00:00', '19:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-05-30', 165, 115, 64, 3487.61, '主井检修', '19:00:00', '20:55:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-05-30', 0, 20, 0, 0.0, '选厂堵料', '23:40:00', '0:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-05-31', 385, 35, 140, 7611.05, '选厂堵料', '0:00:00', '0:35:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-05-31', 34, 26, 9, 483.99, '井下待料', '7:34:00', '8:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-05-31', 0, 210, 0, 0.0, '主井检修', '8:00:00', '11:30:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-05-31', 0, 450, 0, 0.0, '主井尾绳断股停运', '11:30:00', '19:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-05-31', 0, 300, 0, 0.0, '主井尾绳断股停运', '19:00:00', '0:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-01', 0, 420, 0, 0.0, '主井尾绳断股停运', '0:00:00', '07:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-01', 0, 720, 0, 0.0, '主井尾绳断股停运', '07:00:00', '19:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-01', 0, 300, 0, 0.0, '主井尾绳断股停运', '19:00:00', '00:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-02', 0, 420, 0, 0.0, '主井尾绳断股停运', '0:00:00', '07:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-02', 0, 720, 0, 0.0, '7：主井尾绳断股停运', '00:00:00', '19:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-02', 0, 300, 0, 0.0, '19：主井尾绳断股停运', '00:00:00', '00:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-03', 0, 420, 0, 0.0, '主井尾绳断股停运', '0:00:00', '07:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-03', 0, 720, 0, 0.0, '主井尾绳断股停运', '0:00:00', '07:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-03', 0, 300, 0, 0.0, '主井尾绳断股停运', '19:00:00', '0:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-04', 0, 420, 0, 0.0, '主井尾绳断股停运', '0:00:00', '07:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-04', 0, 720, 0, 0.0, '主井尾绳断股停运', '0:00:00', '07:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-04', 0, 300, 0, 0.0, '主井尾绳断股停运', '19:00:00', '0:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-05', 0, 420, 0, 0.0, '主井尾绳断股停运', '0:00:00', '07:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-05', 43, 600, 1, 51.37, '主井尾绳断股停运', '7:00:00', '17:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-05', 0, 77, 0, 0.0, '选厂堵料', '17:43:00', '19:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-05', 96, 35, 35, 1832.03, '选厂堵料', '19:00:00', '19:35:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-05', 135, 34, 52, 2790.83, '选厂堵料', '21:11:00', '21:45:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-06', 27, 53, 11, 592.87, '选厂堵料', '0:27:00', '1:20:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-06', 230, 90, 86, 4619.59, '选厂堵料', '5:10:00', '6:40:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-06', 20, 0, 8, 427.75, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-06', 223, 112, 71, 3829.51, '选厂堵料', '10:33:00', '12:35:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-06', 172, 91, 54, 2781.21, '选厂堵料', '15:27:00', '16:58:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-06', 35, 87, 11, 599.32, '选厂堵料', '17:33:00', '19:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-06', 240, 60, 81, 4409.06, '选厂堵料', '19:00:00', '20:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-07', 65, 11, 24, 1311.32, '选厂堵料', '1:05:00', '1:16:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-07', 292, 52, 111, 6083.45, '选厂堵料', '6:08:00', '7:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-07', 255, 60, 89, 4792.62, '选厂堵料', '7:00:00', '8:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-07', 200, 85, 74, 3994.06, '选厂堵料', '12:15:00', '13:40:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-07', 90, 30, 35, 1914.47, '选厂堵料', '17:00:00', '17:30:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-07', 300, 0, 82, 4491.66, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-08', 420, 0, 141, 7705.82, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-08', 500, 60, 175, 9651.6, '井下维修开关，主井更换滑套', '15:20:00', '16:20:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-08', 160, 0, 62, 3392.31, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-08', 300, 0, 108, 5935.42, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-09', 420, 0, 165, 9120.57, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-09', 90, 180, 28, 1521.77, '主井检修', '8:30:00', '12:30:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-09', 260, 30, 101, 5634.66, '选厂堵料', '16:50:00', '17:20:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-09', 100, 0, 43, 2350.73, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-09', 300, 0, 113, 6303.68, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-10', 420, 0, 160, 9050.96, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-10', 360, 185, 138, 7621.65, '主井检修', '13:00:00', '16:05:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-10', 175, 0, 65, 3492.04, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-10', 300, 0, 117, 6403.36, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-11', 420, 0, 159, 8847.38, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-11', 360, 190, 136, 7599.06, '主井检修', '13:00:00', '16:10:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-11', 170, 0, 66, 3692.85, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-11', 300, 0, 105, 5808.33, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-12', 420, 0, 165, 9168.76, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-12', 200, 200, 73, 4043.82, '主井检修', '10:20:00', '13:40:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-12', 400, 0, 122, 6770.41, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-12', 300, 0, 120, 6584.99, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-13', 420, 0, 147, 8399.32, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-13', 60, 200, 16, 886.36, '主井检修', '8:00:00', '11:20:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-13', 460, 0, 182, 9851.35, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-13', 300, 0, 112, 6247.37, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-14', 420, 0, 154, 8594.51, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-14', 120, 185, 44, 2439.77, '主井检修', '9:00:00', '12:05:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-14', 415, 0, 170, 9448.47, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-14', 300, 0, 117, 6585.16, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-15', 420, 0, 163, 9192.59, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-15', 30, 30, 10, 563.61, '井下待料', '7:30:00', '8:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-15', 460, 200, 166, 9361.79, '主井检修', '8:00:00', '11:20:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-15', 300, 0, 106, 5970.25, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-16', 30, 40, 13, 736.07, '待料', '0:30:00', '1:10:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-16', 350, 0, 138, 7766.46, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-16', 90, 180, 27, 1464.24, '主井检修', '8:30:00', '11:30:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-16', 450, 0, 171, 9280.88, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-16', 300, 0, 109, 6019.79, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-17', 420, 0, 153, 8506.16, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-17', 96, 214, 36, 2000.77, '主井检修', '8:36:00', '12:10:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-17', 140, 60, 34, 1809.14, '焊接挡料板、更换箕斗安全锁电池', '14:30:00', '15:30:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-17', 210, 0, 76, 4147.59, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-17', 300, 0, 118, 6555.6, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-18', 420, 0, 150, 8374.87, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-18', 140, 220, 44, 2369.32, '主井检修', '9:20:00', '13:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-18', 360, 0, 129, 7038.29, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-18', 300, 0, 122, 6795.42, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-19', 420, 0, 143, 7937.83, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-19', 120, 210, 43, 2383.91, '主井检修', '9:00:00', '12:30:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-19', 390, 0, 157, 8706.8, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-19', 232, 13, 85, 4730.59, '选厂堵料', '22:52:00', '23:05:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-19', 55, 0, 20, 1118.52, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-20', 420, 0, 158, 8882.09, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-20', 120, 180, 44, 2422.79, '主井检修', '9:00:00', '12:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-20', 420, 0, 158, 8825.9, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-20', 300, 0, 118, 6481.88, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-21', 420, 0, 161, 8889.33, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-21', 120, 180, 44, 2451.75, '主井检修', '9:00:00', '12:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-21', 420, 0, 157, 8695.7, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-21', 300, 0, 114, 6282.57, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-22', 420, 0, 161, 9141.76, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-22', 120, 225, 48, 2651.43, '主井检修', '9:00:00', '12:45:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-22', 375, 0, 149, 8398.64, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-22', 60, 98, 19, 1091.82, '井下待料', '20:00:00', '21:38:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-22', 142, 0, 54, 3037.73, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-23', 420, 0, 162, 9264.72, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-23', 90, 210, 31, 1765.99, '主井检修', '8:30:00', '12:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-23', 140, 160, 52, 2872.34, '主井液压泵故障', '14:20:00', '17:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-23', 120, 0, 52, 2888.38, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-23', 300, 0, 120, 6696.09, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-24', 420, 0, 166, 9408.22, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-24', 200, 190, 69, 3888.21, '主井检修', '10:20:00', '13:30:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-24', 93, 128, 23, 1302.69, '选厂堵料', '15:03:00', '17:11:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-24', 109, 0, 43, 2473.46, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-24', 300, 0, 118, 6522.82, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-25', 420, 0, 161, 9261.46, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-25', 360, 220, 141, 8021.86, '主井检修', '13:00:00', '16:40:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-25', 140, 0, 51, 2856.23, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-25', 300, 0, 114, 6602.76, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-26', 330, 90, 123, 7115.15, '选厂堵料', '5:30:00', '7:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-26', 382, 38, 140, 7838.29, '选厂堵料', '7:00:00', '7:38:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-26', 120, 180, 45, 2516.91, '主井检修', '14:00:00', '17:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-26', 300, 0, 121, 6917.02, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-27', 215, 175, 99, 5446.05, '待料', '00:0:00', '2:55:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-27', 0, 30, 0, 0.0, '待料', '6:30:00', '7:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-27', 99, 60, 33, 1839.33, '井下待料', '7:00:00', '8:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-27', 91, 180, 35, 2021.63, '主井检修', '8:00:00', '11:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-27', 73, 150, 26, 1512.93, '井下待料', '11:00:00', '13:30:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-27', 0, 60, 0, 0.0, '井下待料', '15:09:00', '16:09:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-27', 0, 7, 0, 0.0, '选厂堵料', '17:40:00', '17:47:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-27', 17, 87, 4, 229.53, '井下待料', '19:17:00', '20:50:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-27', 30, 68, 13, 704.9, '选厂更换s1电源', '21:20:00', '22:28:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-27', 92, 0, 38, 2097.79, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-06-28', 420, 0, 154, 9087.58, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-28', 360, 180, 139, 7678.03, '主井检修', '13:00:00', '16:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-06-28', 180, 0, 55, 3077.4, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-06-28', 300, 0, 125, 7225.36, NULL, NULL, NULL, 'admin', 'admin');