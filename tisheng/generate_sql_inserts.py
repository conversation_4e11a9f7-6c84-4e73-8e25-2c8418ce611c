#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
生成SQL插入语句脚本
从CSV文件生成PostgreSQL数据库的INSERT语句
"""

import pandas as pd
import re
from datetime import datetime
import os
import argparse
from decimal import Decimal, getcontext

# 设置decimal精度
getcontext().prec = 10  # 设置精度为10位

def get_shift_id(shift_name):
    """根据班次名称获取对应的工作时段ID"""
    if not isinstance(shift_name, str):
        return None
    
    if '0-8' in shift_name:
        return 1  # 早班
    elif '8-20' in shift_name:
        return 2  # 白班
    elif '20-0' in shift_name:
        return 3  # 夜班
    return None

def parse_date(date_str):
    """解析中文日期格式，转换为标准日期格式"""
    if not isinstance(date_str, str):
        return None
    
    try:
        # 提取月和日
        month_match = re.search(r'(\d+)月', date_str)
        day_match = re.search(r'(\d+)日', date_str)
        
        if month_match and day_match:
            month = int(month_match.group(1))
            day = int(day_match.group(1))
            # 假设年份是2025年
            return f"2025-{month:02d}-{day:02d}"
    except Exception as e:
        print(f"日期解析错误: {date_str}, 错误: {e}")
    
    return None

def parse_fault_info(fault_info):
    """解析故障信息，返回故障原因、开始时间和结束时间"""
    if not isinstance(fault_info, str) or not fault_info:
        return None, None, None
    
    # 尝试匹配时间范围模式（如 "5:20-7:00停产检修")
    time_range_pattern = re.compile(r'(\d+[\:|：]\d+)\s*[-|\u2014]\s*(\d+[\:|：]\d+)')
    match = time_range_pattern.search(fault_info)
    
    start_time = None
    end_time = None
    reason = fault_info
    
    if match:
        time_range = match.group(0)
        start_time_str = match.group(1)
        end_time_str = match.group(2)
        
        # 将中文冒号替换为英文冒号
        start_time_str = start_time_str.replace('：', ':')
        end_time_str = end_time_str.replace('：', ':')
        
        # 添加秒数并标准化格式
        if len(start_time_str.split(':')) == 1:
            start_time_str += ':00'
        if len(start_time_str.split(':')) == 2:
            start_time_str += ':00'
            
        if len(end_time_str.split(':')) == 1:
            end_time_str += ':00'
        if len(end_time_str.split(':')) == 2:
            end_time_str += ':00'
        
        start_time = start_time_str
        end_time = end_time_str
        
        # 尝试提取故障原因（时间范围之后的文本）
        reason_parts = re.split(time_range_pattern, fault_info)
        reason = ''.join([p for p in reason_parts if p and not re.match(r'\d+[\:|：]\d+', p)]).strip()
    
    return reason, start_time, end_time

def safe_value(value, default=0, as_decimal=True):
    """安全获取值，处理NaN和None，并确保返回数字类型
    参数:
        value: 要转换的值
        default: 默认值，当转换失败时返回
        as_decimal: 是否返回Decimal类型，如果为False则返回float
    """
    if pd.isna(value) or value is None:
        return Decimal(default) if as_decimal else default
    
    # 尝试转换为数字
    try:
        # 如果是字符串，尝试转换
        if isinstance(value, str):
            # 移除可能的逗号、空格等
            value = value.replace(',', '').strip()
            if value == '':
                return Decimal(default) if as_decimal else default
            return Decimal(value) if as_decimal else float(value)
        # 如果已经是数字类型，转换为Decimal或直接返回
        elif isinstance(value, (int, float)):
            return Decimal(str(value)) if as_decimal else value
        # 其他情况，尝试强制转换
        else:
            return Decimal(str(value)) if as_decimal else float(value)
    except Exception as e:
        print(f"警告: 无法将值 '{value}' ({type(value)}) 转换为数字，使用默认值 {default}")
        return Decimal(default) if as_decimal else default

def generate_sql_inserts(csv_file, output_file=None, encoding='utf-8'):
    """从CSV文件生成SQL插入语句"""
    print(f"读取CSV文件: {csv_file}")
    
    try:
        # 读取CSV文件，不使用第一行作为列名
        df = pd.read_csv(csv_file, header=None, encoding=encoding)
        
        print("分析CSV文件结构...")
        
        # 定义列索引
        date_col = 0  # 第一列是日期
        shift_col = 1  # 第二列是班次
        operation_time_col = 2  # 第三列是运行时间
        fault_time_col = 3  # 第四列是故障处理时间
        buckets_col = 4  # 第五列是提升斗数
        weight_col = 5  # 第六列是提升量
        fault_reason_col = 6  # 第七列是故障处理原因
        
        # 初始化处理数据列表
        generate_sql_inserts.processed_data_list = []
        
        processed_rows = 0
        skipped_rows = 0
        
        # 用于向前填充班次名称
        current_date = None
        current_shift_name = None
        current_shift_id = None
        
        for index, row in df.iterrows():
            date_str = row[0]  # 日期在第一列
            shift_name = row[1]  # 班次在第二列
            
            # 处理日期
            if isinstance(date_str, str) and '月' in date_str and '日' in date_str:
                current_date = parse_date(date_str)
            
            # 处理班次 - 如果当前行有班次名称，则更新当前班次
            if isinstance(shift_name, str):
                # 跳过标题行
                if shift_name in ['主井提升统计表', '班次', '日合计']:
                    skipped_rows += 1
                    continue
                    
                # 更新当前班次
                current_shift_name = shift_name
                current_shift_id = get_shift_id(current_shift_name)
            
            # 如果没有有效的日期或班次，跳过该行
            if not current_date or not current_shift_id:
                skipped_rows += 1
                continue
            
            # 获取数据值，处理可能的NaN
            operation_time = safe_value(row[operation_time_col])
            fault_time = safe_value(row[fault_time_col])
            buckets = safe_value(row[buckets_col])
            weight = safe_value(row[weight_col])
            
            # 如果所有数据字段都为0或空，跳过该行
            if operation_time == 0 and fault_time == 0 and buckets == 0 and weight == 0 and pd.isna(row[fault_reason_col]):
                skipped_rows += 1
                continue
            
            # 解析故障信息
            fault_reason = None
            fault_start_time = None
            fault_end_time = None
            if not pd.isna(row[fault_reason_col]) and row[fault_reason_col]:
                fault_reason, fault_start_time, fault_end_time = parse_fault_info(row[fault_reason_col])
            
            # 直接保存每行数据，不进行聚合
            processed_data = {
                'date': current_date,
                'shift_id': current_shift_id,
                'operation_time': operation_time,
                'fault_time': fault_time,
                'buckets': buckets,
                'weight': weight,
                'fault_reason': fault_reason,
                'fault_start_time': fault_start_time,
                'fault_end_time': fault_end_time
            }
            
            # 添加到处理过的数据列表
            if not hasattr(generate_sql_inserts, 'processed_data_list'):
                generate_sql_inserts.processed_data_list = []
            generate_sql_inserts.processed_data_list.append(processed_data)
            
            processed_rows += 1
        
        # 生成SQL插入语句
        data_inserts = []
        
        # 生成数据插入语句 - 每行数据一条SQL语句
        for data in generate_sql_inserts.processed_data_list:
            # 将Decimal转换为整数或保留两位小数
            operation_time = int(data['operation_time'])
            fault_time = int(data['fault_time'])
            buckets = int(data['buckets'])
            weight = float(round(data['weight'], 2))  # 重量保留两位小数
            
            # 处理可能的NULL值
            fault_reason_sql = f"'{data['fault_reason']}'" if data['fault_reason'] else "NULL"
            start_time_sql = f"'{data['fault_start_time']}'" if data['fault_start_time'] else "NULL"
            end_time_sql = f"'{data['fault_end_time']}'" if data['fault_end_time'] else "NULL"
            
            # 生成插入语句 - 使用新的合并表结构，不包含id字段
            data_inserts.append(f"""
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
({data['shift_id']}, '{data['date']}', {operation_time}, {fault_time}, {buckets}, {weight}, {fault_reason_sql}, {start_time_sql}, {end_time_sql}, 'admin', 'admin');""".strip())
        
        # 组合所有SQL语句
        all_inserts = [
            "-- 插入提取数据到PostgreSQL数据库",
            f"-- 生成日期: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "-- 清空现有数据（如果需要）",
            "-- TRUNCATE TABLE data_mine_hoisting CASCADE;",
            "",
            "-- 插入提升和故障数据（使用新的合并表结构）",
            *data_inserts
        ]
        
        sql_content = '\n'.join(all_inserts)
        
        # 如果指定了输出文件，写入文件
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(sql_content)
            print(f"SQL插入语句已写入文件: {output_file}")
        else:
            print(sql_content)
        
        print(f"处理完成: 处理了 {processed_rows} 行数据，跳过了 {skipped_rows} 行")
        
    except Exception as e:
        print(f"生成SQL插入语句时出错: {e}")
        traceback.print_exc()

def main():
    parser = argparse.ArgumentParser(description='从CSV文件生成SQL插入语句')
    parser.add_argument('csv_file', help='CSV文件路径')
    parser.add_argument('-o', '--output', help='输出SQL文件路径')
    parser.add_argument('--encoding', default='utf-8', help='CSV文件编码，默认为utf-8')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.csv_file):
        print(f"错误: 找不到CSV文件 '{args.csv_file}'")
        return
    
    generate_sql_inserts(args.csv_file, args.output, args.encoding)

if __name__ == "__main__":
    main()
