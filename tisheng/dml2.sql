-- 插入提取数据到PostgreSQL数据库
-- 生成日期: 2025-07-18 21:28:49

-- 清空现有数据（如果需要）
-- TRUNCATE TABLE data_mine_hoisting CASCADE;

-- 插入提升和故障数据（使用新的合并表结构）
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-01-29', 0, 0, 0, 0.0, '主井停机', NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-01-29', 0, 0, 0, 0.0, '主井停机', NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-01-29', 0, 0, 0, 0.0, '主井停机', NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-01-31', 298, 130, 107, 5842.69, '选厂处理下料口', '7:00:00', '9:10:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-01-31', 308, 0, 109, 5904.46, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-01-31', 63, 67, 17, 923.34, '井下待料', '20:03:00', '21:10:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-01-31', 170, 0, 45, 2399.49, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-01', 86, 40, 22, 1183.07, '井下待料', '1:26:00', '2:06:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-01', 89, 42, 32, 1673.95, '井下待料', '3:35:00', '4:17:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-01', 146, 17, 53, 2833.39, '井下待料', '6:43:00', '7:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-01', 79, 65, 29, 1545.69, ',-1130堵料', '7:00:00', '8:05:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-01', 144, 62, 52, 2799.68, '井下待料', '9:24:00', '10:26:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-01', 190, 73, 69, 3740.36, '井下待料', '12:50:00', '14:03:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-01', 65, 42, 29, 1537.76, '井下待料', '17:13:00', '17:55:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-01', 25, 105, 8, 414.25, '井下待料', '19:25:00', '21:10:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-01', 33, 75, 10, 546.2, ',-1130堵料', '21:43:00', '22:58:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-01', 27, 35, 8, 422.33, ',-1130堵料', '23:25:00', '0:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-02', 209, 119, 75, 4116.3, ',-1130堵料', '3:29:00', '5:30:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-02', 90, 0, 36, 1974.87, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-02', 30, 240, 7, 382.4, '主井检修', '7:30:00', '12:30:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-02', 135, 7, 52, 2850.13, '选厂堵料', '14:45:00', '14:52:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-02', 112, 140, 38, 2037.62, '选厂堵料', '16:40:00', '19:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-02', 170, 30, 69, 3686.9, '选厂堵料', '19:00:00', '19:30:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-02', 30, 70, 9, 490.5, '选厂堵料', '22:20:00', '23:30:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-03', 359, 50, 137, 7373.78, '选厂堵料', '5:59:00', '6:49:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-03', 11, 0, 4, 215.7, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-03', 331, 129, 115, 6292.56, '井下待料', '12:31:00', '14:40:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-03', 139, 50, 54, 2950.74, '井下待料', '16:59:00', '17:49:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-03', 38, 43, 14, 773.11, '选厂处理下料口', '17:39:00', '18:22:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-03', 47, 193, 15, 822.13, '井下待料', '19:47:00', '22:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-03', 120, 0, 46, 2505.45, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-04', 20, 40, 7, 338.19, '井下待料', '0:20:00', '1:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-04', 91, 23, 34, 1836.48, '井下待料', '2:31:00', '2:54:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-04', 120, 33, 47, 2537.67, '井下待料', '4:55:00', '5:28:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-04', 59, 43, 20, 1070.35, '井下待料', '6:17:00', '7:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-04', 10, 50, 2, 100.0, '井下待料', '7:10:00', '8:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-04', 225, 175, 82, 4347.86, '主井检修', '8:00:00', '10:55:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-04', 203, 57, 74, 3865.17, '井下待料', '14:40:00', '15:37:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-04', 180, 120, 61, 3335.68, '井下待料', '19:00:00', '21:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-05', 20, 28, 6, 280.43, '井下待料', '0:20:00', '0:48:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-05', 94, 103, 39, 2123.12, '井下待料', '2:22:00', '4:05:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-05', 97, 88, 30, 1550.68, '井下待料', '5:32:00', '7:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-05', 140, 580, 57, 2972.31, '主井检修', '7:00:00', '16:40:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-05', 300, 0, 99, 5402.88, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-06', 78, 0, 32, 1725.73, '选厂维修0号皮带，粗料仓满', '1:18:00', '2:17:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-06', 43, 0, 17, 894.32, '选厂维修0号皮带，粗料仓满', '3:00:00', '7:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-06', 152, 748, 60, 3296.55, '选厂维修0号皮带粗矿仓满', '7:00:00', '16:28:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-06', 90, 30, 29, 1603.25, '选厂修补皮带', '20:30:00', '21:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-06', 125, 55, 49, 2717.59, '选厂堵料', '23:05:00', '0:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-07', 39, 71, 18, 1085.61, '选厂堵料', '0:00:00', '1:11:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-07', 65, 245, 26, 1422.7, '选厂修补皮带', '1:50:00', '5:55:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-07', 60, 540, 25, 1312.58, '选厂维修0号皮带粗矿仓满', '8:00:00', '17:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-07', 19, 18, 9, 489.59, '选厂堵料', '17:19:00', '17:37:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-07', 83, 0, 33, 1757.65, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-07', 243, 57, 75, 4153.42, '选厂维修0号皮带粗矿仓满', '23:03:00', '0:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-08', 0, 420, 0, 0.0, '选厂维修0号皮带粗矿仓满', '0:00:00', '7:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-08', 60, 660, 18, 903.32, '选厂维修0号皮带粗矿仓满', '8:00:00', '19:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-08', 0, 300, 0, 0.0, '选厂粗矿仓满', '19:00:00', '24:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-09', 408, 12, 156, 8290.29, '选厂粗矿仓满，待放料', '0:00:00', '0:12:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-09', 20, 35, 5, 277.04, '选厂堵料', '7:20:00', '7:55:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-09', 93, 11, 33, 1834.87, '选厂堵料', '9:27:00', '9:38:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-09', 164, 442, 54, 2975.81, '井下电机车车厢掉道', '12:22:00', '19:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-09', 180, 120, 56, 3093.9, '井下电机车车厢掉道', '19:00:00', '21:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-10', 120, 120, 43, 2274.78, '井下待料', '0:00:00', '2:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-10', 120, 60, 40, 2160.78, '主井调试卸载开关', '4:00:00', '5:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-10', 320, 62, 120, 6469.86, '井下待料', '12:20:00', '13:22:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-10', 338, 0, 131, 7109.51, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-10', 103, 80, 40, 2148.94, '井下待料', '19:00:00', '20:20:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-10', 60, 32, 22, 1212.54, '井下待料', '22:03:00', '22:35:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-10', 0, 25, 0, 0.0, '井下断电信号系统急停', '23:35:00', '0:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-11', 0, 420, 0, 0.0, '副井井口电缆损坏', '0:00:00', '7:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-11', 50, 180, 5, 280.79, '副井井口电缆损坏', '7:00:00', '11:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-11', 27, 13, 9, 497.08, '井下待料', '11:50:00', '12:03:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-11', 265, 60, 90, 4880.28, '选厂堵料', '12:30:00', '13:30:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-11', 0, 65, 0, 0.0, '选厂粗矿仓满', '17:55:00', '19:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-11', 0, 300, 0, 0.0, '选厂粗矿仓满', '19:00:00', '24:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-12', 77, 114, 30, 1620.42, '选厂粗矿仓满', '0:00:00', '1:54:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-12', 0, 209, 0, 0.0, '选厂粗矿仓满', '3:11:00', '7:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-12', 0, 720, 2, 108.82, '选厂粗矿仓满', '7:00:00', '19:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-12', 0, 300, 0, 0.0, '选厂粗料仓满', '19:00:00', '0:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-13', 152, 268, 66, 3579.63, '选厂粗料仓满', '0:00:00', '4:28:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-13', 160, 131, 57, 3076.64, '选厂粗料仓满', '9:40:00', '11:51:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-13', 429, 0, 154, 8342.72, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-13', 300, 0, 109, 6029.39, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-14', 61, 179, 18, 964.73, '选厂堵料', '1:01:00', '4:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-14', 180, 0, 71, 3768.8, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-14', 418, 180, 137, 7125.39, '井下待料', '13:58:00', '16:58:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-14', 122, 0, 46, 2467.75, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-14', 60, 20, 12, 634.89, '选厂堵料', '20:00:00', '20:20:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-14', 10, 60, 6, 322.68, '选厂堵料', '20:30:00', '22:30:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-14', 90, 0, 43, 2239.59, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-15', 85, 15, 26, 1397.63, '选厂堵料', '1:25:00', '1:40:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-15', 165, 125, 41, 2307.72, '选厂皮带损坏', '4:25:00', '6:20:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-15', 0, 40, 0, 0.0, '选厂堵料', '6:20:00', '7:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-15', 333, 15, 104, 5619.43, '选厂堵料', '7:00:00', '7:15:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-15', 245, 40, 88, 4739.67, '选厂堵料', '12:48:00', '13:28:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-15', 11, 76, 4, 218.96, '选厂维修皮带', '17:33:00', '18:49:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-15', 255, 45, 83, 4480.83, '井下待料', '23:15:00', '0:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-16', 232, 28, 76, 4082.96, '井下待料', '0:00:00', '0:28:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-16', 67, 93, 43, 2267.27, '井下待料', '4:20:00', '5:53:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-16', 84, 227, 24, 1279.2, '主井检修', '8:24:00', '12:11:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-16', 158, 191, 68, 3686.43, '维修主井励磁变电缆头击穿', '14:49:00', '18:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-16', 60, 0, 22, 1221.79, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-16', 300, 0, 92, 5069.97, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-17', 420, 0, 142, 7581.95, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-17', 104, 236, 32, 1719.38, '主井检修', '8:44:00', '12:40:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-17', 380, 0, 145, 7721.61, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-17', 300, 0, 100, 5370.38, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-18', 265, 39, 93, 4990.66, '井下待料', '4:25:00', '5:04:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-18', 116, 0, 50, 2696.63, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-18', 25, 195, 8, 379.11, '主井检修', '7:25:00', '10:40:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-18', 275, 25, 97, 5237.02, '井下待料', '7:00:00', '7:25:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-18', 133, 57, 42, 2241.31, '井下待料', '15:15:00', '16:12:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-18', 13, 22, 3, 162.05, '选厂处理下料口', '18:25:00', '18:47:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-18', 50, 40, 11, 595.34, '井下待料', '19:50:00', '20:30:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-18', 100, 15, 37, 1993.81, '井下待料', '22:10:00', '22:25:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-18', 95, 0, 32, 1697.94, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-19', 276, 67, 99, 5286.24, '井下待料', '4:36:00', '5:43:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-19', 75, 0, 36, 1904.03, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-19', 120, 300, 41, 2168.55, '主井检修', '9:00:00', '14:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-19', 300, 0, 121, 6413.68, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-19', 300, 0, 108, 5814.98, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-20', 420, 0, 135, 7247.13, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-20', 110, 180, 38, 2023.37, '主井检修', '8:50:00', '11:50:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-20', 43, 52, 14, 744.18, '选厂下料口卡铁板', '12:33:00', '13:25:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-20', 335, 0, 118, 6282.94, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-20', 300, 0, 86, 4582.36, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-21', 107, 63, 34, 1833.26, '井下待料', '1:47:00', '2:50:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-21', 250, 0, 94, 4946.2, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-21', 23, 37, 8, 386.6, '井下待料', '7:23:00', '8:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-21', 480, 180, 172, 9292.44, '主井检修', '8:00:00', '11:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-21', 191, 29, 65, 3487.74, '选厂清理下料口', '22:11:00', '22:40:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-21', 80, 0, 29, 1633.04, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-22', 280, 38, 100, 5351.72, '井下待料', '4:40:00', '5:18:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-22', 75, 27, 35, 1852.6, '井下待料', '6:33:00', '7:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-22', 94, 48, 27, 1414.11, '井下待料', '7:00:00', '7:48:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-22', 420, 158, 158, 8389.5, '主井检修', '9:22:00', '12:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-22', 48, 80, 12, 625.29, '井下待料', '19:48:00', '21:08:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-22', 172, 0, 52, 2812.9, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-23', 17, 39, 6, 300.13, '井下待料', '0:17:00', '0:56:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-23', 111, 63, 38, 2047.74, '井下待料', '2:47:00', '3:50:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-23', 75, 45, 28, 1492.5, '井下待料', '5:05:00', '5:50:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-23', 70, 0, 24, 1300.89, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-23', 140, 170, 47, 2527.61, '主井检修', '9:20:00', '12:10:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-23', 110, 15, 40, 2089.73, '选厂堵料', '14:00:00', '14:15:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-23', 285, 0, 113, 5819.54, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-23', 45, 35, 15, 741.94, '井下待料', '19:45:00', '20:20:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-23', 202, 18, 73, 3909.96, '，-1130m2#放矿机堵料', '23:42:00', '0:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-23', 0, 0, 0, 29850.33, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-24', 385, 35, 145, 7717.8, '，-1130m2#放矿机堵料', '0:00:00', '0:35:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-24', 430, 60, 153, 8433.99, '井下待料', '7:00:00', '8:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-24', 0, 230, 0, 0.0, '主井检修', '8:00:00', '11:50:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-24', 280, 20, 98, 5270.76, '选厂堵料', '23:40:00', '0:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-25', 360, 60, 124, 6754.32, '选厂堵料', '0:00:00', '1:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-25', 120, 30, 37, 1991.16, '选厂堵料', '9:00:00', '9:30:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-25', 364, 150, 143, 7590.21, '主井检修', '9:30:00', '12:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-25', 0, 30, 0, 0.0, '选厂堵料', '12:00:00', '12:30:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-25', 0, 26, 0, 0.0, '选厂堵料', '18:34:00', '19:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-25', 300, 0, 100, 5512.01, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-26', 420, 0, 143, 7685.42, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-26', 173, 100, 61, 3344.5, '主井检修', '9:53:00', '11:33:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-26', 447, 0, 181, 9933.11, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-26', 17, 0, 4, 221.24, '选厂清理下料口', '19:17:00', '19:35:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-26', 101, 0, 40, 2106.35, '，-1130m检修动锥', '21:16:00', '24:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-26', 0, 0, 0, 12680.7, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-27', 0, 420, 0, 0.0, '，-1130检修动锥', '0:00:00', '7:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-27', 0, 420, 0, 0.0, '，-1130检修动锥', '7:00:00', '19:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-27', 0, 300, 0, 0.0, '-1130检修动锥', '19:00:00', '0:00:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(1, '2025-02-28', 297, 117, 120, 6099.75, '井下维修1130动锥', '0:00:00', '1:57:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-28', 60, 109, 24, 1262.8, '，-1130m1号放矿机堵料', '8:00:00', '9:49:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-28', 488, 10, 167, 8855.88, '，-1277响炮暂停提料', '17:57:00', '18:07:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(2, '2025-02-28', 53, 0, 21, 1154.39, NULL, NULL, NULL, 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-28', 10, 50, 4, 204.31, '井下待料', '19:10:00', '20:20:00', 'admin', 'admin');
INSERT INTO data_mine_hoisting 
(working_period_id, operation_date, operation_time, fault_time, buckets, weight, fault_reason, fault_start_time, fault_end_time, create_by, update_by)
VALUES
(3, '2025-02-28', 220, 0, 76, 4182.35, NULL, NULL, NULL, 'admin', 'admin');