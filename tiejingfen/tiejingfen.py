import pandas as pd

# 定义文件路径列表
file_paths = [
    '/Users/<USER>/Documents/moonlighting/lxbi-data/选框/选矿厂调度室报表（1月份）.xlsx',
    '/Users/<USER>/Documents/moonlighting/lxbi-data/选框/选矿厂调度室报表（2月份）.xlsx',
    '/Users/<USER>/Documents/moonlighting/lxbi-data/选框/选矿厂给公司调度报表（6月）.xlsx',
    '/Users/<USER>/Documents/moonlighting/lxbi-data/选框/选矿厂给公司调度室报表（3月份）.xlsx',
    '/Users/<USER>/Documents/moonlighting/lxbi-data/选框/选矿厂给公司调度室报表（4月份）.xlsx',
    '/Users/<USER>/Documents/moonlighting/lxbi-data/选框/选矿厂给公司调度室报表（5月）.xlsx',
    '/Users/<USER>/Documents/moonlighting/lxbi-data/选框/选矿厂早会报表（7月）.xlsx'
]

# 初始化一个空字典来存储结果
all_results = {}

# 遍历每个文件路径
for file_path in file_paths:
    # 读取 Excel 文件
    excel_file = pd.ExcelFile(file_path)

    # 获取所有表名
    sheet_names = excel_file.sheet_names

    # 遍历每个工作表
    for sheet_name in sheet_names:
        df = excel_file.parse(sheet_name)

        # 提取 C8、C9、C10、C11 单元格内容（假设列索引从 0 开始，C 列索引为 2）
        cells_data = {}
        for row in [7, 8, 9, 10]:
            if row < df.shape[0] and 2 < df.shape[1]:
                cell_value = df.iloc[row, 2]
            else:
                cell_value = None
            cells_data[f'C{row + 1}'] = cell_value

        # 将当前工作表的结果添加到总结果字典中
        all_results.setdefault(file_path, {})[sheet_name] = cells_data

# 输出汇总结果
print(all_results)
