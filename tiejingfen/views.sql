-- 铁精粉生产数据统计视图

-- 按日统计
CREATE OR REPLACE VIEW vdata_iron_concentrate_daily_stats AS
SELECT 
    operation_date,
    COUNT(*) as record_count,
    SUM(production_volume) as total_production_volume,
    AVG(tfe_content) as avg_tfe_content,
    AVG(fineness_minus_500) as avg_fineness_minus_500,
    AVG(moisture_content) as avg_moisture_content
FROM data_iron_concentrate
GROUP BY operation_date
ORDER BY operation_date;

-- 按周统计
CREATE OR REPLACE VIEW vdata_iron_concentrate_weekly_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number,
    wk.week_start_date,
    wk.week_end_date,
    COUNT(*) as record_count,
    SUM(production_volume) as total_production_volume,
    AVG(tfe_content) as avg_tfe_content,
    AVG(fineness_minus_500) as avg_fineness_minus_500,
    AVG(moisture_content) as avg_moisture_content
FROM data_iron_concentrate
CROSS JOIN LATERAL get_week_thu_to_wed(operation_date) AS wk
GROUP BY wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date
ORDER BY year, week_number;

-- 按月统计
CREATE OR REPLACE VIEW vdata_iron_concentrate_monthly_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    COUNT(*) as record_count,
    SUM(production_volume) as total_production_volume,
    AVG(tfe_content) as avg_tfe_content,
    AVG(fineness_minus_500) as avg_fineness_minus_500,
    AVG(moisture_content) as avg_moisture_content
FROM data_iron_concentrate
CROSS JOIN LATERAL get_financial_month(operation_date) AS fm
GROUP BY fm.financial_year, fm.financial_month
ORDER BY year, month;

-- 按年统计
CREATE OR REPLACE VIEW vdata_iron_concentrate_yearly_stats AS
SELECT 
    fy.financial_year AS year,
    COUNT(*) as record_count,
    SUM(production_volume) as total_production_volume,
    AVG(tfe_content) as avg_tfe_content,
    AVG(fineness_minus_500) as avg_fineness_minus_500,
    AVG(moisture_content) as avg_moisture_content
FROM data_iron_concentrate
CROSS JOIN LATERAL get_financial_year(operation_date) AS fy
GROUP BY fy.financial_year
ORDER BY year;
