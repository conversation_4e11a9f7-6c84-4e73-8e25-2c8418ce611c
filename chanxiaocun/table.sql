-- 产销存统计表
DROP TABLE IF EXISTS data_production_sales_inventory;

CREATE TABLE data_production_sales_inventory (
    id BIGSERIAL PRIMARY KEY,
    operation_date DATE NOT NULL,
    product_type VARCHAR(20) NOT NULL,
    production_volume DECIMAL(10,3),
    sales_volume DECIMAL(10,3),
    inventory_volume DECIMAL(10,3),
    remarks VARCHAR(255),
    create_by VA<PERSON><PERSON><PERSON>(64),
    create_time TIMESTAMP,
    update_by VARCHAR(64),
    update_time TIMESTAMP
);

-- 添加字段注释
COMMENT ON TABLE data_production_sales_inventory IS '产销存统计数据表';
COMMENT ON COLUMN data_production_sales_inventory.id IS '主键ID';
COMMENT ON COLUMN data_production_sales_inventory.operation_date IS '作业日期';
COMMENT ON COLUMN data_production_sales_inventory.product_type IS '产品类型';
COMMENT ON COLUMN data_production_sales_inventory.production_volume IS '产量(t)';
COMMENT ON COLUMN data_production_sales_inventory.sales_volume IS '销量(t)';
COMMENT ON COLUMN data_production_sales_inventory.inventory_volume IS '库存(t)';
COMMENT ON COLUMN data_production_sales_inventory.remarks IS '备注';
COMMENT ON COLUMN data_production_sales_inventory.create_by IS '创建人';
COMMENT ON COLUMN data_production_sales_inventory.create_time IS '创建时间';
COMMENT ON COLUMN data_production_sales_inventory.update_by IS '更新人';
COMMENT ON COLUMN data_production_sales_inventory.update_time IS '更新时间';

-- 添加唯一约束（支持填报时的UPSERT操作）
ALTER TABLE data_production_sales_inventory 
ADD CONSTRAINT uk_date_product UNIQUE (operation_date, product_type);

-- 添加索引
CREATE INDEX idx_production_sales_inventory_date ON data_production_sales_inventory(operation_date);
CREATE INDEX idx_production_sales_inventory_product ON data_production_sales_inventory(product_type);

-- 示例数据插入语句（基于图示数据）
INSERT INTO data_production_sales_inventory (operation_date, product_type, production_volume, sales_volume, inventory_volume) VALUES
('2024-06-29', '铁精粉', NULL, NULL, NULL),
('2024-06-29', '铁矿石', NULL, NULL, NULL),
('2024-06-29', '配矿', NULL, NULL, NULL),
('2024-06-30', '铁精粉', NULL, NULL, NULL),
('2024-06-30', '铁矿石', NULL, NULL, NULL),
('2024-06-30', '配矿', NULL, NULL, NULL),
('2024-07-01', '铁精粉', NULL, NULL, NULL),
('2024-07-01', '铁矿石', NULL, NULL, NULL),
('2024-07-01', '配矿', NULL, NULL, NULL),
('2024-07-02', '铁精粉', NULL, NULL, NULL),
('2024-07-02', '铁矿石', NULL, NULL, NULL),
('2024-07-02', '配矿', NULL, NULL, NULL),
('2024-07-03', '铁精粉', NULL, NULL, NULL),
('2024-07-03', '铁矿石', NULL, NULL, NULL),
('2024-07-03', '配矿', NULL, NULL, NULL),
('2024-07-04', '铁精粉', NULL, NULL, NULL),
('2024-07-04', '铁矿石', NULL, NULL, NULL),
('2024-07-04', '配矿', NULL, NULL, NULL),
('2024-07-05', '铁精粉', NULL, NULL, NULL),
('2024-07-05', '铁矿石', NULL, NULL, NULL),
('2024-07-05', '配矿', NULL, NULL, NULL),
('2024-07-06', '铁精粉', NULL, NULL, NULL),
('2024-07-06', '铁矿石', NULL, NULL, NULL),
('2024-07-06', '配矿', NULL, NULL, NULL),
('2024-07-07', '铁精粉', NULL, NULL, NULL),
('2024-07-07', '铁矿石', NULL, NULL, NULL),
('2024-07-07', '配矿', NULL, NULL, NULL);
