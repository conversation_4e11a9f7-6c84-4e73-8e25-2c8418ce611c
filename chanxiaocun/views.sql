-- 产销存统计视图

-- 按日总体统计
CREATE OR REPLACE VIEW vdata_production_sales_inventory_daily_total_stats AS
SELECT 
    operation_date,
    COUNT(*) as record_count,
    SUM(production_volume) as total_production_volume,
    SUM(sales_volume) as total_sales_volume,
    SUM(inventory_volume) as total_inventory_volume
FROM data_production_sales_inventory
GROUP BY operation_date
ORDER BY operation_date;

-- 按日详细统计
CREATE OR REPLACE VIEW vdata_production_sales_inventory_daily_stats AS
SELECT 
    operation_date,
    product_type,
    COUNT(*) as record_count,
    SUM(production_volume) as total_production_volume,
    SUM(sales_volume) as total_sales_volume,
    SUM(inventory_volume) as total_inventory_volume,
    AVG(production_volume) as avg_production_volume,
    AVG(sales_volume) as avg_sales_volume,
    AVG(inventory_volume) as avg_inventory_volume
FROM data_production_sales_inventory
GROUP BY operation_date, product_type
ORDER BY operation_date, product_type;

-- 按日按产品类型统计
CREATE OR REPLACE VIEW vdata_production_sales_inventory_daily_product_stats AS
SELECT 
    operation_date,
    product_type,
    SUM(production_volume) as total_production_volume,
    SUM(sales_volume) as total_sales_volume,
    SUM(inventory_volume) as total_inventory_volume
FROM data_production_sales_inventory
GROUP BY operation_date, product_type
ORDER BY operation_date, product_type;

-- 按周总体统计
CREATE OR REPLACE VIEW vdata_production_sales_inventory_weekly_total_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number,
    wk.week_start_date,
    wk.week_end_date,
    COUNT(*) as record_count,
    SUM(production_volume) as total_production_volume,
    SUM(sales_volume) as total_sales_volume,
    SUM(inventory_volume) as total_inventory_volume
FROM data_production_sales_inventory
CROSS JOIN LATERAL get_week_thu_to_wed(operation_date) AS wk
GROUP BY wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date
ORDER BY year, week_number;

-- 按周详细统计
CREATE OR REPLACE VIEW vdata_production_sales_inventory_weekly_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number,
    wk.week_start_date,
    wk.week_end_date,
    product_type,
    COUNT(*) as record_count,
    SUM(production_volume) as total_production_volume,
    SUM(sales_volume) as total_sales_volume,
    SUM(inventory_volume) as total_inventory_volume,
    AVG(production_volume) as avg_production_volume,
    AVG(sales_volume) as avg_sales_volume,
    AVG(inventory_volume) as avg_inventory_volume
FROM data_production_sales_inventory
CROSS JOIN LATERAL get_week_thu_to_wed(operation_date) AS wk
GROUP BY wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date, product_type
ORDER BY year, week_number, product_type;

-- 按周按产品类型统计
CREATE OR REPLACE VIEW vdata_production_sales_inventory_weekly_product_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number,
    wk.week_start_date,
    wk.week_end_date,
    product_type,
    SUM(production_volume) as total_production_volume,
    SUM(sales_volume) as total_sales_volume,
    SUM(inventory_volume) as total_inventory_volume
FROM data_production_sales_inventory
CROSS JOIN LATERAL get_week_thu_to_wed(operation_date) AS wk
GROUP BY wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date, product_type
ORDER BY year, week_number, product_type;

-- 按月总体统计
CREATE OR REPLACE VIEW vdata_production_sales_inventory_monthly_total_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    COUNT(*) as record_count,
    SUM(production_volume) as total_production_volume,
    SUM(sales_volume) as total_sales_volume,
    SUM(inventory_volume) as total_inventory_volume
FROM data_production_sales_inventory
CROSS JOIN LATERAL get_financial_month(operation_date) AS fm
GROUP BY fm.financial_year, fm.financial_month
ORDER BY year, month;

-- 按月详细统计
CREATE OR REPLACE VIEW vdata_production_sales_inventory_monthly_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    product_type,
    COUNT(*) as record_count,
    SUM(production_volume) as total_production_volume,
    SUM(sales_volume) as total_sales_volume,
    SUM(inventory_volume) as total_inventory_volume,
    AVG(production_volume) as avg_production_volume,
    AVG(sales_volume) as avg_sales_volume,
    AVG(inventory_volume) as avg_inventory_volume
FROM data_production_sales_inventory
CROSS JOIN LATERAL get_financial_month(operation_date) AS fm
GROUP BY fm.financial_year, fm.financial_month, product_type
ORDER BY year, month, product_type;

-- 按月按产品类型统计
CREATE OR REPLACE VIEW vdata_production_sales_inventory_monthly_product_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    product_type,
    SUM(production_volume) as total_production_volume,
    SUM(sales_volume) as total_sales_volume,
    SUM(inventory_volume) as total_inventory_volume
FROM data_production_sales_inventory
CROSS JOIN LATERAL get_financial_month(operation_date) AS fm
GROUP BY fm.financial_year, fm.financial_month, product_type
ORDER BY year, month, product_type;

-- 按年总体统计
CREATE OR REPLACE VIEW vdata_production_sales_inventory_yearly_total_stats AS
SELECT 
    fy.financial_year AS year,
    COUNT(*) as record_count,
    SUM(production_volume) as total_production_volume,
    SUM(sales_volume) as total_sales_volume,
    SUM(inventory_volume) as total_inventory_volume
FROM data_production_sales_inventory
CROSS JOIN LATERAL get_financial_year(operation_date) AS fy
GROUP BY fy.financial_year
ORDER BY year;

-- 按年详细统计
CREATE OR REPLACE VIEW vdata_production_sales_inventory_yearly_stats AS
SELECT 
    fy.financial_year AS year,
    product_type,
    COUNT(*) as record_count,
    SUM(production_volume) as total_production_volume,
    SUM(sales_volume) as total_sales_volume,
    SUM(inventory_volume) as total_inventory_volume,
    AVG(production_volume) as avg_production_volume,
    AVG(sales_volume) as avg_sales_volume,
    AVG(inventory_volume) as avg_inventory_volume
FROM data_production_sales_inventory
CROSS JOIN LATERAL get_financial_year(operation_date) AS fy
GROUP BY fy.financial_year, product_type
ORDER BY year, product_type;

-- 按年按产品类型统计
CREATE OR REPLACE VIEW vdata_production_sales_inventory_yearly_product_stats AS
SELECT 
    fy.financial_year AS year,
    product_type,
    SUM(production_volume) as total_production_volume,
    SUM(sales_volume) as total_sales_volume,
    SUM(inventory_volume) as total_inventory_volume
FROM data_production_sales_inventory
CROSS JOIN LATERAL get_financial_year(operation_date) AS fy
GROUP BY fy.financial_year, product_type
ORDER BY year, product_type;

-- 库存余额统计视图（显示最新库存状态）
CREATE OR REPLACE VIEW vdata_production_sales_inventory_current_balance AS
SELECT 
    product_type,
    MAX(operation_date) as latest_date,
    (SELECT inventory_volume 
     FROM data_production_sales_inventory psi2 
     WHERE psi2.product_type = psi1.product_type 
     AND psi2.operation_date = MAX(psi1.operation_date)
     LIMIT 1) as current_inventory
FROM data_production_sales_inventory psi1
WHERE inventory_volume IS NOT NULL
GROUP BY product_type
ORDER BY product_type;

-- 产销存趋势分析视图
CREATE OR REPLACE VIEW vdata_production_sales_inventory_trend_analysis AS
SELECT 
    operation_date,
    product_type,
    production_volume,
    sales_volume,
    inventory_volume,
    production_volume - sales_volume as net_production,
    CASE 
        WHEN production_volume > 0 THEN ROUND((sales_volume / production_volume) * 100, 2)
        ELSE NULL 
    END as sales_rate,
    LAG(inventory_volume) OVER (PARTITION BY product_type ORDER BY operation_date) as prev_inventory,
    inventory_volume - LAG(inventory_volume) OVER (PARTITION BY product_type ORDER BY operation_date) as inventory_change
FROM data_production_sales_inventory
WHERE production_volume IS NOT NULL OR sales_volume IS NOT NULL OR inventory_volume IS NOT NULL
ORDER BY product_type, operation_date;
