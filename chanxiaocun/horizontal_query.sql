-- 横排显示一日产销存数据的查询语句

-- 方案1：使用CASE WHEN实现行转列（推荐）
CREATE OR REPLACE VIEW vdata_production_sales_inventory_horizontal AS
SELECT 
    operation_date AS 日期,
    -- 铁精粉数据
    MAX(CASE WHEN product_type = '铁精粉' THEN production_volume END) AS 铁精粉_产量,
    MAX(CASE WHEN product_type = '铁精粉' THEN sales_volume END) AS 铁精粉_销量,
    MAX(CASE WHEN product_type = '铁精粉' THEN inventory_volume END) AS 铁精粉_库存,
    -- 铁矿石数据
    MAX(CASE WHEN product_type = '铁矿石' THEN production_volume END) AS 铁矿石_产量,
    MAX(CASE WHEN product_type = '铁矿石' THEN sales_volume END) AS 铁矿石_销量,
    MAX(CASE WHEN product_type = '铁矿石' THEN inventory_volume END) AS 铁矿石_库存,
    -- 配矿数据
    MAX(CASE WHEN product_type = '配矿' THEN production_volume END) AS 配矿_产量,
    MAX(CASE WHEN product_type = '配矿' THEN sales_volume END) AS 配矿_销量,
    MAX(CASE WHEN product_type = '配矿' THEN inventory_volume END) AS 配矿_库存
FROM data_production_sales_inventory
GROUP BY operation_date
ORDER BY operation_date;

-- 查询指定日期的横排数据
SELECT * FROM vdata_production_sales_inventory_horizontal 
WHERE 日期 = '2024-07-01';

-- 查询指定日期范围的横排数据
SELECT * FROM vdata_production_sales_inventory_horizontal 
WHERE 日期 BETWEEN '2024-06-29' AND '2024-07-07';

-- 方案2：填报用的UPSERT存储过程
CREATE OR REPLACE FUNCTION upsert_daily_production_sales_inventory(
    p_operation_date DATE,
    -- 铁精粉数据
    p_iron_concentrate_production DECIMAL(10,3) DEFAULT NULL,
    p_iron_concentrate_sales DECIMAL(10,3) DEFAULT NULL,
    p_iron_concentrate_inventory DECIMAL(10,3) DEFAULT NULL,
    -- 铁矿石数据
    p_iron_ore_production DECIMAL(10,3) DEFAULT NULL,
    p_iron_ore_sales DECIMAL(10,3) DEFAULT NULL,
    p_iron_ore_inventory DECIMAL(10,3) DEFAULT NULL,
    -- 配矿数据
    p_mixed_ore_production DECIMAL(10,3) DEFAULT NULL,
    p_mixed_ore_sales DECIMAL(10,3) DEFAULT NULL,
    p_mixed_ore_inventory DECIMAL(10,3) DEFAULT NULL,
    -- 操作人
    p_operator VARCHAR(64) DEFAULT 'system'
) RETURNS VOID AS $$
DECLARE
    current_time TIMESTAMP := NOW();
BEGIN
    -- 铁精粉数据
    INSERT INTO data_production_sales_inventory 
    (operation_date, product_type, production_volume, sales_volume, inventory_volume, create_by, create_time, update_by, update_time)
    VALUES (p_operation_date, '铁精粉', p_iron_concentrate_production, p_iron_concentrate_sales, p_iron_concentrate_inventory, p_operator, current_time, p_operator, current_time)
    ON CONFLICT (operation_date, product_type) 
    DO UPDATE SET 
        production_volume = EXCLUDED.production_volume,
        sales_volume = EXCLUDED.sales_volume,
        inventory_volume = EXCLUDED.inventory_volume,
        update_by = EXCLUDED.update_by,
        update_time = EXCLUDED.update_time;
    
    -- 铁矿石数据
    INSERT INTO data_production_sales_inventory 
    (operation_date, product_type, production_volume, sales_volume, inventory_volume, create_by, create_time, update_by, update_time)
    VALUES (p_operation_date, '铁矿石', p_iron_ore_production, p_iron_ore_sales, p_iron_ore_inventory, p_operator, current_time, p_operator, current_time)
    ON CONFLICT (operation_date, product_type) 
    DO UPDATE SET 
        production_volume = EXCLUDED.production_volume,
        sales_volume = EXCLUDED.sales_volume,
        inventory_volume = EXCLUDED.inventory_volume,
        update_by = EXCLUDED.update_by,
        update_time = EXCLUDED.update_time;
    
    -- 配矿数据
    INSERT INTO data_production_sales_inventory 
    (operation_date, product_type, production_volume, sales_volume, inventory_volume, create_by, create_time, update_by, update_time)
    VALUES (p_operation_date, '配矿', p_mixed_ore_production, p_mixed_ore_sales, p_mixed_ore_inventory, p_operator, current_time, p_operator, current_time)
    ON CONFLICT (operation_date, product_type) 
    DO UPDATE SET 
        production_volume = EXCLUDED.production_volume,
        sales_volume = EXCLUDED.sales_volume,
        inventory_volume = EXCLUDED.inventory_volume,
        update_by = EXCLUDED.update_by,
        update_time = EXCLUDED.update_time;
END;
$$ LANGUAGE plpgsql;

-- 方案3：批量填报的简化查询（用于前端表单）
-- 查询某日期的数据，如果不存在则返回空值，便于填报
SELECT 
    '2024-07-01'::DATE AS operation_date,
    COALESCE(MAX(CASE WHEN product_type = '铁精粉' THEN production_volume END), 0) AS iron_concentrate_production,
    COALESCE(MAX(CASE WHEN product_type = '铁精粉' THEN sales_volume END), 0) AS iron_concentrate_sales,
    COALESCE(MAX(CASE WHEN product_type = '铁精粉' THEN inventory_volume END), 0) AS iron_concentrate_inventory,
    COALESCE(MAX(CASE WHEN product_type = '铁矿石' THEN production_volume END), 0) AS iron_ore_production,
    COALESCE(MAX(CASE WHEN product_type = '铁矿石' THEN sales_volume END), 0) AS iron_ore_sales,
    COALESCE(MAX(CASE WHEN product_type = '铁矿石' THEN inventory_volume END), 0) AS iron_ore_inventory,
    COALESCE(MAX(CASE WHEN product_type = '配矿' THEN production_volume END), 0) AS mixed_ore_production,
    COALESCE(MAX(CASE WHEN product_type = '配矿' THEN sales_volume END), 0) AS mixed_ore_sales,
    COALESCE(MAX(CASE WHEN product_type = '配矿' THEN inventory_volume END), 0) AS mixed_ore_inventory
FROM data_production_sales_inventory
WHERE operation_date = '2024-07-01';

-- 示例：使用存储过程填报数据
SELECT upsert_daily_production_sales_inventory(
    '2024-07-01'::DATE,
    -- 铁精粉：产量, 销量, 库存
    100.5, 95.2, 200.0,
    -- 铁矿石：产量, 销量, 库存  
    150.0, 140.0, 300.0,
    -- 配矿：产量, 销量, 库存
    80.0, 75.0, 150.0,
    -- 操作人
    'admin'
);

-- 验证填报结果
SELECT * FROM vdata_production_sales_inventory_horizontal 
WHERE 日期 = '2024-07-01';
