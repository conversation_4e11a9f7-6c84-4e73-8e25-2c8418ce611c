import pandas as pd
import os
import argparse
from datetime import datetime

def extract_excel_dynamic_revised(file_path, start_row, end_row, start_col, end_col, output_file=None):
    """
    Extract data from Excel sheets with dynamic row matching:
    1. For the first sheet: Use the specified start_row and end_row
    2. For subsequent sheets: Find rows with matching values in column A
    
    Args:
        file_path (str): Path to the Excel file
        start_row (int): Starting row number for the first sheet (1-indexed as in Excel)
        end_row (int): Ending row number for the first sheet (1-indexed as in Excel)
        start_col (str): Starting column letter (A-Z, AA-ZZ)
        end_col (str): Ending column letter (A-Z, AA-ZZ)
        output_file (str, optional): Path to save the extracted data. If None, will create a default name.
    
    Returns:
        dict: Dictionary with sheet names as keys and extracted DataFrames as values
    """
    # If no output file specified, create a default name
    if output_file is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = os.path.join(os.path.dirname(file_path), f"提取数据_动态_{timestamp}.xlsx")
    
    # Convert column letters to 0-indexed integers
    def col_to_num(col_str):
        num = 0
        for c in col_str:
            num = num * 26 + (ord(c.upper()) - ord('A') + 1)
        return num - 1  # 0-indexed
    
    start_col_idx = col_to_num(start_col)
    end_col_idx = col_to_num(end_col)
    
    # Convert 1-indexed rows to 0-indexed for pandas
    start_row_idx = start_row - 1
    end_row_idx = end_row - 1
    
    print(f"Initial extraction range: rows {start_row}-{end_row}, columns {start_col}-{end_col}")
    print(f"This corresponds to rows {start_row_idx}-{end_row_idx} (0-indexed) and columns {start_col_idx}-{end_col_idx} (0-indexed)")
    
    # Read the Excel file
    excel_file = pd.ExcelFile(file_path)
    sheet_names = excel_file.sheet_names
    
    if not sheet_names:
        print("No sheets found in the Excel file.")
        return None
    
    # Create a dictionary to store data from each sheet
    all_data = {}
    
    # Process the first sheet using the specified row range
    first_sheet_name = sheet_names[0]
    print(f"Processing first sheet: '{first_sheet_name}' using specified range")
    
    # Read the first sheet with the specified range
    first_sheet_df = pd.read_excel(file_path, sheet_name=first_sheet_name, header=None)
    
    # Extract the full sheet to find markers
    first_sheet_df_full = pd.read_excel(file_path, sheet_name=first_sheet_name, header=None)
    
    # Check bounds and adjust if necessary
    max_row_idx = len(first_sheet_df_full) - 1
    max_col_idx = len(first_sheet_df_full.columns) - 1
    
    if end_row_idx > max_row_idx:
        print(f"Warning: end_row_idx {end_row_idx} exceeds available rows {max_row_idx}. Adjusting to {max_row_idx}")
        end_row_idx = max_row_idx
    
    if end_col_idx > max_col_idx:
        print(f"Warning: end_col_idx {end_col_idx} exceeds available columns {max_col_idx}. Adjusting to {max_col_idx}")
        end_col_idx = max_col_idx
    
    if start_row_idx > max_row_idx:
        print(f"Error: start_row_idx {start_row_idx} exceeds available rows {max_row_idx}")
        return None
    
    if start_col_idx > max_col_idx:
        print(f"Error: start_col_idx {start_col_idx} exceeds available columns {max_col_idx}")
        return None
    
    print(f"Adjusted extraction range: rows {start_row_idx}-{end_row_idx} (0-indexed) and columns {start_col_idx}-{end_col_idx} (0-indexed)")
    print(f"DataFrame shape: {first_sheet_df_full.shape}")
    
    # Extract the data from the specified range
    first_sheet_extracted = first_sheet_df.iloc[start_row_idx:end_row_idx+1, start_col_idx:end_col_idx+1]
    
    # Store the extracted data
    all_data = {first_sheet_name: first_sheet_extracted}
    
    # Get the start and end markers from the first sheet
    start_marker = first_sheet_df_full.iloc[start_row_idx, start_col_idx]
    end_marker = first_sheet_df_full.iloc[end_row_idx, start_col_idx]
    
    # Determine marker index by finding all end markers in the first sheet
    end_marker_positions = []
    for idx, value in enumerate(first_sheet_df_full.iloc[start_row_idx+1:, start_col_idx], start=start_row_idx+1):
        if pd.notna(value) and str(value) == str(end_marker):
            end_marker_positions.append(idx)
    
    # Calculate which marker was used in the first sheet (1st or 2nd)
    marker_index = 1  # Default to first marker
    if end_marker_positions and len(end_marker_positions) > 1:
        for i, pos in enumerate(end_marker_positions, start=1):
            if pos == end_row_idx:
                marker_index = i
                break
    
    print("Extracted marker values from first sheet:")
    print(f"  Start marker: '{start_marker}'")
    print(f"  End marker: '{end_marker}'")
    print(f"  Using marker #{marker_index} for all sheets")
    
    # Process all other sheets using the marker values
    for sheet_name in sheet_names[1:]:
        print(f"\nProcessing sheet: '{sheet_name}'")
        
        try:
            # Read the entire sheet (or a large enough portion)
            # We need to read more rows than expected to find the markers
            sheet_df_full = pd.read_excel(file_path, sheet_name=sheet_name, header=None, nrows=500)
            
            # Find the start marker in the specified start column
            start_col_idx = start_col_idx  # Using the start column index from parameters
            start_idx = None
            for idx, value in enumerate(sheet_df_full.iloc[:, start_col_idx]):
                if pd.notna(value) and str(value) == str(start_marker):
                    start_idx = idx
                    print(f"  Found start marker '{start_marker}' at row {idx + 1}, column {start_col_idx + 1}")
                    break
            
            if start_idx is None:
                print(f"  Warning: Could not find exact start marker '{start_marker}' in sheet '{sheet_name}' column {start_col_idx + 1}")
                # Try to find a similar value in the start column
                for idx, value in enumerate(sheet_df_full.iloc[:, start_col_idx]):
                    if pd.notna(value) and isinstance(value, (str, int, float)):
                        if isinstance(start_marker, str) and isinstance(value, str):
                            if start_marker.lower() in value.lower() or value.lower() in start_marker.lower():
                                start_idx = idx
                                print(f"  Using similar start marker '{value}' at row {idx + 1}, column {start_col_idx + 1}")
                                break
            
            if start_idx is None:
                print(f"  Error: Could not find any suitable start marker in sheet '{sheet_name}' column {start_col_idx + 1}")
                continue
            
            # Find ALL end markers after the start marker
            end_idxs = []
            for idx, value in enumerate(sheet_df_full.iloc[start_idx+1:, start_col_idx], start=start_idx+1):
                if pd.notna(value) and str(value) == str(end_marker):
                    end_idxs.append(idx)
                    print(f"  Found end marker '{end_marker}' at row {idx + 1}, column {start_col_idx + 1}")
            
            # If we found end markers, use the marker index determined from the first sheet
            if end_idxs:
                # Check if we have enough markers for the requested index
                if marker_index > len(end_idxs):
                    print(f"  Warning: Marker index {marker_index} from first sheet, but only found {len(end_idxs)} markers")
                    # Use the last available marker if requested index is too high
                    end_idx = end_idxs[-1]
                    print(f"  Using last available marker (#{len(end_idxs)}) at row {end_idx + 1}")
                else:
                    # Use the marker index from first sheet (1-based, so subtract 1 for 0-based indexing)
                    end_idx = end_idxs[marker_index - 1]
                    print(f"  Using marker #{marker_index} at row {end_idx + 1}")
            else:
                print(f"  Warning: Could not find any end marker in sheet '{sheet_name}' column {start_col_idx + 1}")
                # Calculate fixed interval based on the first sheet's data length
                first_sheet_length = end_row_idx - start_row_idx + 1
                end_idx = start_idx + first_sheet_length - 1
                print(f"  Using fixed interval of {first_sheet_length} rows: from row {start_idx + 1} to row {end_idx + 1}")
                
                # Make sure we don't exceed the available data
                max_available_row = len(sheet_df_full) - 1
                if end_idx > max_available_row:
                    end_idx = max_available_row
                    print(f"  Adjusted end row to {end_idx + 1} (last available row in sheet)")
            
            # Extract data between start_idx and end_idx (inclusive)
            sheet_df = sheet_df_full.iloc[start_idx:end_idx+1, start_col_idx:end_col_idx+1]
            
            all_data[sheet_name] = sheet_df
            
            print(f"  Successfully extracted data from row {start_idx + 1} to {end_idx + 1}")
        except Exception as e:
            print(f"  Error processing sheet {sheet_name}: {str(e)}")
    
    # Create a writer to save all data to separate sheets in the output Excel file
    with pd.ExcelWriter(output_file) as writer:
        # Create a summary sheet that combines all data
        summary_data = []
        
        for sheet_name, df in all_data.items():
            # Add the sheet name as a column to identify the data source
            df_copy = df.copy()
            df_copy.insert(0, '日期', sheet_name)
            summary_data.append(df_copy)
            
            # Also save each sheet's data to a separate sheet in the output file
            df.to_excel(writer, sheet_name=sheet_name, index=False, header=False)
        
        # Combine all data and save to a summary sheet
        if summary_data:
            combined_df = pd.concat(summary_data, ignore_index=True)
            combined_df.to_excel(writer, sheet_name='汇总数据', index=False)
            
            # Also save as CSV for easier data processing
            csv_output = output_file.replace('.xlsx', '.csv')
            combined_df.to_csv(csv_output, index=False, encoding='utf-8-sig')
    
    print(f"\nData extraction complete. Output saved to: {output_file}")
    return all_data

def main():
    parser = argparse.ArgumentParser(description='Extract data from Excel file with dynamic row matching.')
    parser.add_argument('file_path', help='Path to the Excel file')
    parser.add_argument('--start-row', type=int, required=True, help='Starting row number for first sheet (1-indexed as in Excel)')
    parser.add_argument('--end-row', type=int, required=True, help='Ending row number for first sheet (1-indexed as in Excel)')
    parser.add_argument('--start-col', required=True, help='Starting column letter (A-Z, AA-ZZ)')
    parser.add_argument('--end-col', required=True, help='Ending column letter (A-Z, AA-ZZ)')
    parser.add_argument('--output', help='Output file path (optional)')
    
    args = parser.parse_args()
    
    extract_excel_dynamic_revised(
        args.file_path,
        args.start_row,
        args.end_row,
        args.start_col,
        args.end_col,
        args.output
    )

if __name__ == "__main__":
    import sys
    if len(sys.argv) == 1:
        print("Example usage:")
        print("python extract_excel_dynamic_revised.py c:\\path\\to\\file.xlsx --start-row 218 --end-row 232 --start-col A --end-col I")
        print("\nFor interactive mode, enter the parameters below:")
        
        file_path = input("Excel file path: ")
        start_row = int(input("Start row for first sheet (e.g., 218): "))
        end_row = int(input("End row for first sheet (e.g., 232): "))
        start_col = input("Start column (e.g., A): ")
        end_col = input("End column (e.g., I): ")
        output = input("Output file path (leave blank for default): ") or None
        
        extract_excel_dynamic_revised(file_path, start_row, end_row, start_col, end_col, output)
    else:
        main()
