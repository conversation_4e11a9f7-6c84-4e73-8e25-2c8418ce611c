-- 按日总体统计
CREATE OR REPLACE VIEW vdata_ball_mill_daily_total_stats AS
SELECT 
    report_date,
    COUNT(*) AS total_records,
    SUM(daily_ore_tonnage) AS total_daily_ore_tonnage,
    SUM(operating_time) AS total_operating_time,
    CASE WHEN SUM(operating_time) > 0 THEN ROUND(SUM(daily_ore_tonnage) / SUM(operating_time), 4) ELSE NULL END AS avg_unit_efficiency_tph,
    CASE WHEN (24.0 * COUNT(DISTINCT report_date)) > 0 THEN ROUND(SUM(operating_time) / (24.0 * COUNT(DISTINCT report_date)) * 100, 2) ELSE NULL END AS avg_equipment_utilization,
    AVG(raw_ore_total_iron_grade) AS avg_raw_ore_total_iron_grade,
    AVG(raw_ore_magnetic_iron_grade) AS avg_raw_ore_magnetic_iron_grade,
    AVG(first_stage_cyclone_overflow_200_mesh) AS avg_first_stage_cyclone_overflow_200_mesh,
    AVG(second_stage_cyclone_overflow_500_mesh) AS avg_second_stage_cyclone_overflow_500_mesh
FROM data_ball_mill
GROUP BY report_date
ORDER BY report_date;

-- 按周总体统计（财务周：周四-周三）
CREATE OR REPLACE VIEW vdata_ball_mill_weekly_total_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number,
    wk.week_start_date,
    wk.week_end_date,
    COUNT(*) AS total_records,
    SUM(daily_ore_tonnage) AS total_daily_ore_tonnage,
    SUM(operating_time) AS total_operating_time,
    CASE WHEN SUM(operating_time) > 0 THEN ROUND(SUM(daily_ore_tonnage) / SUM(operating_time), 4) ELSE NULL END AS avg_unit_efficiency_tph,
    CASE WHEN (24.0 * COUNT(DISTINCT bm.report_date)) > 0 THEN ROUND(SUM(operating_time) / (24.0 * COUNT(DISTINCT bm.report_date)) * 100, 2) ELSE NULL END AS avg_equipment_utilization,
    AVG(raw_ore_total_iron_grade) AS avg_raw_ore_total_iron_grade,
    AVG(raw_ore_magnetic_iron_grade) AS avg_raw_ore_magnetic_iron_grade,
    AVG(first_stage_cyclone_overflow_200_mesh) AS avg_first_stage_cyclone_overflow_200_mesh,
    AVG(second_stage_cyclone_overflow_500_mesh) AS avg_second_stage_cyclone_overflow_500_mesh
FROM data_ball_mill bm
CROSS JOIN LATERAL get_week_thu_to_wed(bm.report_date) AS wk
GROUP BY wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date
ORDER BY year, week_number;

-- 按月总体统计（财务月）
CREATE OR REPLACE VIEW vdata_ball_mill_monthly_total_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    COUNT(*) AS total_records,
    SUM(daily_ore_tonnage) AS total_daily_ore_tonnage,
    SUM(operating_time) AS total_operating_time,
    CASE WHEN SUM(operating_time) > 0 THEN ROUND(SUM(daily_ore_tonnage) / SUM(operating_time), 4) ELSE NULL END AS avg_unit_efficiency_tph,
    CASE WHEN (24.0 * COUNT(DISTINCT bm.report_date)) > 0 THEN ROUND(SUM(operating_time) / (24.0 * COUNT(DISTINCT bm.report_date)) * 100, 2) ELSE NULL END AS avg_equipment_utilization,
    AVG(raw_ore_total_iron_grade) AS avg_raw_ore_total_iron_grade,
    AVG(raw_ore_magnetic_iron_grade) AS avg_raw_ore_magnetic_iron_grade,
    AVG(first_stage_cyclone_overflow_200_mesh) AS avg_first_stage_cyclone_overflow_200_mesh,
    AVG(second_stage_cyclone_overflow_500_mesh) AS avg_second_stage_cyclone_overflow_500_mesh
FROM data_ball_mill bm
CROSS JOIN LATERAL get_financial_month(bm.report_date) AS fm
GROUP BY fm.financial_year, fm.financial_month
ORDER BY year, month;
