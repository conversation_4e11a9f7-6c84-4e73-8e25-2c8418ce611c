create table public.data_ball_mill
(
    id                                     serial
        primary key,
    report_date                            date not null
        constraint data_ball_mill_pk
            unique,
    daily_ore_tonnage                      numeric(10, 2),
    operating_time                         numeric(8, 2),
    raw_ore_total_iron_grade               numeric(10, 2),
    raw_ore_magnetic_iron_grade            numeric(10, 2),
    first_stage_cyclone_overflow_200_mesh  numeric(10, 2),
    second_stage_cyclone_overflow_500_mesh numeric(10, 2),
    create_time                            timestamp(6) default now(),
    update_time                            timestamp(6) default now(),
    create_by                              varchar(64),
    update_by                              varchar(64)
);

comment on table public.data_ball_mill is '球磨机';

comment on column public.data_ball_mill.report_date is '日期';

comment on column public.data_ball_mill.daily_ore_tonnage is '日干矿（t）';

comment on column public.data_ball_mill.operating_time is '运行时间';

comment on column public.data_ball_mill.raw_ore_total_iron_grade is '原矿品位-全铁（TFe）';

comment on column public.data_ball_mill.raw_ore_magnetic_iron_grade is '原矿品位-磁性铁（MFe）';

comment on column public.data_ball_mill.first_stage_cyclone_overflow_200_mesh is '一段旋流器溢流-200目含量/%';

comment on column public.data_ball_mill.second_stage_cyclone_overflow_500_mesh is '二段旋流器溢流-500目含量/%';

alter table public.data_ball_mill
    owner to postgres;

