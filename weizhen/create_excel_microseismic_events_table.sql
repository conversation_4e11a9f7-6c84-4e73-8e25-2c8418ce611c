-- 微震事件数据表建表脚本
CREATE TABLE excel_microseismic_events (
    id BIGINT PRIMARY KEY,                          -- 雪花ID主键
    project_id INTEGER NOT NULL,                    -- 工程ID
    event_name VARCHAR(100) NOT NULL,               -- 事件名称
    event_date DATE NOT NULL,                       -- 微震事件日期
    event_time TIME NOT NULL,                       -- 微震事件时间
    location_error DOUBLE PRECISION,                   -- 微震事件定位误差
    x_coordinate DOUBLE PRECISION,                     -- 微震事件X轴坐标
    y_coordinate DOUBLE PRECISION,                     -- 微震事件Y轴坐标
    z_coordinate DOUBLE PRECISION,                     -- 微震事件Z轴坐标
    radiated_energy DOUBLE PRECISION,               -- 微震事件辐射能（范围大，使用双精度）
    p_wave_radiated_energy DOUBLE PRECISION,        -- 微震事件P波辐射能（范围大，使用双精度）
    s_wave_radiated_energy DOUBLE PRECISION,        -- 微震事件S波辐射能（范围大，使用双精度）
    moment_magnitude DOUBLE PRECISION,                 -- 矩震级
    richter_magnitude DOUBLE PRECISION,                -- 里氏震级
    local_magnitude DOUBLE PRECISION,                  -- 当地震级
    seismic_moment DOUBLE PRECISION,                -- 地震矩（范围大，使用双精度）
    p_wave_seismic_moment DOUBLE PRECISION,         -- P波地震矩（范围大，使用双精度）
    s_wave_seismic_moment DOUBLE PRECISION,         -- S波地震矩（范围大，使用双精度）
    volumetric_strain DOUBLE PRECISION,               -- 体变势
    apparent_stress DOUBLE PRECISION,               -- 视应力（范围大，使用双精度）
    apparent_volume DOUBLE PRECISION,               -- 视体积（范围大，使用双精度）
    corner_frequency DOUBLE PRECISION,                 -- 转角频率
    p_wave_corner_frequency DOUBLE PRECISION,          -- P波转角频率
    s_wave_corner_frequency DOUBLE PRECISION,          -- S波转角频率
    p_wave_low_freq_amplitude DOUBLE PRECISION,       -- P波低频幅值
    s_wave_low_freq_amplitude DOUBLE PRECISION,       -- S波低频幅值
    static_stress_drop DOUBLE PRECISION,            -- 静态应力降（范围大，使用双精度）
    dynamic_stress_drop DOUBLE PRECISION,           -- 动态应力降（范围大，使用双精度）
    source_radius DOUBLE PRECISION,                   -- 震源半径
    max_slip_velocity DOUBLE PRECISION,             -- 最大滑移速度（范围可能较大）
    server_id INTEGER,                              -- 微震事件服务器ID
    triggered_sensor_count INTEGER,                 -- 被触发传感器个数
    triggered_sensor_ids TEXT,                      -- 被触发传感器ID
    location_sensor_ids TEXT,                       -- 参与定位传感器ID
    signal_type INTEGER,                            -- 信号类型
    auto_signal_type INTEGER,                       -- 信号类型_自动

    is_deleted BOOLEAN DEFAULT FALSE,
    
    -- 审计字段
    created_by VARCHAR(100) DEFAULT 'system',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100) DEFAULT 'system',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_excel_microseismic_events_project_id ON excel_microseismic_events(project_id);
CREATE INDEX idx_excel_microseismic_events_event_date ON excel_microseismic_events(event_date);
CREATE INDEX idx_excel_microseismic_events_event_time ON excel_microseismic_events(event_time);
CREATE INDEX idx_excel_microseismic_events_created_at ON excel_microseismic_events(created_at);

-- 添加表注释
COMMENT ON TABLE excel_microseismic_events IS '微震事件数据表';
COMMENT ON COLUMN excel_microseismic_events.id IS '主键ID（雪花ID）';
COMMENT ON COLUMN excel_microseismic_events.project_id IS '工程ID';
COMMENT ON COLUMN excel_microseismic_events.event_name IS '事件名称';
COMMENT ON COLUMN excel_microseismic_events.event_date IS '微震事件日期';
COMMENT ON COLUMN excel_microseismic_events.event_time IS '微震事件时间';
COMMENT ON COLUMN excel_microseismic_events.location_error IS '微震事件定位误差';
COMMENT ON COLUMN excel_microseismic_events.x_coordinate IS '微震事件X轴坐标';
COMMENT ON COLUMN excel_microseismic_events.y_coordinate IS '微震事件Y轴坐标';
COMMENT ON COLUMN excel_microseismic_events.z_coordinate IS '微震事件Z轴坐标';
COMMENT ON COLUMN excel_microseismic_events.radiated_energy IS '微震事件辐射能';
COMMENT ON COLUMN excel_microseismic_events.p_wave_radiated_energy IS '微震事件P波辐射能';
COMMENT ON COLUMN excel_microseismic_events.s_wave_radiated_energy IS '微震事件S波辐射能';
COMMENT ON COLUMN excel_microseismic_events.moment_magnitude IS '矩震级';
COMMENT ON COLUMN excel_microseismic_events.richter_magnitude IS '里氏震级';
COMMENT ON COLUMN excel_microseismic_events.local_magnitude IS '当地震级';
COMMENT ON COLUMN excel_microseismic_events.seismic_moment IS '地震矩';
COMMENT ON COLUMN excel_microseismic_events.p_wave_seismic_moment IS 'P波地震矩';
COMMENT ON COLUMN excel_microseismic_events.s_wave_seismic_moment IS 'S波地震矩';
COMMENT ON COLUMN excel_microseismic_events.volumetric_strain IS '体变势';
COMMENT ON COLUMN excel_microseismic_events.apparent_stress IS '视应力';
COMMENT ON COLUMN excel_microseismic_events.apparent_volume IS '视体积';
COMMENT ON COLUMN excel_microseismic_events.corner_frequency IS '转角频率';
COMMENT ON COLUMN excel_microseismic_events.p_wave_corner_frequency IS 'P波转角频率';
COMMENT ON COLUMN excel_microseismic_events.s_wave_corner_frequency IS 'S波转角频率';
COMMENT ON COLUMN excel_microseismic_events.p_wave_low_freq_amplitude IS 'P波低频幅值';
COMMENT ON COLUMN excel_microseismic_events.s_wave_low_freq_amplitude IS 'S波低频幅值';
COMMENT ON COLUMN excel_microseismic_events.static_stress_drop IS '静态应力降';
COMMENT ON COLUMN excel_microseismic_events.dynamic_stress_drop IS '动态应力降';
COMMENT ON COLUMN excel_microseismic_events.source_radius IS '震源半径';
COMMENT ON COLUMN excel_microseismic_events.max_slip_velocity IS '最大滑移速度';
COMMENT ON COLUMN excel_microseismic_events.server_id IS '微震事件服务器ID';
COMMENT ON COLUMN excel_microseismic_events.triggered_sensor_count IS '被触发传感器个数';
COMMENT ON COLUMN excel_microseismic_events.triggered_sensor_ids IS '被触发传感器ID';
COMMENT ON COLUMN excel_microseismic_events.location_sensor_ids IS '参与定位传感器ID';
COMMENT ON COLUMN excel_microseismic_events.signal_type IS '信号类型';
COMMENT ON COLUMN excel_microseismic_events.auto_signal_type IS '信号类型_自动';
COMMENT ON COLUMN excel_microseismic_events.created_at IS '创建时间';
COMMENT ON COLUMN excel_microseismic_events.updated_at IS '更新时间';
