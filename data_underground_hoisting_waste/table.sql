
-- 井下提升与抛废（日指标）
DROP TABLE IF EXISTS data_underground_hoisting_waste;

CREATE TABLE IF NOT EXISTS data_underground_hoisting_waste (
    id                     bigserial primary key,
    operation_date         date NOT NULL,              -- 日期
    belt_name              varchar(50),                -- 皮带/系统，如：S2皮带
    dry_ore_tons           numeric(10,2),              -- 日干矿（t）
    runtime_minutes        integer,                    -- 运行时间（分钟）
    waste_tons             numeric(10,2),              -- 抛废量（t）
    grade_tfe              numeric(10,2),               -- 品位-全铁（TFe）
    grade_mfe              numeric(10,2),               -- 品位-磁性铁（MFe）
    remarks                varchar(500),               -- 备注
    create_by              varchar(64),
    create_time            timestamp(6) DEFAULT now(),
    update_by              varchar(64),
    update_time            timestamp(6) DEFAULT now()
);

-- 表与字段注释
COMMENT ON TABLE data_underground_hoisting_waste IS '井下提升与抛废（日指标）';
COMMENT ON COLUMN data_underground_hoisting_waste.id IS '主键ID';
COMMENT ON COLUMN data_underground_hoisting_waste.operation_date IS '日期';
COMMENT ON COLUMN data_underground_hoisting_waste.belt_name IS '皮带/系统名称（如S2皮带）';
COMMENT ON COLUMN data_underground_hoisting_waste.dry_ore_tons IS '日干矿（t）';
COMMENT ON COLUMN data_underground_hoisting_waste.runtime_minutes IS '运行时间（分钟）';
COMMENT ON COLUMN data_underground_hoisting_waste.waste_tons IS '抛废量（t）';
COMMENT ON COLUMN data_underground_hoisting_waste.grade_tfe IS '品位-全铁（TFe）';
COMMENT ON COLUMN data_underground_hoisting_waste.grade_mfe IS '品位-磁性铁（MFe）';
COMMENT ON COLUMN data_underground_hoisting_waste.remarks IS '备注';
COMMENT ON COLUMN data_underground_hoisting_waste.create_by IS '创建人';
COMMENT ON COLUMN data_underground_hoisting_waste.create_time IS '创建时间';
COMMENT ON COLUMN data_underground_hoisting_waste.update_by IS '更新人';
COMMENT ON COLUMN data_underground_hoisting_waste.update_time IS '更新时间';

-- 唯一约束：同一日期+皮带仅一条记录
ALTER TABLE data_underground_hoisting_waste
    ADD CONSTRAINT uk_uhw_date_belt UNIQUE (operation_date, belt_name);

-- 索引
CREATE INDEX IF NOT EXISTS idx_uhw_date ON data_underground_hoisting_waste (operation_date);
CREATE INDEX IF NOT EXISTS idx_uhw_belt ON data_underground_hoisting_waste (belt_name);
CREATE INDEX IF NOT EXISTS idx_uhw_date_belt ON data_underground_hoisting_waste (operation_date, belt_name);
