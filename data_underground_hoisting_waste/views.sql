
-- 按日总体统计（跨皮带汇总）
CREATE OR REPLACE VIEW vdata_underground_hoisting_waste_daily_total_stats AS
SELECT 
    operation_date,
    COUNT(*) AS total_records,
    SUM(dry_ore_tons) AS total_dry_ore_tons,
    SUM(runtime_minutes) AS total_runtime_minutes,
    SUM(waste_tons) AS total_waste_tons,
    CASE WHEN SUM(runtime_minutes) > 0 THEN ROUND(SUM(dry_ore_tons) * 60.0 / SUM(runtime_minutes), 4) ELSE NULL END AS avg_unit_efficiency_tph,
    CASE WHEN (24.0 * COUNT(DISTINCT operation_date)) > 0 THEN ROUND((SUM(runtime_minutes) / 60.0) / (24.0 * COUNT(DISTINCT operation_date)) * 100, 2) ELSE NULL END AS avg_equipment_utilization,
    AVG(grade_tfe) AS avg_grade_tfe,
    AVG(grade_mfe) AS avg_grade_mfe
FROM data_underground_hoisting_waste
GROUP BY operation_date
ORDER BY operation_date;

-- 按周总体统计（财务周：周四-周三，跨皮带汇总）
CREATE OR REPLACE VIEW vdata_underground_hoisting_waste_weekly_total_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number,
    wk.week_start_date,
    wk.week_end_date,
    COUNT(*) AS total_records,
    SUM(dry_ore_tons) AS total_dry_ore_tons,
    SUM(runtime_minutes) AS total_runtime_minutes,
    SUM(waste_tons) AS total_waste_tons,
    CASE WHEN SUM(runtime_minutes) > 0 THEN ROUND(SUM(dry_ore_tons) * 60.0 / SUM(runtime_minutes), 4) ELSE NULL END AS avg_unit_efficiency_tph,
    CASE WHEN (24.0 * COUNT(DISTINCT uhw.operation_date)) > 0 THEN ROUND((SUM(runtime_minutes) / 60.0) / (24.0 * COUNT(DISTINCT uhw.operation_date)) * 100, 2) ELSE NULL END AS avg_equipment_utilization,
    AVG(grade_tfe) AS avg_grade_tfe,
    AVG(grade_mfe) AS avg_grade_mfe
FROM data_underground_hoisting_waste uhw
CROSS JOIN LATERAL get_week_thu_to_wed(uhw.operation_date) AS wk
GROUP BY wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date
ORDER BY year, week_number;

-- 按月总体统计（财务月，跨皮带汇总）
CREATE OR REPLACE VIEW vdata_underground_hoisting_waste_monthly_total_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    COUNT(*) AS total_records,
    SUM(dry_ore_tons) AS total_dry_ore_tons,
    SUM(runtime_minutes) AS total_runtime_minutes,
    SUM(waste_tons) AS total_waste_tons,
    CASE WHEN SUM(runtime_minutes) > 0 THEN ROUND(SUM(dry_ore_tons) * 60.0 / SUM(runtime_minutes), 4) ELSE NULL END AS avg_unit_efficiency_tph,
    CASE WHEN (24.0 * COUNT(DISTINCT uhw.operation_date)) > 0 THEN ROUND((SUM(runtime_minutes) / 60.0) / (24.0 * COUNT(DISTINCT uhw.operation_date)) * 100, 2) ELSE NULL END AS avg_equipment_utilization,
    AVG(grade_tfe) AS avg_grade_tfe,
    AVG(grade_mfe) AS avg_grade_mfe
FROM data_underground_hoisting_waste uhw
CROSS JOIN LATERAL get_financial_month(uhw.operation_date) AS fm
GROUP BY fm.financial_year, fm.financial_month
ORDER BY year, month;
