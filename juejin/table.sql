-- 掘进数据表
CREATE TABLE IF NOT EXISTS data_tunneling (
    id bigserial primary key,
    operation_date date NOT NULL,   -- 作业日期
    project_department_id bigint NOT NULL,   -- 项目部门ID
    working_face_id bigint NOT NULL,   -- 工作面ID (中段)
    stope_id bigint NOT NULL,   -- 采场ID
    working_period_id bigint NOT NULL,   -- 作业时段ID
    tunneling_length numeric(8,2),   -- 掘进长度(m)
    tunneling_volume numeric(10,2),   -- 掘进体积(m³)
    work_content varchar(200),   -- 工作内容
    remarks varchar(500),   -- 备注
    create_by varchar(64),
    create_time timestamp(6) DEFAULT now(),
    update_by varchar(64),
    update_time timestamp(6) DEFAULT now(),
    FOREIGN KEY (project_department_id) REFERENCES base_project_department(project_department_id),
    FOREIGN KEY (stope_id) REFERENCES base_stope(stope_id),
    FOREIG<PERSON> KEY (working_face_id) REFERENCES base_working_face(working_face_id),
    FOREIGN KEY (working_period_id) REFERENCES base_working_period(working_period_id)
);

COMMENT ON TABLE data_tunneling IS '掘进数据表';
COMMENT ON COLUMN data_tunneling.id IS '掘进数据ID';
COMMENT ON COLUMN data_tunneling.operation_date IS '作业日期';
COMMENT ON COLUMN data_tunneling.project_department_id IS '项目部门ID';
COMMENT ON COLUMN data_tunneling.stope_id IS '采场ID';
COMMENT ON COLUMN data_tunneling.working_face_id IS '工作面ID';
COMMENT ON COLUMN data_tunneling.working_period_id IS '作业时段ID';
COMMENT ON COLUMN data_tunneling.tunneling_length IS '掘进长度(m)';
COMMENT ON COLUMN data_tunneling.tunneling_volume IS '掘进体积(m³)';
COMMENT ON COLUMN data_tunneling.work_content IS '工作内容';
COMMENT ON COLUMN data_tunneling.remarks IS '备注';
COMMENT ON COLUMN data_tunneling.create_by IS '创建人';
COMMENT ON COLUMN data_tunneling.create_time IS '创建时间';
COMMENT ON COLUMN data_tunneling.update_by IS '更新人';
COMMENT ON COLUMN data_tunneling.update_time IS '更新时间';

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_data_tunneling_date ON data_tunneling (operation_date);
CREATE INDEX IF NOT EXISTS idx_data_tunneling_department ON data_tunneling (project_department_id);
CREATE INDEX IF NOT EXISTS idx_data_tunneling_stope ON data_tunneling (stope_id);
CREATE INDEX IF NOT EXISTS idx_data_tunneling_face ON data_tunneling (working_face_id);
CREATE INDEX IF NOT EXISTS idx_data_tunneling_period ON data_tunneling (working_period_id);
