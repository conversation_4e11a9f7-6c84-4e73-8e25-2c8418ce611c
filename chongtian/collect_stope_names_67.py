#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
收集采场名称脚本 - 适配文件6和7
从extracted_data6.csv和extracted_data7.csv中提取唯一的采场名称，并生成SQL插入语句
用于填充base_stope表（仅插入新的采场名称）
"""

import pandas as pd
import os
import re

def collect_stope_names_from_file(csv_file):
    """从CSV文件中收集唯一的采场名称 - 适配文件6和7格式"""
    
    # 检查文件是否存在
    if not os.path.exists(csv_file):
        print(f"错误: 找不到文件 {csv_file}")
        return None
    
    try:
        # 读取CSV文件
        df = pd.read_csv(csv_file, encoding='utf-8', header=None)
        
        print(f"成功读取文件: {csv_file}")
        print(f"数据行数: {len(df)}")
        
        # 收集采场名称
        stope_names = set()
        
        for index, row in df.iterrows():
            # 检查第10列（索引为9）是否包含采场信息
            if len(row) > 9 and not pd.isna(row.iloc[9]):
                content = str(row.iloc[9]).strip()
                
                # 使用正则表达式提取采场名称，如"0#-8-1/2"、"0#-16-5"、"0#-12-1/2"、"0#-7-5"
                # 提取数字部分作为采场名称
                match = re.search(r'0#-(\d+-\d+(?:/\d+)?)', content)
                if match:
                    stope_name = match.group(1)
                    stope_names.add(stope_name)
                    print(f"找到采场: {stope_name}")
                else:
                    # 如果没有匹配到，尝试其他格式
                    match = re.search(r'0#-(\d+-\d+)', content)
                    if match:
                        stope_name = match.group(1)
                        stope_names.add(stope_name)
                        print(f"找到采场: {stope_name}")
        
        return list(stope_names)
    
    except Exception as e:
        print(f"处理CSV文件时出错: {str(e)}")
        return None

def collect_stope_names_from_files_67():
    """从文件6和7中收集采场名称"""
    
    # 定义要处理的文件列表（文件6和7）
    csv_files = [
        'extracted_data6.csv',
        'extracted_data7.csv'
    ]
    
    all_stope_names = set()
    
    for csv_file in csv_files:
        if os.path.exists(csv_file):
            print(f"\n处理文件: {csv_file}")
            stope_names = collect_stope_names_from_file(csv_file)
            if stope_names:
                all_stope_names.update(stope_names)
                print(f"从 {csv_file} 中收集到 {len(stope_names)} 个采场名称")
        else:
            print(f"文件不存在: {csv_file}")
    
    return list(all_stope_names)

def generate_sql_inserts(stope_names):
    """生成SQL插入语句，仅插入base_stope表中不存在的采场名称"""
    if not stope_names:
        print("没有有效的采场名称")
        return []
    
    sql_inserts = []
    for name in stope_names:
        # 文件6和7的采场默认working_face_id为1（可根据实际情况调整）
        working_face_id = 1
        
        # 生成INSERT语句，使用ON CONFLICT DO NOTHING避免重复
        sql = f"INSERT INTO base_stope (stope_name, working_face_id, status, is_delete, create_by, create_time) VALUES ('{name}', {working_face_id}, 1, 0, 'system', now()) ON CONFLICT (stope_name, working_face_id) DO NOTHING;"
        sql_inserts.append(sql)
    
    return sql_inserts

if __name__ == "__main__":
    print("开始收集采场名称（文件6和7格式）...")
    
    # 从文件6和7中收集采场名称
    stope_names = collect_stope_names_from_files_67()
    
    if stope_names:
        print(f"\n总共收集到 {len(stope_names)} 个唯一的采场名称:")
        for name in sorted(stope_names):
            print(f"  - {name}")
        
        # 生成SQL插入语句
        sql_statements = generate_sql_inserts(stope_names)
        
        if sql_statements:
            # 写入SQL文件
            output_file = "stope_dml_67.sql"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("-- 充填模块采场数据插入语句 (文件6和7格式)\n")
                f.write(f"-- 生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"-- 总共 {len(sql_statements)} 个采场\n\n")
                
                for sql in sql_statements:
                    f.write(sql + "\n")
            
            print(f"\n已生成 {len(sql_statements)} 条SQL插入语句到 {output_file}")
        else:
            print("未生成任何SQL插入语句")
    else:
        print("未找到任何有效的采场名称")
