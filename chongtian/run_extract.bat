python "c:\Users\<USER>\doc\5\extract_excel.py" "c:\Users\<USER>\doc\5\统计\2025年1月生产及建设统计表.xlsx" --start-row 222 --end-row 256 --start-col F --end-col O --output "C:\Users\<USER>\doc\5\chongtian\extracted_data1.xlsx"

python c:\Users\<USER>\doc\5\chongtian\generate_sql_inserts.py c:\Users\<USER>\doc\5\chongtian\extracted_data1.csv -o c:\Users\<USER>\doc\5\chongtian\dml1.sql

python "c:\Users\<USER>\doc\5\extract_excel.py" "c:\Users\<USER>\doc\5\统计\2025年2月生产及建设统计表.xlsx" --start-row 230 --end-row 279 --start-col F --end-col O --output "C:\Users\<USER>\doc\5\chongtian\extracted_data2.xlsx"

python c:\Users\<USER>\doc\5\chongtian\generate_sql_inserts.py c:\Users\<USER>\doc\5\chongtian\extracted_data2.csv -o c:\Users\<USER>\doc\5\chongtian\dml2.sql

python "c:\Users\<USER>\doc\5\extract_excel.py" "c:\Users\<USER>\doc\5\统计\2025年3月份生产及建设统计表.xlsx" --start-row 250 --end-row 299 --start-col F --end-col O --output "C:\Users\<USER>\doc\5\chongtian\extracted_data3.xlsx"

python c:\Users\<USER>\doc\5\chongtian\generate_sql_inserts.py c:\Users\<USER>\doc\5\chongtian\extracted_data3.csv -o c:\Users\<USER>\doc\5\chongtian\dml3.sql

python "c:\Users\<USER>\doc\5\extract_excel.py" "c:\Users\<USER>\doc\5\统计\2025年4月生产及建设统计表.xlsx" --start-row 250 --end-row 299 --start-col F --end-col O --output "C:\Users\<USER>\doc\5\chongtian\extracted_data4.xlsx"

python c:\Users\<USER>\doc\5\chongtian\generate_sql_inserts.py c:\Users\<USER>\doc\5\chongtian\extracted_data4.csv -o c:\Users\<USER>\doc\5\chongtian\dml4.sql

python "c:\Users\<USER>\doc\5\extract_excel.py" "c:\Users\<USER>\doc\5\统计\2025年5月份生产及建设统计表.xlsx" --start-row 261 --end-row 311 --start-col F --end-col O --output "C:\Users\<USER>\doc\5\chongtian\extracted_data5.xlsx"

python c:\Users\<USER>\doc\5\chongtian\generate_sql_inserts.py c:\Users\<USER>\doc\5\chongtian\extracted_data5.csv -o c:\Users\<USER>\doc\5\chongtian\dml5.sql

python "c:\Users\<USER>\doc\5\extract_excel.py" "c:\Users\<USER>\doc\5\统计\2025年6月份生产及建设统计表.xlsx" --start-row 257 --end-row 322 --start-col G --end-col O --output "C:\Users\<USER>\doc\5\chongtian\extracted_data6.xlsx"

python c:\Users\<USER>\doc\5\chongtian\generate_sql_inserts.py c:\Users\<USER>\doc\5\chongtian\extracted_data6.csv -o c:\Users\<USER>\doc\5\chongtian\dml6.sql

python "c:\Users\<USER>\doc\5\extract_excel.py" "c:\Users\<USER>\doc\5\统计\2025年7月份生产及建设统计表.xlsx" --start-row 257 --end-row 322 --start-col G --end-col O --output "C:\Users\<USER>\doc\5\chongtian\extracted_data7.xlsx"

python c:\Users\<USER>\doc\5\chongtian\generate_sql_inserts.py c:\Users\<USER>\doc\5\chongtian\extracted_data7.csv -o c:\Users\<USER>\doc\5\chongtian\dml7.sql


python c:\Users\<USER>\doc\5\chongtian\collect_stope_names.py


-1020m(\d)-(\d+-\d+(/\d+)?)采场

$1#-$2