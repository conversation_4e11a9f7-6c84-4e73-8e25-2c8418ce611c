-- 充填模块采场数据插入语句
-- 生成时间: 2025-07-31 22:09:30
-- 总共 6 个采场

INSERT INTO base_stope (stope_name, working_face_id, status, is_delete, create_by, create_time) VALUES ('20-5', 1, 1, 0, 'system', now()) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id, status, is_delete, create_by, create_time) VALUES ('12-5', 1, 1, 0, 'system', now()) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id, status, is_delete, create_by, create_time) VALUES ('8-5', 1, 1, 0, 'system', now()) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id, status, is_delete, create_by, create_time) VALUES ('16-5', 1, 1, 0, 'system', now()) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id, status, is_delete, create_by, create_time) VALUES ('11-5', 1, 1, 0, 'system', now()) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id, status, is_delete, create_by, create_time) VALUES ('8-1', 1, 1, 0, 'system', now()) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
