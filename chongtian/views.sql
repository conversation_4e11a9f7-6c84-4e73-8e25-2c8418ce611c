-- 充填数据视图

-- 创建视图：按日统计充填数据（总体）
CREATE OR REPLACE VIEW vdata_filling_daily_total_stats AS
SELECT 
    operation_date,
    SUM(slurry_volume) AS total_slurry_volume,
    SUM(cement_weight) AS total_cement_weight,
    AVG(filling_concentration) AS avg_filling_concentration
FROM 
    data_filling
GROUP BY 
    operation_date
ORDER BY 
    operation_date;

-- 创建视图：按日统计充填数据（按作业时段、项目部门、采场）
CREATE OR REPLACE VIEW vdata_filling_daily_stats AS
SELECT 
    operation_date,
    working_period_id,
    project_department_id,
    stope_id,
    SUM(slurry_volume) AS total_slurry_volume,
    SUM(cement_weight) AS total_cement_weight,
    AVG(filling_concentration) AS avg_filling_concentration
FROM 
    data_filling
GROUP BY 
    operation_date,
    working_period_id,
    project_department_id,
    stope_id
ORDER BY 
    operation_date, working_period_id, project_department_id, stope_id;

-- 创建视图：按日期和作业时段统计充填数据
CREATE OR REPLACE VIEW vdata_filling_daily_period_stats AS
SELECT 
    f.operation_date,
    f.working_period_id,
    wp.working_period_name,
    SUM(f.slurry_volume) AS total_slurry_volume,
    SUM(f.cement_weight) AS total_cement_weight,
    AVG(f.filling_concentration) AS avg_filling_concentration
FROM 
    data_filling f
JOIN
    base_working_period wp ON f.working_period_id = wp.working_period_id
GROUP BY
    f.operation_date,
    f.working_period_id,
    wp.working_period_name
ORDER BY 
    f.operation_date, f.working_period_id;

-- 创建视图：按日期和项目部门统计充填数据
CREATE OR REPLACE VIEW vdata_filling_daily_department_stats AS
SELECT 
    f.operation_date,
    f.project_department_id,
    pd.project_department_name,
    SUM(f.slurry_volume) AS total_slurry_volume,
    SUM(f.cement_weight) AS total_cement_weight,
    AVG(f.filling_concentration) AS avg_filling_concentration
FROM 
    data_filling f
JOIN
    base_project_department pd ON f.project_department_id = pd.project_department_id
GROUP BY
    f.operation_date,
    f.project_department_id,
    pd.project_department_name
ORDER BY 
    f.operation_date, f.project_department_id;

-- 创建视图：按日期和采场统计充填数据
CREATE OR REPLACE VIEW vdata_filling_daily_stope_stats AS
SELECT 
    f.operation_date,
    f.stope_id,
    s.stope_name,
    SUM(f.slurry_volume) AS total_slurry_volume,
    SUM(f.cement_weight) AS total_cement_weight,
    AVG(f.filling_concentration) AS avg_filling_concentration
FROM 
    data_filling f
JOIN
    base_stope s ON f.stope_id = s.stope_id
GROUP BY
    f.operation_date,
    f.stope_id,
    s.stope_name
ORDER BY 
    f.operation_date, f.stope_id;

-- 创建视图：按月统计充填数据（总体）（月定义为前一个月的29号到当月28号）
CREATE OR REPLACE VIEW vdata_filling_monthly_total_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    SUM(slurry_volume) AS total_slurry_volume,
    SUM(cement_weight) AS total_cement_weight,
    AVG(filling_concentration) AS avg_filling_concentration
FROM 
    data_filling
CROSS JOIN LATERAL get_financial_month(operation_date) AS fm
GROUP BY 
    fm.financial_year, fm.financial_month
ORDER BY 
    fm.financial_year, fm.financial_month;

-- 创建视图：按月统计充填数据（按作业时段）
CREATE OR REPLACE VIEW vdata_filling_monthly_period_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    f.working_period_id,
    wp.working_period_name,
    SUM(f.slurry_volume) AS total_slurry_volume,
    SUM(f.cement_weight) AS total_cement_weight,
    AVG(f.filling_concentration) AS avg_filling_concentration
FROM 
    data_filling f
CROSS JOIN LATERAL get_financial_month(f.operation_date) AS fm
JOIN
    base_working_period wp ON f.working_period_id = wp.working_period_id
GROUP BY 
    fm.financial_year, fm.financial_month, f.working_period_id, wp.working_period_name
ORDER BY 
    year, month, f.working_period_id;

-- 创建视图：按月统计充填数据（按项目部门）
CREATE OR REPLACE VIEW vdata_filling_monthly_department_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    f.project_department_id,
    pd.project_department_name,
    SUM(f.slurry_volume) AS total_slurry_volume,
    SUM(f.cement_weight) AS total_cement_weight,
    AVG(f.filling_concentration) AS avg_filling_concentration
FROM 
    data_filling f
CROSS JOIN LATERAL get_financial_month(f.operation_date) AS fm
JOIN
    base_project_department pd ON f.project_department_id = pd.project_department_id
GROUP BY 
    fm.financial_year, fm.financial_month, f.project_department_id, pd.project_department_name
ORDER BY 
    year, month, f.project_department_id;

-- 创建视图：按月统计充填数据（按采场）
CREATE OR REPLACE VIEW vdata_filling_monthly_stope_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    f.stope_id,
    s.stope_name,
    SUM(f.slurry_volume) AS total_slurry_volume,
    SUM(f.cement_weight) AS total_cement_weight,
    AVG(f.filling_concentration) AS avg_filling_concentration
FROM 
    data_filling f
CROSS JOIN LATERAL get_financial_month(f.operation_date) AS fm
JOIN
    base_stope s ON f.stope_id = s.stope_id
GROUP BY 
    fm.financial_year, fm.financial_month, f.stope_id, s.stope_name
ORDER BY 
    year, month, f.stope_id;

-- 创建视图：按周统计充填数据（总体）
CREATE OR REPLACE VIEW vdata_filling_weekly_total_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number AS week,
    wk.week_start_date,
    wk.week_end_date,
    SUM(slurry_volume) AS total_slurry_volume,
    SUM(cement_weight) AS total_cement_weight,
    AVG(filling_concentration) AS avg_filling_concentration
FROM 
    data_filling
CROSS JOIN LATERAL get_week_thu_to_wed(operation_date) AS wk
GROUP BY 
    wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date
ORDER BY 
    wk.week_year, wk.week_number;

-- 创建视图：按周统计充填数据（按作业时段）
CREATE OR REPLACE VIEW vdata_filling_weekly_period_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number AS week,
    wk.week_start_date,
    wk.week_end_date,
    f.working_period_id,
    wp.working_period_name,
    SUM(f.slurry_volume) AS total_slurry_volume,
    SUM(f.cement_weight) AS total_cement_weight,
    AVG(f.filling_concentration) AS avg_filling_concentration
FROM 
    data_filling f
CROSS JOIN LATERAL get_week_thu_to_wed(f.operation_date) AS wk
JOIN
    base_working_period wp ON f.working_period_id = wp.working_period_id
GROUP BY 
    wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date,
    f.working_period_id, wp.working_period_name
ORDER BY 
    year, week, f.working_period_id;

-- 创建视图：按周统计充填数据（按项目部门）
CREATE OR REPLACE VIEW vdata_filling_weekly_department_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number AS week,
    wk.week_start_date,
    wk.week_end_date,
    f.project_department_id,
    pd.project_department_name,
    SUM(f.slurry_volume) AS total_slurry_volume,
    SUM(f.cement_weight) AS total_cement_weight,
    AVG(f.filling_concentration) AS avg_filling_concentration
FROM 
    data_filling f
CROSS JOIN LATERAL get_week_thu_to_wed(f.operation_date) AS wk
JOIN
    base_project_department pd ON f.project_department_id = pd.project_department_id
GROUP BY 
    wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date,
    f.project_department_id, pd.project_department_name
ORDER BY 
    year, week, f.project_department_id;

-- 创建视图：按周统计充填数据（按采场）
CREATE OR REPLACE VIEW vdata_filling_weekly_stope_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number AS week,
    wk.week_start_date,
    wk.week_end_date,
    f.stope_id,
    s.stope_name,
    SUM(f.slurry_volume) AS total_slurry_volume,
    SUM(f.cement_weight) AS total_cement_weight,
    AVG(f.filling_concentration) AS avg_filling_concentration
FROM 
    data_filling f
CROSS JOIN LATERAL get_week_thu_to_wed(f.operation_date) AS wk
JOIN
    base_stope s ON f.stope_id = s.stope_id
GROUP BY 
    wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date,
    f.stope_id, s.stope_name
ORDER BY 
    year, week, f.stope_id;

-- 创建视图：按年统计充填数据（总体）
CREATE OR REPLACE VIEW vdata_filling_yearly_total_stats AS
SELECT 
    fy.financial_year AS year,
    SUM(slurry_volume) AS total_slurry_volume,
    SUM(cement_weight) AS total_cement_weight,
    AVG(filling_concentration) AS avg_filling_concentration
FROM 
    data_filling
CROSS JOIN LATERAL get_financial_year(operation_date) AS fy
GROUP BY 
    fy.financial_year
ORDER BY 
    fy.financial_year;

-- 创建视图：按年统计充填数据（按作业时段）
CREATE OR REPLACE VIEW vdata_filling_yearly_period_stats AS
SELECT 
    fy.financial_year AS year,
    f.working_period_id,
    wp.working_period_name,
    SUM(f.slurry_volume) AS total_slurry_volume,
    SUM(f.cement_weight) AS total_cement_weight,
    AVG(f.filling_concentration) AS avg_filling_concentration
FROM 
    data_filling f
CROSS JOIN LATERAL get_financial_year(f.operation_date) AS fy
JOIN
    base_working_period wp ON f.working_period_id = wp.working_period_id
GROUP BY 
    fy.financial_year, f.working_period_id, wp.working_period_name
ORDER BY 
    year, f.working_period_id;

-- 创建视图：按年统计充填数据（按项目部门）
CREATE OR REPLACE VIEW vdata_filling_yearly_department_stats AS
SELECT 
    fy.financial_year AS year,
    f.project_department_id,
    pd.project_department_name,
    SUM(f.slurry_volume) AS total_slurry_volume,
    SUM(f.cement_weight) AS total_cement_weight,
    AVG(f.filling_concentration) AS avg_filling_concentration
FROM 
    data_filling f
CROSS JOIN LATERAL get_financial_year(f.operation_date) AS fy
JOIN
    base_project_department pd ON f.project_department_id = pd.project_department_id
GROUP BY 
    fy.financial_year, f.project_department_id, pd.project_department_name
ORDER BY 
    year, f.project_department_id;

-- 创建视图：按年统计充填数据（按采场）
CREATE OR REPLACE VIEW vdata_filling_yearly_stope_stats AS
SELECT 
    fy.financial_year AS year,
    f.stope_id,
    s.stope_name,
    SUM(f.slurry_volume) AS total_slurry_volume,
    SUM(f.cement_weight) AS total_cement_weight,
    AVG(f.filling_concentration) AS avg_filling_concentration
FROM 
    data_filling f
CROSS JOIN LATERAL get_financial_year(f.operation_date) AS fy
JOIN
    base_stope s ON f.stope_id = s.stope_id
GROUP BY 
    fy.financial_year, f.stope_id, s.stope_name
ORDER BY 
    year, f.stope_id;
