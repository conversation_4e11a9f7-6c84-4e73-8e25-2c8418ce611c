-- 充填模块采场数据插入语句 (文件6和7格式)
-- 生成时间: 2025-07-31 22:13:03
-- 总共 5 个采场

INSERT INTO base_stope (stope_name, working_face_id, status, is_delete, create_by, create_time) VALUES ('16-5', 1, 1, 0, 'system', now()) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id, status, is_delete, create_by, create_time) VALUES ('26-1', 1, 1, 0, 'system', now()) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id, status, is_delete, create_by, create_time) VALUES ('8-1/2', 1, 1, 0, 'system', now()) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id, status, is_delete, create_by, create_time) VALUES ('7-5', 1, 1, 0, 'system', now()) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id, status, is_delete, create_by, create_time) VALUES ('12-1/2', 1, 1, 0, 'system', now()) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
