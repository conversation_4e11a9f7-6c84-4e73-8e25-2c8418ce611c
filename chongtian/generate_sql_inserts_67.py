#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
生成SQL插入语句脚本 - 适配文件6和7
从CSV文件生成PostgreSQL数据库的INSERT语句 - 充填数据
专门处理extracted_data6.csv和extracted_data7.csv格式
"""

import pandas as pd
import re
from datetime import datetime, time
import os
import argparse
from decimal import Decimal, getcontext
import psycopg2
from psycopg2 import sql

# 设置decimal精度
getcontext().prec = 10  # 设置精度为10位

# 定义工作时段ID映射
WORKING_PERIOD_MAP = {
    '0-8时': 1,     # 0-8时对应时段1
    '8-20时': 2,    # 8-20时对应时段2
    '20-0时': 3     # 20-0时对应时段3
}

def parse_date(date_str):
    """解析中文日期格式，转换为标准日期格式"""
    if not isinstance(date_str, str):
        return None
    
    try:
        # 处理"5月29日"、"6月2日"、"7月10日"等格式
        month_match = re.search(r'(\d+)月', date_str)
        day_match = re.search(r'(\d+)日', date_str)
        
        if month_match and day_match:
            month = int(month_match.group(1))
            day = int(day_match.group(1))
            # 根据月份判断年份
            year = 2025 if month <= 12 else 2024
            return f"{year}-{month:02d}-{day:02d}"
    except Exception as e:
        print(f"解析日期出错: {str(e)}")
    return None

def parse_time(time_str):
    """解析时间格式，转换为标准时间格式"""
    if not isinstance(time_str, str) and not isinstance(time_str, (int, float)):
        return None
    
    try:
        if isinstance(time_str, (int, float)):
            # Excel时间序列号转换
            hours = int(time_str / 100) if time_str > 100 else int(time_str)
            minutes = int(time_str % 100) if time_str > 100 else 0
            if hours < 24 and minutes < 60:
                return f"{hours:02d}:{minutes:02d}:00"
        else:
            # 处理字符串时间格式
            time_match = re.search(r'(\d{1,2}):(\d{2})', str(time_str))
            if time_match:
                hours = int(time_match.group(1))
                minutes = int(time_match.group(2))
                if hours < 24 and minutes < 60:
                    return f"{hours:02d}:{minutes:02d}:00"
    except Exception as e:
        print(f"解析时间出错: {str(e)}")
    return None

def safe_value(value, default=0, as_decimal=True):
    """安全获取值，处理NaN和None，并确保返回数字类型"""
    if pd.isna(value) or value is None:
        return Decimal(str(default)) if as_decimal else default
    
    try:
        # 如果是字符串，尝试提取数字
        if isinstance(value, str):
            # 移除非数字字符（除了小数点和负号）
            cleaned = re.sub(r'[^\d.-]', '', value)
            if cleaned and cleaned != '-':
                return Decimal(cleaned) if as_decimal else float(cleaned)
            else:
                return Decimal(str(default)) if as_decimal else default
        else:
            return Decimal(str(value)) if as_decimal else float(value)
    except (ValueError, TypeError):
        return Decimal(str(default)) if as_decimal else default

def connect_to_database():
    """连接到PostgreSQL数据库"""
    try:
        conn = psycopg2.connect(
            host='**********',
            port=5432,
            dbname='lxbi',
            user='postgres',
            password='admin321.'
        )
        return conn
    except Exception as e:
        print(f"数据库连接失败: {str(e)}")
        return None

def get_stope_id_by_name(conn, stope_name):
    """根据采场名称查询采场ID"""
    if not conn:
        return 1  # 默认值
    
    try:
        cursor = conn.cursor()
        # 查询采场ID
        cursor.execute("""
            SELECT stope_id 
            FROM base_stope 
            WHERE stope_name = %s 
            AND is_delete = 0 
            AND status = 1
        """, (stope_name,))
        
        result = cursor.fetchone()
        cursor.close()
        
        if result:
            return result[0]
        else:
            print(f"未找到采场: {stope_name}")
            return 1  # 默认值
    except Exception as e:
        print(f"查询采场ID出错: {str(e)}")
        return 1  # 默认值

def extract_stope_name(content):
    """从内容中提取采场名称 - 适配文件6和7的格式"""
    if not isinstance(content, str):
        return None
    
    # 匹配采场名称格式，如"0#-8-1/2"、"0#-16-5"、"0#-12-1/2"、"0#-7-5"
    # 提取数字部分作为采场名称
    match = re.search(r'0#-(\d+-\d+(?:/\d+)?)', content)
    if match:
        return match.group(1)
    
    # 如果没有匹配到，尝试其他格式
    match = re.search(r'0#-(\d+-\d+)', content)
    if match:
        return match.group(1)
    
    return None

def clean_period_string(period_str):
    """清理时段字符串，移除换行符等"""
    if not isinstance(period_str, str):
        return str(period_str)
    
    # 移除换行符和多余空格
    cleaned = re.sub(r'\s+', '', period_str)
    return cleaned

def generate_sql_inserts(csv_file, output_file=None, encoding='utf-8', db_connect=True):
    """从CSV文件生成SQL插入语句 - 适配文件6和7"""
    
    # 连接数据库
    conn = None
    if db_connect:
        conn = connect_to_database()
        if not conn:
            print("警告: 无法连接数据库，将使用默认值")
    
    # 读取CSV文件
    try:
        df = pd.read_csv(csv_file, encoding=encoding)
        print(f"成功读取CSV文件: {csv_file}")
        print(f"数据行数: {len(df)}")
    except Exception as e:
        print(f"读取CSV文件失败: {str(e)}")
        return
    
    # 生成输出文件名
    if not output_file:
        base_name = os.path.splitext(os.path.basename(csv_file))[0]
        output_file = f"chongtian_dml_{base_name}.sql"
    
    sql_statements = []
    processed_rows = 0
    skipped_rows = 0
    
    current_date = None
    current_stope_id = 1  # 默认采场ID
    first_filling_time = None
    end_filling_time = None
    
    for index, row in df.iterrows():
        try:
            # 获取日期
            date_str = str(row.iloc[0]) if not pd.isna(row.iloc[0]) else ""
            
            # 解析日期
            parsed_date = parse_date(date_str)
            if parsed_date:
                current_date = parsed_date
            
            # 检查是否为首充时间行
            if len(row) > 1 and str(row.iloc[1]) == '首充时间':
                if len(row) > 2 and not pd.isna(row.iloc[2]):
                    first_filling_time = parse_time(row.iloc[2])
                # 提取采场名称 - 文件6和7中采场名称在第10列（索引9）
                if len(row) > 9:
                    stope_content = str(row.iloc[9]) if not pd.isna(row.iloc[9]) else ""
                    stope_name = extract_stope_name(stope_content)
                    if stope_name and conn:
                        current_stope_id = get_stope_id_by_name(conn, stope_name)
                continue
            
            # 检查是否为结束时间行
            if len(row) > 1 and str(row.iloc[1]) == '结束时间':
                if len(row) > 2 and not pd.isna(row.iloc[2]):
                    end_filling_time = parse_time(row.iloc[2])
                continue
            
            # 检查是否为数据行（包含时段信息）
            if len(row) > 1:
                period_str = str(row.iloc[1]) if not pd.isna(row.iloc[1]) else ""
                # 清理时段字符串
                period_str = clean_period_string(period_str)
                
                # 跳过非时段行
                if period_str not in WORKING_PERIOD_MAP:
                    continue
                
                # 检查是否有有效的数据
                if len(row) < 9:
                    continue
                
                # 获取生产料浆量和胶固粉量 - 文件6和7中位置可能不同
                slurry_volume = safe_value(row.iloc[3], 0)  # 第4列
                cement_weight = safe_value(row.iloc[5], 0)  # 第6列
                
                # 跳过数据为0的行（可选）
                if slurry_volume == 0 and cement_weight == 0:
                    skipped_rows += 1
                    continue
                
                # 获取其他字段
                filling_ratio = str(row.iloc[7]) if not pd.isna(row.iloc[7]) else None
                filling_concentration = safe_value(row.iloc[8], 0)
                
                # 获取工作时段ID
                working_period_id = WORKING_PERIOD_MAP.get(period_str, 1)
                
                # 项目部门ID设置为NULL
                project_department_id = 'NULL'
                
                # 生成SQL语句（单行格式）
                sql = f"INSERT INTO data_filling (operation_date, project_department_id, stope_id, working_period_id, slurry_volume, cement_weight, filling_ratio, filling_concentration, first_filling_time, end_filling_time, create_by, create_time) VALUES ('{current_date}', {project_department_id}, {current_stope_id}, {working_period_id}, {slurry_volume}, {cement_weight}, {f"'{filling_ratio}'" if filling_ratio else 'NULL'}, {filling_concentration}, {f"'{first_filling_time}'" if first_filling_time else 'NULL'}, {f"'{end_filling_time}'" if end_filling_time else 'NULL'}, 'system', now());"
                
                sql_statements.append(sql)
                processed_rows += 1
                
        except Exception as e:
            print(f"处理第{index+1}行时出错: {str(e)}")
            skipped_rows += 1
            continue
    
    # 关闭数据库连接
    if conn:
        conn.close()
    
    # 写入SQL文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("-- 充填数据插入语句 (文件6和7格式)\n")
            f.write(f"-- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"-- 源文件: {csv_file}\n")
            f.write(f"-- 处理行数: {processed_rows}\n")
            f.write(f"-- 跳过行数: {skipped_rows}\n\n")
            
            for sql in sql_statements:
                f.write(sql + "\n")
        
        print(f"SQL文件生成成功: {output_file}")
        print(f"总共生成 {len(sql_statements)} 条SQL语句")
        print(f"处理行数: {processed_rows}")
        print(f"跳过行数: {skipped_rows}")
        
    except Exception as e:
        print(f"写入SQL文件失败: {str(e)}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='生成充填数据的SQL插入语句 (文件6和7格式)')
    parser.add_argument('csv_file', help='CSV文件路径')
    parser.add_argument('-o', '--output', help='输出SQL文件路径', default='chongtian_dml_67.sql')
    parser.add_argument('--encoding', help='CSV文件编码', default='utf-8')
    parser.add_argument('--no-db', action='store_true', help='不连接数据库，使用默认值')
    
    args = parser.parse_args()
    
    print(f"开始处理CSV文件: {args.csv_file}")
    print(f"输出SQL文件: {args.output}")
    
    generate_sql_inserts(
        args.csv_file, 
        args.output, 
        encoding=args.encoding,
        db_connect=not args.no_db
    )
    
    print("处理完成！")
