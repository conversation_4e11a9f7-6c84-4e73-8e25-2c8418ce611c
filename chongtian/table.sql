-- 充填数据表
CREATE TABLE IF NOT EXISTS data_filling (
    id bigserial primary key,
    operation_date date NOT NULL,   -- 作业日期
    project_department_id bigint NOT NULL,   -- 项目部门ID
    stope_id bigint NOT NULL,   -- 采场ID
    working_period_id bigint NOT NULL,   -- 作业时段ID
    slurry_volume numeric(10,2),   -- 生产料浆量(m³)
    cement_weight numeric(10,2),   -- 胶固粉量(t)
    filling_ratio varchar(20),   -- 充填比例
    filling_concentration numeric(5,2),   -- 充填浓度(%)
    first_filling_time time,   -- 首冲时间
    end_filling_time time,   -- 结束时间
    remarks varchar(500),   -- 备注
    create_by varchar(64),
    create_time timestamp(6) DEFAULT now(),
    update_by varchar(64),
    update_time timestamp(6) DEFAULT now(),
    FOREIGN KEY (project_department_id) REFERENCES base_project_department(project_department_id),
    FOREIGN KEY (stope_id) REFERENCES base_stope(stope_id),
    FOREIGN KEY (working_period_id) REFERENCES base_working_period(working_period_id)
);

COMMENT ON TABLE data_filling IS '充填数据表';
COMMENT ON COLUMN data_filling.id IS '充填数据ID';
COMMENT ON COLUMN data_filling.operation_date IS '作业日期';
COMMENT ON COLUMN data_filling.project_department_id IS '项目部门ID';
COMMENT ON COLUMN data_filling.stope_id IS '采场ID';
COMMENT ON COLUMN data_filling.working_period_id IS '作业时段ID';
COMMENT ON COLUMN data_filling.slurry_volume IS '生产料浆量(m³)';
COMMENT ON COLUMN data_filling.cement_weight IS '胶固粉量(t)';
COMMENT ON COLUMN data_filling.filling_ratio IS '充填比例';
COMMENT ON COLUMN data_filling.filling_concentration IS '充填浓度(%)';
COMMENT ON COLUMN data_filling.first_filling_time IS '首冲时间';
COMMENT ON COLUMN data_filling.end_filling_time IS '结束时间';
COMMENT ON COLUMN data_filling.remarks IS '备注';
COMMENT ON COLUMN data_filling.create_by IS '创建人';
COMMENT ON COLUMN data_filling.create_time IS '创建时间';
COMMENT ON COLUMN data_filling.update_by IS '更新人';
COMMENT ON COLUMN data_filling.update_time IS '更新时间';

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_data_filling_date ON data_filling (operation_date);
CREATE INDEX IF NOT EXISTS idx_data_filling_department ON data_filling (project_department_id);
CREATE INDEX IF NOT EXISTS idx_data_filling_stope ON data_filling (stope_id);
CREATE INDEX IF NOT EXISTS idx_data_filling_period ON data_filling (working_period_id);
