create table base_stope
(
    stope_id        bigserial
        primary key,
    stope_name      varchar(64),
    working_face_id bigint,
    status          smallint,
    create_by       varchar(64),
    create_time     timestamp(6),
    update_by       varchar(64),
    update_time     timestamp(6) default now(),
    start_time      timestamp(6),
    end_time        timestamp(6),
    is_delete       integer      default 0 not null
);

comment on table base_stope is '采场配置';

comment on column base_stope.stope_id is '采场ID';

comment on column base_stope.stope_name is '采场名称';

comment on column base_stope.working_face_id is '工作面ID';

comment on column base_stope.status is '状态';

comment on column base_stope.create_by is '创建人';

comment on column base_stope.create_time is '创建时间';

comment on column base_stope.update_by is '操作人';

comment on column base_stope.update_time is '更新时间';

comment on column base_stope.start_time is '采场开始时间';

comment on column base_stope.end_time is '采场结束时间';

comment on column base_stope.is_delete is '是否删除';
