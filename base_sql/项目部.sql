create table base_project_department
(
    project_department_id   bigserial
        primary key,
    project_department_name varchar(64),
    status                  smallint,
    create_by               varchar(64),
    create_time             timestamp(6),
    update_by               varchar(64),
    update_time             timestamp(6) default now(),
    start_time              timestamp(6),
    end_time                timestamp(6),
    is_delete               integer      default 0 not null
);

comment on table base_project_department is '项目部门配置';

comment on column base_project_department.project_department_id is '项目部门ID';

comment on column base_project_department.project_department_name is '项目部门名称';

comment on column base_project_department.status is '状态';

comment on column base_project_department.create_by is '创建人';

comment on column base_project_department.create_time is '创建时间';

comment on column base_project_department.update_by is '操作人';

comment on column base_project_department.update_time is '更新时间';

comment on column base_project_department.start_time is '项目部门开始时间';

comment on column base_project_department.end_time is '项目部门结束时间';

comment on column base_project_department.is_delete is '是否删除';
