create table base_working_face
(
    working_face_id   bigserial
        primary key,
    working_face_name varchar(64),
    status            smallint,
    create_by         varchar(64),
    create_time       timestamp(6),
    update_by         varchar(64),
    update_time       timestamp(6) default now(),
    start_time        timestamp(6),
    end_time          timestamp(6),
    is_delete         integer      default 0 not null
);

comment on table base_working_face is '中段-工作面配置';

comment on column base_working_face.working_face_id is '工作面ID';

comment on column base_working_face.working_face_name is '工作面名称';

comment on column base_working_face.status is '状态';

comment on column base_working_face.create_by is '创建人';

comment on column base_working_face.create_time is '创建时间';

comment on column base_working_face.update_by is '操作人';

comment on column base_working_face.update_time is '更新时间';

comment on column base_working_face.start_time is '工作面开始时间';

comment on column base_working_face.end_time is '工作面结束时间';

comment on column base_working_face.is_delete is '是否删除';
