
-- 按日总体统计
CREATE OR REPLACE VIEW vdata_high_pressure_roll_daily_total_stats AS
SELECT 
    operation_date,
    COUNT(*) AS total_records,
    SUM(dry_ore_tons) AS total_dry_ore_tons,
    SUM(runtime_hours) AS total_runtime_hours,
    CASE WHEN SUM(runtime_hours) > 0 THEN ROUND(SUM(dry_ore_tons) / SUM(runtime_hours), 4) ELSE NULL END AS avg_unit_efficiency_tph,
    CASE WHEN (24.0 * COUNT(DISTINCT operation_date)) > 0 THEN ROUND(SUM(runtime_hours) / (24.0 * COUNT(DISTINCT operation_date)) * 100, 2) ELSE NULL END AS avg_equipment_utilization
FROM data_high_pressure_roll
GROUP BY operation_date
ORDER BY operation_date;

-- 按周总体统计（财务周：周四-周三）
CREATE OR REPLACE VIEW vdata_high_pressure_roll_weekly_total_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number,
    wk.week_start_date,
    wk.week_end_date,
    COUNT(*) AS total_records,
    SUM(dry_ore_tons) AS total_dry_ore_tons,
    SUM(runtime_hours) AS total_runtime_hours,
    CASE WHEN SUM(runtime_hours) > 0 THEN ROUND(SUM(dry_ore_tons) / SUM(runtime_hours), 4) ELSE NULL END AS avg_unit_efficiency_tph,
    CASE WHEN (24.0 * COUNT(DISTINCT hpr.operation_date)) > 0 THEN ROUND(SUM(runtime_hours) / (24.0 * COUNT(DISTINCT hpr.operation_date)) * 100, 2) ELSE NULL END AS avg_equipment_utilization
FROM data_high_pressure_roll hpr
CROSS JOIN LATERAL get_week_thu_to_wed(hpr.operation_date) AS wk
GROUP BY wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date
ORDER BY year, week_number;

-- 按月总体统计（财务月）
CREATE OR REPLACE VIEW vdata_high_pressure_roll_monthly_total_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    COUNT(*) AS total_records,
    SUM(dry_ore_tons) AS total_dry_ore_tons,
    SUM(runtime_hours) AS total_runtime_hours,
    CASE WHEN SUM(runtime_hours) > 0 THEN ROUND(SUM(dry_ore_tons) / SUM(runtime_hours), 4) ELSE NULL END AS avg_unit_efficiency_tph,
    CASE WHEN (24.0 * COUNT(DISTINCT hpr.operation_date)) > 0 THEN ROUND(SUM(runtime_hours) / (24.0 * COUNT(DISTINCT hpr.operation_date)) * 100, 2) ELSE NULL END AS avg_equipment_utilization
FROM data_high_pressure_roll hpr
CROSS JOIN LATERAL get_financial_month(hpr.operation_date) AS fm
GROUP BY fm.financial_year, fm.financial_month
ORDER BY year, month;
