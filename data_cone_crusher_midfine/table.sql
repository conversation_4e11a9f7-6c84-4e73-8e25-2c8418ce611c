-- 中细碎-圆锥破壁机
DROP TABLE IF EXISTS data_cone_crusher_midfine;

CREATE TABLE IF NOT EXISTS data_cone_crusher_midfine (
    id                      bigserial PRIMARY KEY,
    operation_date          date NOT NULL,              -- 日期
    dry_ore_tons            numeric(10,2),              -- 日干矿（t）
    runtime_hours           numeric(10,2),              -- 运行时间（小时）
    remarks                 varchar(500),               -- 备注
    create_by               varchar(64),                -- 操作人/创建人
    create_time             timestamp(6) DEFAULT now(), -- 创建时间
    update_by               varchar(64),                -- 更新人
    update_time             timestamp(6) DEFAULT now()  -- 更新时间
);

-- 表与字段注释
COMMENT ON TABLE data_cone_crusher_midfine IS '中细碎-圆锥破壁机';
COMMENT ON COLUMN data_cone_crusher_midfine.id IS '主键ID';
COMMENT ON COLUMN data_cone_crusher_midfine.operation_date IS '日期';
COMMENT ON COLUMN data_cone_crusher_midfine.dry_ore_tons IS '日干矿（t）';
COMMENT ON COLUMN data_cone_crusher_midfine.runtime_hours IS '运行时间（小时）';

COMMENT ON COLUMN data_cone_crusher_midfine.remarks IS '备注';
COMMENT ON COLUMN data_cone_crusher_midfine.create_by IS '创建人';
COMMENT ON COLUMN data_cone_crusher_midfine.create_time IS '创建时间';
COMMENT ON COLUMN data_cone_crusher_midfine.update_by IS '更新人';
COMMENT ON COLUMN data_cone_crusher_midfine.update_time IS '更新时间';

-- 唯一约束：同一日期仅一条记录
ALTER TABLE data_cone_crusher_midfine
    ADD CONSTRAINT uk_ccmf_date UNIQUE (operation_date);

-- 索引
CREATE INDEX IF NOT EXISTS idx_ccmf_date ON data_cone_crusher_midfine (operation_date);
