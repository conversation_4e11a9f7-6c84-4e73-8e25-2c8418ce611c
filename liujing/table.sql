-- 溜井运行数据表
CREATE TABLE IF NOT EXISTS data_orepass_operation
(
    id                  bigserial primary key,
    project_department_id bigint NOT NULL,          -- 项目部门ID
    ore_pass_id         bigint NOT NULL,           -- 溜井ID
    working_period_id   bigint NOT NULL,           -- 作业时段ID
    operation_date      date NOT NULL,             -- 作业日期
    trips               integer,                   -- 溜放趟数
    ore_tons            numeric(10,2),             -- 溜放矿石量（吨）
    create_by           varchar(64),
    create_time         timestamp(6) DEFAULT now(),
    update_by           varchar(64),
    update_time         timestamp(6) DEFAULT now(),
    FOREIGN KEY (working_period_id) REFERENCES base_working_period(working_period_id),
    FOREIGN KEY (project_department_id) REFERENCES base_project_department(project_department_id),
    FOREIGN KEY (ore_pass_id) REFERENCES base_ore_pass(ore_pass_id)
);

COMMENT ON TABLE data_orepass_operation IS '溜井运行数据表';
COMMENT ON COLUMN data_orepass_operation.id IS '溜井数据ID';
COMMENT ON COLUMN data_orepass_operation.project_department_id IS '项目部门ID';
COMMENT ON COLUMN data_orepass_operation.ore_pass_id IS '溜井ID';
COMMENT ON COLUMN data_orepass_operation.working_period_id IS '作业时段ID';
COMMENT ON COLUMN data_orepass_operation.operation_date IS '作业日期';
COMMENT ON COLUMN data_orepass_operation.trips IS '溜放趟数';
COMMENT ON COLUMN data_orepass_operation.ore_tons IS '溜放矿石量（吨）';
COMMENT ON COLUMN data_orepass_operation.create_by IS '创建人';
COMMENT ON COLUMN data_orepass_operation.create_time IS '创建时间';
COMMENT ON COLUMN data_orepass_operation.update_by IS '更新人';
COMMENT ON COLUMN data_orepass_operation.update_time IS '更新时间';

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_data_orepass_operation_date ON data_orepass_operation (operation_date);
CREATE INDEX IF NOT EXISTS idx_data_orepass_working_period ON data_orepass_operation (working_period_id);
CREATE INDEX IF NOT EXISTS idx_data_orepass_ore_pass ON data_orepass_operation (ore_pass_id);
CREATE INDEX IF NOT EXISTS idx_data_orepass_date_period ON data_orepass_operation (operation_date, working_period_id);
CREATE INDEX IF NOT EXISTS idx_data_orepass_department ON data_orepass_operation (project_department_id);
