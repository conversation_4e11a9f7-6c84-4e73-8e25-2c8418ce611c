-- 生成时间: 2025-07-07 22:27:35
-- 溜井名称数量: 13

-- 插入溜井基础数据
INSERT INTO base_ore_pass (ore_pass_name, create_by, update_by) VALUES ('0-2溜井', 'admin', 'admin') ON CONFLICT (ore_pass_name) DO NOTHING;
INSERT INTO base_ore_pass (ore_pass_name, create_by, update_by) VALUES ('2-1溜井', 'admin', 'admin') ON CONFLICT (ore_pass_name) DO NOTHING;
INSERT INTO base_ore_pass (ore_pass_name, create_by, update_by) VALUES ('2-2溜井', 'admin', 'admin') ON CONFLICT (ore_pass_name) DO NOTHING;
INSERT INTO base_ore_pass (ore_pass_name, create_by, update_by) VALUES ('2-3溜井', 'admin', 'admin') ON CONFLICT (ore_pass_name) DO NOTHING;
INSERT INTO base_ore_pass (ore_pass_name, create_by, update_by) VALUES ('2-4溜井', 'admin', 'admin') ON CONFLICT (ore_pass_name) DO NOTHING;
INSERT INTO base_ore_pass (ore_pass_name, create_by, update_by) VALUES ('2-8溜井', 'admin', 'admin') ON CONFLICT (ore_pass_name) DO NOTHING;
INSERT INTO base_ore_pass (ore_pass_name, create_by, update_by) VALUES ('0-4溜井', 'admin', 'admin') ON CONFLICT (ore_pass_name) DO NOTHING;
INSERT INTO base_ore_pass (ore_pass_name, create_by, update_by) VALUES ('0-5溜井', 'admin', 'admin') ON CONFLICT (ore_pass_name) DO NOTHING;
INSERT INTO base_ore_pass (ore_pass_name, create_by, update_by) VALUES ('2-5溜井', 'admin', 'admin') ON CONFLICT (ore_pass_name) DO NOTHING;
INSERT INTO base_ore_pass (ore_pass_name, create_by, update_by) VALUES ('2-6溜井', 'admin', 'admin') ON CONFLICT (ore_pass_name) DO NOTHING;
INSERT INTO base_ore_pass (ore_pass_name, create_by, update_by) VALUES ('2-9溜井', 'admin', 'admin') ON CONFLICT (ore_pass_name) DO NOTHING;
INSERT INTO base_ore_pass (ore_pass_name, create_by, update_by) VALUES ('2-10溜井', 'admin', 'admin') ON CONFLICT (ore_pass_name) DO NOTHING;