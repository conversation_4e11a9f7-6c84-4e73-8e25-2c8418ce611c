-- 溜井放矿数据视图

-- 创建视图：按日统计溜井放矿数据
CREATE OR REPLACE VIEW vdata_orepass_operation_daily_stats AS
SELECT 
    operation_date,
    SUM(trips) AS total_trips,
    ROUND(SUM(ore_tons), 2) AS total_ore_tons
FROM 
    data_orepass_operation
GROUP BY 
    operation_date
ORDER BY 
    operation_date;

-- 创建视图：按日期和时段统计溜井放矿数据
CREATE OR REPLACE VIEW vdata_orepass_operation_daily_period_stats AS
SELECT 
    o.operation_date,
    p.working_period_id,
    p.working_period_name,
    SUM(o.trips) AS total_trips,
    ROUND(SUM(o.ore_tons), 2) AS total_ore_tons
FROM 
    data_orepass_operation o
JOIN
    base_working_period p ON o.working_period_id = p.working_period_id
GROUP BY
    o.operation_date,
    p.working_period_id,
    p.working_period_name
ORDER BY 
    o.operation_date, p.working_period_id;

-- 创建视图：按日期和溜井统计放矿数据
CREATE OR REPLACE VIEW vdata_orepass_operation_daily_orepass_stats AS
SELECT 
    o.operation_date,
    o.ore_pass_id,
    op.ore_pass_name,
    SUM(o.trips) AS total_trips,
    ROUND(SUM(o.ore_tons), 2) AS total_ore_tons
FROM 
    data_orepass_operation o
JOIN
    base_ore_pass op ON o.ore_pass_id = op.ore_pass_id
GROUP BY
    o.operation_date,
    o.ore_pass_id,
    op.ore_pass_name
ORDER BY 
    o.operation_date, o.ore_pass_id;

-- 创建视图：按日期和项目部门统计放矿数据
CREATE OR REPLACE VIEW vdata_orepass_operation_daily_department_stats AS
SELECT 
    o.operation_date,
    o.project_department_id,
    pd.project_department_name,
    SUM(o.trips) AS total_trips,
    ROUND(SUM(o.ore_tons), 2) AS total_ore_tons
FROM 
    data_orepass_operation o
JOIN
    base_project_department pd ON o.project_department_id = pd.project_department_id
GROUP BY
    o.operation_date,
    o.project_department_id,
    pd.project_department_name
ORDER BY 
    o.operation_date, o.project_department_id;

-- 创建视图：按月统计溜井放矿数据（月定义为前一个月的29号到当月28号）
CREATE OR REPLACE VIEW vdata_orepass_operation_monthly_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    SUM(trips) AS total_trips,
    ROUND(SUM(ore_tons), 2) AS total_ore_tons
FROM 
    data_orepass_operation
CROSS JOIN LATERAL get_financial_month(operation_date) AS fm
GROUP BY 
    fm.financial_year, fm.financial_month
ORDER BY 
    year, month;

-- 创建视图：按月份和溜井统计放矿数据
CREATE OR REPLACE VIEW vdata_orepass_operation_monthly_orepass_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    o.ore_pass_id,
    op.ore_pass_name,
    SUM(o.trips) AS total_trips,
    ROUND(SUM(o.ore_tons), 2) AS total_ore_tons
FROM 
    data_orepass_operation o
CROSS JOIN LATERAL get_financial_month(o.operation_date) AS fm
JOIN
    base_ore_pass op ON o.ore_pass_id = op.ore_pass_id
GROUP BY 
    fm.financial_year, fm.financial_month,
    o.ore_pass_id,
    op.ore_pass_name
ORDER BY 
    year, month, o.ore_pass_id;

-- 创建视图：按月份和项目部门统计放矿数据
CREATE OR REPLACE VIEW vdata_orepass_operation_monthly_department_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    o.project_department_id,
    pd.project_department_name,
    SUM(o.trips) AS total_trips,
    ROUND(SUM(o.ore_tons), 2) AS total_ore_tons
FROM 
    data_orepass_operation o
CROSS JOIN LATERAL get_financial_month(o.operation_date) AS fm
JOIN
    base_project_department pd ON o.project_department_id = pd.project_department_id
GROUP BY 
    fm.financial_year, fm.financial_month,
    o.project_department_id,
    pd.project_department_name
ORDER BY 
    year, month, o.project_department_id;

-- 创建视图：按月份和时段统计放矿数据
CREATE OR REPLACE VIEW vdata_orepass_operation_monthly_period_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    o.working_period_id,
    wp.working_period_name,
    SUM(o.trips) AS total_trips,
    ROUND(SUM(o.ore_tons), 2) AS total_ore_tons
FROM 
    data_orepass_operation o
CROSS JOIN LATERAL get_financial_month(o.operation_date) AS fm
JOIN
    base_working_period wp ON o.working_period_id = wp.working_period_id
GROUP BY 
    fm.financial_year, fm.financial_month,
    o.working_period_id,
    wp.working_period_name
ORDER BY 
    year, month, o.working_period_id;

-- 创建视图：按周统计溜井放矿数据
CREATE OR REPLACE VIEW vdata_orepass_operation_nature_weekly_stats AS
SELECT 
    EXTRACT(YEAR FROM operation_date) AS year,
    EXTRACT(WEEK FROM operation_date) AS week_number,
    MIN(operation_date) AS week_start_date,
    MAX(operation_date) AS week_end_date,
    SUM(trips) AS total_trips,
    ROUND(SUM(ore_tons), 2) AS total_ore_tons
FROM 
    data_orepass_operation
GROUP BY 
    EXTRACT(YEAR FROM operation_date),
    EXTRACT(WEEK FROM operation_date)
ORDER BY 
    year, week_number;

-- 创建视图：按周和溜井统计放矿数据
CREATE OR REPLACE VIEW vdata_orepass_operation_nature_weekly_orepass_stats AS
SELECT 
    EXTRACT(YEAR FROM o.operation_date) AS year,
    EXTRACT(WEEK FROM o.operation_date) AS week_number,
    MIN(o.operation_date) AS week_start_date,
    MAX(o.operation_date) AS week_end_date,
    o.ore_pass_id,
    op.ore_pass_name,
    SUM(o.trips) AS total_trips,
    ROUND(SUM(o.ore_tons), 2) AS total_ore_tons
FROM 
    data_orepass_operation o
JOIN
    base_ore_pass op ON o.ore_pass_id = op.ore_pass_id
GROUP BY 
    EXTRACT(YEAR FROM o.operation_date),
    EXTRACT(WEEK FROM o.operation_date),
    o.ore_pass_id,
    op.ore_pass_name
ORDER BY 
    year, week_number, o.ore_pass_id;

-- 创建视图：按周和项目部门统计放矿数据
CREATE OR REPLACE VIEW vdata_orepass_operation_nature_weekly_department_stats AS
SELECT 
    EXTRACT(YEAR FROM o.operation_date) AS year,
    EXTRACT(WEEK FROM o.operation_date) AS week_number,
    MIN(o.operation_date) AS week_start_date,
    MAX(o.operation_date) AS week_end_date,
    o.project_department_id,
    pd.project_department_name,
    SUM(o.trips) AS total_trips,
    ROUND(SUM(o.ore_tons), 2) AS total_ore_tons
FROM 
    data_orepass_operation o
JOIN
    base_project_department pd ON o.project_department_id = pd.project_department_id
GROUP BY 
    EXTRACT(YEAR FROM o.operation_date),
    EXTRACT(WEEK FROM o.operation_date),
    o.project_department_id,
    pd.project_department_name
ORDER BY 
    year, week_number, o.project_department_id;

-- 创建视图：按周和时段统计放矿数据
CREATE OR REPLACE VIEW vdata_orepass_operation_nature_weekly_period_stats AS
SELECT 
    EXTRACT(YEAR FROM o.operation_date) AS year,
    EXTRACT(WEEK FROM o.operation_date) AS week_number,
    MIN(o.operation_date) AS week_start_date,
    MAX(o.operation_date) AS week_end_date,
    o.working_period_id,
    wp.working_period_name,
    SUM(o.trips) AS total_trips,
    ROUND(SUM(o.ore_tons), 2) AS total_ore_tons
FROM 
    data_orepass_operation o
JOIN
    base_working_period wp ON o.working_period_id = wp.working_period_id
GROUP BY 
    EXTRACT(YEAR FROM o.operation_date),
    EXTRACT(WEEK FROM o.operation_date),
    o.working_period_id,
    wp.working_period_name
ORDER BY 
    year, week_number, o.working_period_id;

-- 创建视图：按周统计溜井放矿数据（周四到周三为一周）
CREATE OR REPLACE VIEW vdata_orepass_operation_weekly_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number,
    wk.week_start_date,
    wk.week_end_date,
    SUM(trips) AS total_trips,
    ROUND(SUM(ore_tons), 2) AS total_ore_tons
FROM 
    data_orepass_operation
CROSS JOIN LATERAL get_week_thu_to_wed(operation_date) AS wk
GROUP BY 
    wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date
ORDER BY 
    year, week_number;

-- 创建视图：按周和溜井统计放矿数据（周四到周三为一周）
CREATE OR REPLACE VIEW vdata_orepass_operation_weekly_orepass_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number,
    wk.week_start_date,
    wk.week_end_date,
    o.ore_pass_id,
    op.ore_pass_name,
    SUM(o.trips) AS total_trips,
    ROUND(SUM(o.ore_tons), 2) AS total_ore_tons
FROM 
    data_orepass_operation o
CROSS JOIN LATERAL get_week_thu_to_wed(o.operation_date) AS wk
JOIN
    base_ore_pass op ON o.ore_pass_id = op.ore_pass_id
GROUP BY 
    wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date,
    o.ore_pass_id,
    op.ore_pass_name
ORDER BY 
    year, week_number, o.ore_pass_id;

-- 创建视图：按周和项目部门统计放矿数据（周四到周三为一周）
CREATE OR REPLACE VIEW vdata_orepass_operation_weekly_department_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number,
    wk.week_start_date,
    wk.week_end_date,
    o.project_department_id,
    pd.project_department_name,
    SUM(o.trips) AS total_trips,
    ROUND(SUM(o.ore_tons), 2) AS total_ore_tons
FROM 
    data_orepass_operation o
CROSS JOIN LATERAL get_week_thu_to_wed(o.operation_date) AS wk
JOIN
    base_project_department pd ON o.project_department_id = pd.project_department_id
GROUP BY 
    wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date,
    o.project_department_id,
    pd.project_department_name
ORDER BY 
    year, week_number, o.project_department_id;

-- 创建视图：按周和时段统计放矿数据（周四到周三为一周）
CREATE OR REPLACE VIEW vdata_orepass_operation_weekly_period_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number,
    wk.week_start_date,
    wk.week_end_date,
    o.working_period_id,
    wp.working_period_name,
    SUM(o.trips) AS total_trips,
    ROUND(SUM(o.ore_tons), 2) AS total_ore_tons
FROM 
    data_orepass_operation o
CROSS JOIN LATERAL get_week_thu_to_wed(o.operation_date) AS wk
JOIN
    base_working_period wp ON o.working_period_id = wp.working_period_id
GROUP BY 
    wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date,
    o.working_period_id,
    wp.working_period_name
ORDER BY 
    year, week_number, o.working_period_id;

-- 创建视图：按年统计溜井放矿数据（年定义为上一年12月29号到当年12月28号）
CREATE OR REPLACE VIEW vdata_orepass_operation_yearly_stats AS
SELECT 
    fy.financial_year AS year,
    SUM(trips) AS total_trips,
    ROUND(SUM(ore_tons), 2) AS total_ore_tons
FROM 
    data_orepass_operation
CROSS JOIN LATERAL get_financial_year(operation_date) AS fy
GROUP BY 
    fy.financial_year
ORDER BY 
    year;

-- 创建视图：按年份和溜井统计放矿数据
CREATE OR REPLACE VIEW vdata_orepass_operation_yearly_orepass_stats AS
SELECT 
    fy.financial_year AS year,
    o.ore_pass_id,
    op.ore_pass_name,
    SUM(o.trips) AS total_trips,
    ROUND(SUM(o.ore_tons), 2) AS total_ore_tons
FROM 
    data_orepass_operation o
CROSS JOIN LATERAL get_financial_year(o.operation_date) AS fy
JOIN
    base_ore_pass op ON o.ore_pass_id = op.ore_pass_id
GROUP BY 
    fy.financial_year,
    o.ore_pass_id,
    op.ore_pass_name
ORDER BY 
    year, o.ore_pass_id;

-- 创建视图：按年份和项目部门统计放矿数据
CREATE OR REPLACE VIEW vdata_orepass_operation_yearly_department_stats AS
SELECT 
    fy.financial_year AS year,
    o.project_department_id,
    pd.project_department_name,
    SUM(o.trips) AS total_trips,
    ROUND(SUM(o.ore_tons), 2) AS total_ore_tons
FROM 
    data_orepass_operation o
CROSS JOIN LATERAL get_financial_year(o.operation_date) AS fy
JOIN
    base_project_department pd ON o.project_department_id = pd.project_department_id
GROUP BY 
    fy.financial_year,
    o.project_department_id,
    pd.project_department_name
ORDER BY 
    year, o.project_department_id;

-- 创建视图：按年份和时段统计放矿数据
CREATE OR REPLACE VIEW vdata_orepass_operation_yearly_period_stats AS
SELECT 
    fy.financial_year AS year,
    o.working_period_id,
    wp.working_period_name,
    SUM(o.trips) AS total_trips,
    ROUND(SUM(o.ore_tons), 2) AS total_ore_tons
FROM 
    data_orepass_operation o
CROSS JOIN LATERAL get_financial_year(o.operation_date) AS fy
JOIN
    base_working_period wp ON o.working_period_id = wp.working_period_id
GROUP BY 
    fy.financial_year,
    o.working_period_id,
    wp.working_period_name
ORDER BY 
    year, o.working_period_id;