-- 插入溜井放矿数据到PostgreSQL数据库
-- 生成日期: 2025-07-18 21:49:40
-- 自动从数据库查询溜井ID

-- 清空现有数据（如果需要）
-- TRUNCATE TABLE data_orepass_operation CASCADE;

-- 插入溜井放矿数据
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-01-01', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-01-01', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-01-01', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-01-01', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-01-01', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-01-01', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-01-01', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-01-01', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-01-01', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-01-01', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-01-01', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-01-01', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-01-01', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-01-01', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-01-01', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-01-01', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-01-01', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-01-01', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-01-01', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-01-01', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-01-01', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-01-01', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-01-02', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-01-02', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-01-02', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-01-02', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-01-02', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-01-02', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-01-02', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-01-02', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-01-02', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-01-02', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-01-02', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-01-02', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-01-02', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-01-02', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-01-02', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-01-02', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-01-02', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-01-02', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-01-02', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-01-02', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-01-02', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-01-02', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-01-03', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-01-03', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-01-03', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-01-03', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-01-03', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-01-03', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-01-03', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-01-03', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-01-03', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-01-03', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-01-03', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-01-03', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-01-03', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-01-03', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-01-03', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-01-03', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-01-03', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-01-03', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-01-03', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-01-03', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-01-03', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-01-03', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-01-03', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-01-03', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-01-03', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-01-04', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-01-04', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-01-04', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-01-04', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-01-04', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-01-04', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-01-04', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-01-04', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-01-04', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-01-04', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-01-04', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-01-04', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-01-04', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-01-04', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-01-04', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-01-04', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-01-04', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-01-04', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-01-04', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-01-04', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-01-04', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-01-04', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-01-04', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-01-05', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-01-05', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-01-05', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-01-05', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-01-05', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-01-05', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-01-05', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 3, '2025-01-05', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-01-05', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-01-05', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-01-05', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-01-05', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-01-05', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-01-05', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-01-05', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-01-05', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-01-05', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-01-05', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-01-05', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-01-05', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-01-05', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-01-05', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-01-05', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-01-05', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-01-05', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-01-06', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-01-06', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-01-06', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-01-06', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-01-06', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-01-06', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-01-06', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-01-06', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-01-06', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-01-06', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-01-06', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-01-06', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-01-06', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-01-06', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-01-06', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-01-06', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-01-06', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-01-06', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-01-06', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-01-06', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-01-06', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-01-06', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-01-07', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-01-07', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-01-07', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-01-07', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-01-07', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-01-07', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-01-07', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-01-07', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-01-07', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-01-07', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-01-07', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-01-07', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-01-07', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-01-07', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-01-07', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-01-07', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-01-07', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-01-07', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-01-07', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-01-07', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-01-07', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-01-07', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-01-08', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-01-08', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-01-08', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-01-08', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-01-08', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-01-08', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-01-08', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-01-08', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-01-08', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-01-08', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-01-08', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-01-08', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-01-08', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-01-08', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-01-08', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-01-08', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-01-08', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-01-08', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-01-08', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-01-08', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-01-08', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-01-08', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-01-08', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-01-08', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-01-09', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-01-09', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-01-09', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-01-09', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-01-09', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-01-09', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-01-09', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-01-09', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-01-09', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-01-09', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-01-09', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-01-09', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-01-09', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-01-09', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-01-09', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-01-09', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-01-09', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-01-09', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-01-09', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-01-09', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-01-09', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-01-09', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-01-09', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-01-09', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-01-09', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-01-10', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-01-10', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-01-10', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-01-10', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-01-10', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-01-10', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-01-10', 14, 2800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-01-10', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-01-10', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-01-10', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-01-10', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-01-10', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-01-10', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-01-10', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-01-10', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-01-10', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-01-10', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-01-10', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-01-10', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-01-10', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-01-10', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-01-10', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-01-10', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-01-11', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-01-11', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-01-11', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-01-11', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-01-11', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-01-11', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-01-11', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-01-11', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-01-11', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-01-11', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-01-11', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-01-11', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-01-11', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-01-11', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-01-11', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-01-11', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-01-11', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-01-11', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-01-11', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-01-11', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-01-11', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-01-11', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-01-11', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-01-11', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-01-11', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-01-11', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-01-11', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-01-12', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-01-12', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-01-12', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-01-12', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-01-12', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-01-12', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-01-12', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-01-12', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-01-12', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-01-12', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-01-12', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-01-12', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-01-12', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-01-12', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-01-12', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-01-12', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-01-12', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-01-12', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-01-12', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-01-12', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-01-12', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-01-13', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-01-13', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-01-13', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-01-13', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-01-13', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-01-13', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-01-13', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-01-13', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-01-13', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-01-13', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 3, '2025-01-13', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-01-13', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-01-13', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-01-13', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-01-13', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-01-13', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-01-13', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-01-13', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-01-13', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-01-13', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-01-13', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-01-13', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-01-14', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-01-14', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-01-14', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-01-14', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-01-14', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-01-14', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-01-14', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-01-14', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-01-14', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-01-14', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 3, '2025-01-14', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-01-14', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-01-14', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-01-14', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-01-14', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-01-14', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-01-14', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-01-14', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-01-14', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-01-14', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-01-14', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-01-14', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-01-14', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-01-14', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-01-14', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-01-14', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-01-14', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-01-14', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-01-15', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-01-15', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-01-15', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-01-15', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-01-15', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-01-15', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-01-15', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-01-15', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-01-15', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-01-15', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-01-15', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-01-15', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-01-15', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-01-15', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-01-15', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-01-15', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-01-15', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-01-15', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-01-15', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-01-15', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-01-15', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-01-15', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-01-15', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-01-15', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-01-15', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-01-16', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-01-16', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-01-16', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-01-16', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-01-16', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-01-16', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-01-16', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 3, '2025-01-16', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-01-16', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-01-16', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-01-16', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-01-16', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-01-16', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-01-16', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-01-16', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-01-16', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-01-16', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-01-16', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-01-16', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-01-16', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-01-16', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-01-16', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-01-16', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-01-16', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-01-16', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-01-17', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-01-17', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-01-17', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-01-17', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-01-17', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-01-17', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-01-17', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 3, '2025-01-17', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-01-17', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-01-17', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-01-17', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-01-17', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-01-17', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-01-17', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-01-17', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-01-17', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-01-17', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-01-17', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-01-17', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-01-17', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-01-17', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-01-17', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-01-17', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-01-17', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-01-17', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-01-17', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-01-18', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-01-18', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-01-18', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-01-18', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-01-18', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-01-18', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-01-18', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-01-18', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-01-18', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-01-18', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-01-18', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-01-18', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-01-18', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-01-18', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-01-18', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-01-18', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-01-18', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-01-18', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-01-18', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-01-18', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-01-18', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-01-18', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-01-18', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-01-18', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-01-18', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-01-18', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-01-18', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-01-19', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-01-19', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-01-19', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-01-19', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-01-19', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 3, '2025-01-19', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-01-19', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-01-19', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-01-19', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-01-19', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-01-19', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-01-19', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-01-19', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-01-19', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-01-19', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-01-19', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-01-19', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-01-19', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-01-19', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-01-19', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-01-19', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-01-20', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-01-20', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-01-20', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-01-20', 14, 2800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-01-20', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-01-20', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-01-20', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-01-20', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-01-20', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 1, '2025-01-20', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-01-20', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-01-20', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-01-20', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-01-20', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-01-20', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-01-20', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-01-20', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-01-20', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-01-20', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-01-20', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-01-20', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-01-21', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-01-21', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-01-21', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-01-21', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-01-21', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-01-21', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-01-21', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-01-21', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-01-21', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 3, '2025-01-21', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-01-21', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-01-21', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-01-21', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-01-21', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-01-21', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-01-21', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-01-21', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-01-21', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-01-21', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-01-21', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-01-21', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-01-21', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-01-21', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-01-21', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-01-21', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-01-21', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-01-21', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-01-22', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-01-22', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-01-22', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-01-22', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-01-22', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-01-22', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-01-22', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-01-22', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 3, '2025-01-22', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-01-22', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-01-22', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-01-22', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 2, '2025-01-22', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-01-22', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-01-22', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-01-22', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-01-22', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-01-22', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-01-22', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-01-22', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-01-22', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-01-22', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-01-22', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-01-22', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-01-22', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-01-22', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-01-23', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-01-23', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-01-23', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-01-23', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-01-23', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-01-23', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-01-23', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-01-23', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-01-23', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-01-23', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-01-23', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-01-23', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-01-23', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-01-23', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-01-23', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-01-23', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-01-23', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-01-23', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-01-23', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-01-23', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-01-23', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-01-23', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-01-24', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-01-24', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-01-24', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-01-24', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-01-24', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-01-24', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-01-24', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-01-24', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-01-24', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 1, '2025-01-24', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-01-24', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-01-24', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-01-24', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-01-24', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-01-24', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-01-24', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-01-24', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-01-24', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-01-24', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-01-24', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-01-24', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-01-24', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-01-24', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-01-24', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-01-24', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-01-25', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-01-25', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-01-25', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-01-25', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-01-25', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-01-25', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-01-25', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-01-25', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-01-25', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-01-25', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-01-25', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-01-25', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 2, '2025-01-25', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-01-25', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-01-25', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-01-25', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-01-25', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-01-25', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-01-25', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-01-25', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-01-25', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-01-25', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-01-25', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-01-25', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-01-25', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-01-25', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-01-25', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-01-25', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-01-26', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-01-26', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-01-26', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-01-26', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-01-26', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-01-26', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-01-26', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-01-26', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-01-26', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-01-26', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-01-26', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-01-26', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 3, '2025-01-26', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-01-26', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-01-26', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-01-26', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-01-26', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-01-26', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-01-26', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-01-26', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-01-26', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-01-26', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-01-26', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-01-26', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-01-26', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-01-26', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-01-26', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-01-26', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-01-27', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-01-27', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-01-27', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-01-27', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-01-27', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-01-27', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-01-27', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-01-27', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-01-27', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-01-27', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 3, '2025-01-27', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-01-27', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-01-27', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-01-27', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-01-27', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-01-27', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-01-27', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-01-27', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-01-27', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-01-27', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-01-27', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-01-27', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-01-27', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-01-27', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-01-27', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-01-27', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-01-28', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-01-28', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-01-28', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-01-28', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-01-28', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-01-28', 2, 400.0, 'admin', 'admin');