#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
生成SQL插入语句脚本
从CSV文件生成PostgreSQL数据库的INSERT语句 - 溜井放矿数据
自动从数据库查询溜井ID
"""

import pandas as pd
import re
from datetime import datetime
import os
import argparse
from decimal import Decimal, getcontext
import psycopg2
from psycopg2 import sql

# 设置decimal精度
getcontext().prec = 10  # 设置精度为10位

def get_working_period_id(time_str):
    """根据时间段获取对应的工作时段ID"""
    if not isinstance(time_str, str):
        return None
    
    if '0-8' in time_str:
        return 1  # 早班
    elif '8-20' in time_str:
        return 2  # 白班
    elif '20-24' in time_str or '20-0' in time_str:
        return 3  # 夜班
    return None

def parse_date(date_str):
    """解析中文日期格式，转换为标准日期格式"""
    if not isinstance(date_str, str):
        return None
    
    try:
        # 提取月和日
        month_match = re.search(r'(\d+)月', date_str)
        day_match = re.search(r'(\d+)日', date_str)
        
        if month_match and day_match:
            month = int(month_match.group(1))
            day = int(day_match.group(1))
            # 假设年份是2025年
            return f"2025-{month:02d}-{day:02d}"
    except Exception as e:
        print(f"日期解析错误: {date_str}, 错误: {e}")
    
    return None

def safe_value(value, default=0, as_decimal=True):
    """安全获取值，处理NaN和None，并确保返回数字类型
    参数:
        value: 要转换的值
        default: 默认值，当转换失败时返回
        as_decimal: 是否返回Decimal类型，如果为False则返回float
    """
    if pd.isna(value) or value is None:
        return Decimal(default) if as_decimal else default
    
    # 尝试转换为数字
    try:
        # 如果是字符串，尝试转换
        if isinstance(value, str):
            # 移除可能的逗号、空格等
            value = value.replace(',', '').strip()
            if value == '':
                return Decimal(default) if as_decimal else default
            return Decimal(value) if as_decimal else float(value)
        # 如果已经是数字类型，转换为Decimal或直接返回
        elif isinstance(value, (int, float)):
            return Decimal(str(value)) if as_decimal else value
        # 其他情况，尝试强制转换
        else:
            return Decimal(str(value)) if as_decimal else float(value)
    except (ValueError, TypeError):
        print(f"警告: 无法将值 '{value}' ({type(value)}) 转换为数字，使用默认值 {default}")
        return Decimal(default) if as_decimal else default

def connect_to_database(host='**********', port=5432, dbname='lxbi', user='postgres', password='admin321.'):
    """连接到PostgreSQL数据库"""
    try:
        conn = psycopg2.connect(
            host=host,
            port=port,
            dbname=dbname,
            user=user,
            password=password
        )
        print(f"成功连接到数据库: {dbname}@{host}:{port}")
        return conn
    except Exception as e:
        print(f"连接数据库失败: {e}")
        return None

def get_ore_pass_id(conn, orepass_name):
    """从base_ore_pass表中查询溜井ID"""
    if not conn:
        print("数据库连接不可用，无法查询溜井ID")
        return None
        
    try:
        with conn.cursor() as cursor:
            # 使用模糊匹配查询溜井ID
            cursor.execute(
                "SELECT ore_pass_id, ore_pass_name FROM base_ore_pass WHERE ore_pass_name LIKE %s",
                [f"%{orepass_name}%"]
            )
            result = cursor.fetchone()
            
            if result:
                ore_pass_id, full_name = result
                print(f"找到溜井ID: {ore_pass_id} 对应名称: {full_name}")
                return ore_pass_id
            else:
                print(f"警告: 未找到溜井名称 '{orepass_name}' 对应的ID")
                return None
    except Exception as e:
        print(f"查询溜井ID时出错: {e}")
        return None

def map_department_name(department_name):
    """映射部门名称到数据库中的名称"""
    if not department_name:
        return None
        
    # 部门名称映射表
    department_mapping = {
        "涟邵项目部": "涟邵建工",
        "中矿项目部": "中矿建设",
        "采运作业区": "自营"
    }
    
    # 检查是否需要映射
    for key, value in department_mapping.items():
        if key in department_name:
            print(f"映射部门名称: '{department_name}' -> '{value}'")
            return value
    
    # 如果没有匹配项，返回原始名称
    return department_name

def get_project_department_id(conn, department_name):
    """从base_project_department表中查询项目部门ID"""
    if not conn:
        print("数据库连接不可用，无法查询项目部门ID")
        return None
        
    # 映射部门名称
    mapped_name = map_department_name(department_name)
    if not mapped_name:
        return None
        
    try:
        with conn.cursor() as cursor:
            # 使用模糊匹配查询项目部门ID
            cursor.execute(
                "SELECT project_department_id, project_department_name FROM base_project_department WHERE project_department_name LIKE %s",
                [f"%{mapped_name}%"]
            )
            result = cursor.fetchone()
            
            if result:
                dept_id, full_name = result
                print(f"找到项目部门ID: {dept_id} 对应名称: {full_name}")
                return dept_id
            else:
                print(f"警告: 未找到项目部门名称 '{mapped_name}' (原名: '{department_name}') 对应的ID")
                return None
    except Exception as e:
        print(f"查询项目部门ID时出错: {e}")
        return None

def generate_sql_inserts(csv_file, output_file=None, encoding='utf-8', db_connect=True):
    """从CSV文件生成SQL插入语句"""
    print(f"读取CSV文件: {csv_file}")
    
    try:
        # 连接数据库
        conn = None
        if db_connect:
            conn = connect_to_database()
            if not conn:
                print("警告: 无法连接到数据库，将使用默认ID值")
        
        # 读取CSV文件，不使用第一行作为列名
        df = pd.read_csv(csv_file, header=None, encoding=encoding)
        
        print("分析CSV文件结构...")
        
        # 初始化处理数据列表
        generate_sql_inserts.processed_data_list = []
        
        processed_rows = 0
        skipped_rows = 0
        
        # 用于向前填充
        current_date = None
        current_department = None
        
        # 遍历CSV数据
        for index, row in df.iterrows():
            # 跳过标题行和空行
            if index == 0 or pd.isna(row[0]) or not isinstance(row[0], str):
                skipped_rows += 1
                continue
                
            # 处理日期
            if isinstance(row[0], str) and '月' in row[0] and '日' in row[0]:
                current_date = parse_date(row[0])
                
                # 跳过标题行（如"四、电机车溜井放矿情况"）
                if '溜井放矿情况' in str(row[1]):
                    skipped_rows += 1
                    continue
                    
                # 跳过表头行（如"项目部,放矿溜井,0-8时,8-20时,20-24时,合计趟数,t"）
                if '项目部' in str(row[1]) and '放矿溜井' in str(row[3]):
                    skipped_rows += 1
                    continue
            
            # 如果没有有效的日期，跳过该行
            if not current_date:
                skipped_rows += 1
                continue
            
            # 处理部门名称
            if not pd.isna(row[1]) and isinstance(row[1], str) and row[1].strip():
                current_department = row[1].strip()
            
            # 获取溜井名称
            orepass_name = row[3] if not pd.isna(row[3]) and isinstance(row[3], str) else None
            
            # 如果没有溜井名称，跳过该行
            if not orepass_name or '溜井' not in orepass_name:
                skipped_rows += 1
                continue
            
            # 处理三个时段的数据
            for period_col, period_name in [(4, '0-8时'), (6, '8-20时'), (7, '20-24时')]:
                # 获取趟数
                trips = safe_value(row[period_col], 0, False)
                
                # 如果趟数为0，跳过该时段
                if trips == 0:
                    continue
                
                # 获取工作时段ID
                working_period_id = get_working_period_id(period_name)
                
                # 计算矿石量（每趟200吨）
                ore_tons = trips * 200
                
                # 存储处理后的数据
                processed_data = {
                    'department': current_department,
                    'orepass_name': orepass_name,
                    'working_period_id': working_period_id,
                    'operation_date': current_date,
                    'trips': int(trips),
                    'ore_tons': float(ore_tons)
                }
                
                # 添加到处理过的数据列表
                generate_sql_inserts.processed_data_list.append(processed_data)
                processed_rows += 1
        
        # 生成SQL插入语句
        data_inserts = []
        
        # 缓存已查询过的溜井ID和部门ID，避免重复查询
        ore_pass_id_cache = {}
        department_id_cache = {}
        
        # 生成数据插入语句 - 每行数据一条SQL语句
        for data in generate_sql_inserts.processed_data_list:
            orepass_name = data['orepass_name']
            department_name = data['department']
            
            # 查询溜井ID
            ore_pass_id = None
            if orepass_name in ore_pass_id_cache:
                ore_pass_id = ore_pass_id_cache[orepass_name]
            elif conn:
                ore_pass_id = get_ore_pass_id(conn, orepass_name)
                ore_pass_id_cache[orepass_name] = ore_pass_id
            
            # 查询项目部门ID
            project_department_id = None
            if department_name in department_id_cache:
                project_department_id = department_id_cache[department_name]
            elif conn and department_name:
                project_department_id = get_project_department_id(conn, department_name)
                department_id_cache[department_name] = project_department_id
            
            # 如果没有找到溜井ID或部门ID，跳过该条记录或使用默认值
            if ore_pass_id is None:
                print(f"警告: 跳过记录，未找到溜井 '{orepass_name}' 对应的ID")
                continue
                
            if project_department_id is None:
                print(f"警告: 未找到部门 '{department_name}' 对应的ID，使用默认值1")
                project_department_id = 1  # 使用默认值
            
            # 生成插入语句
            data_inserts.append(f"""
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
({project_department_id}, {ore_pass_id}, {data['working_period_id']}, '{data['operation_date']}', {data['trips']}, {data['ore_tons']}, 'admin', 'admin');""".strip())
        
        # 组合所有SQL语句
        all_inserts = [
            "-- 插入溜井放矿数据到PostgreSQL数据库",
            f"-- 生成日期: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "-- 自动从数据库查询溜井ID",
            "",
            "-- 清空现有数据（如果需要）",
            "-- TRUNCATE TABLE data_orepass_operation CASCADE;",
            "",
            "-- 插入溜井放矿数据",
            *data_inserts
        ]
        
        # 关闭数据库连接
        if conn:
            conn.close()
        
        sql_content = '\n'.join(all_inserts)
        
        # 如果指定了输出文件，写入文件
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(sql_content)
            print(f"SQL插入语句已写入文件: {output_file}")
        else:
            print(sql_content)
        
        print(f"处理完成: 处理了 {processed_rows} 行数据，跳过了 {skipped_rows} 行")
        
    except Exception as e:
        print(f"生成SQL插入语句时出错: {e}")

def main():
    parser = argparse.ArgumentParser(description='从CSV文件生成SQL插入语句')
    parser.add_argument('csv_file', help='CSV文件路径')
    parser.add_argument('-o', '--output', help='输出SQL文件路径')
    parser.add_argument('--encoding', default='utf-8', help='CSV文件编码，默认为utf-8')
    parser.add_argument('--no-db', action='store_true', help='不连接数据库，使用默认ID值')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.csv_file):
        print(f"错误: 找不到CSV文件 '{args.csv_file}'")
        return
    
    generate_sql_inserts(args.csv_file, args.output, args.encoding, not args.no_db)

if __name__ == "__main__":
    main()
