-- 插入溜井放矿数据到PostgreSQL数据库
-- 生成日期: 2025-07-18 21:48:22
-- 自动从数据库查询溜井ID

-- 清空现有数据（如果需要）
-- TRUNCATE TABLE data_orepass_operation CASCADE;

-- 插入溜井放矿数据
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-04-29', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-04-29', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-04-29', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-04-29', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-04-29', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-04-29', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-04-29', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-04-29', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-04-29', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-04-29', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-04-29', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-04-29', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-04-29', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-04-29', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-04-29', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-04-29', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-04-29', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-04-30', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-04-30', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-04-30', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-04-30', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-04-30', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-04-30', 15, 3000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-04-30', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-04-30', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-04-30', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-04-30', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-04-30', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-04-30', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-04-30', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-04-30', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-04-30', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-04-30', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-04-30', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-04-30', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-04-30', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-04-30', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-04-30', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-04-30', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-05-01', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-05-01', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-05-01', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-05-01', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-05-01', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-05-01', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-05-01', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-05-01', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-05-01', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-05-01', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-05-01', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-05-01', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 3, '2025-05-01', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-05-01', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-05-01', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-05-01', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-05-01', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-05-01', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-05-01', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-05-01', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-05-01', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-05-01', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-05-01', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-05-01', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-05-01', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-05-01', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-05-01', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-05-02', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-05-02', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-05-02', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-05-02', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-05-02', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-05-02', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-05-02', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-05-02', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-05-02', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-05-02', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-05-02', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-05-02', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 1, '2025-05-02', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-05-02', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-05-02', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-05-02', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-05-02', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-05-02', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-05-02', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-05-02', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-05-02', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-05-02', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-05-02', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-05-02', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-05-02', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-05-03', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-05-03', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-05-03', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-05-03', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-05-03', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-05-03', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-05-03', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-05-03', 18, 3600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-05-03', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-05-03', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-05-03', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-05-03', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 1, '2025-05-03', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 3, '2025-05-03', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-05-03', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-05-03', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-05-03', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-05-03', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-05-03', 14, 2800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-05-03', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-05-03', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-05-03', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-05-03', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-05-03', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-05-03', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-05-03', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-05-04', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-05-04', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-05-04', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-05-04', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-05-04', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-05-04', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-05-04', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-05-04', 15, 3000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-05-04', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 3, '2025-05-04', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-05-04', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-05-04', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-05-04', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-05-04', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-05-04', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-05-04', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-05-04', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-05-04', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-05-04', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-05-04', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-05-04', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-05-05', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-05-05', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-05-05', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-05-05', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-05-05', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-05-05', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-05-05', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-05-05', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-05-05', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-05-05', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-05-05', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 2, '2025-05-05', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-05-05', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-05-05', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-05-05', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-05-05', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-05-05', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-05-05', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-05-05', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-05-05', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-05-05', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-05-05', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-05-05', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-05-06', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-05-06', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-05-06', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-05-06', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-05-06', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-05-06', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-05-06', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-05-06', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-05-06', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-05-06', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-05-06', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-05-06', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 1, '2025-05-06', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 2, '2025-05-06', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-05-06', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-05-06', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-05-06', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-05-06', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-05-06', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-05-06', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-05-06', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-05-06', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-05-06', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-05-06', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-05-06', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-05-07', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-05-07', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-05-07', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-05-07', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-05-07', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-05-07', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-05-07', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-05-07', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-05-07', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-05-07', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-05-07', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-05-07', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-05-07', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-05-07', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-05-07', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-05-07', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-05-07', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-05-07', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-05-07', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-05-07', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-05-07', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-05-08', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-05-08', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-05-08', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-05-08', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-05-08', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-05-08', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-05-08', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-05-08', 16, 3200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-05-08', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-05-08', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-05-08', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-05-08', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-05-08', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 2, '2025-05-08', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-05-08', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-05-08', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-05-08', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-05-08', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-05-08', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-05-08', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-05-08', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-05-08', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-05-08', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-05-08', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-05-08', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-05-09', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-05-09', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-05-09', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-05-09', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-05-09', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-05-09', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-05-09', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-05-09', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-05-09', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-05-09', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-05-09', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-05-09', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-05-09', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 1, '2025-05-09', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-05-09', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-05-09', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-05-09', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-05-09', 14, 2800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-05-09', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-05-09', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-05-09', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-05-09', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-05-09', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-05-09', 14, 2800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-05-09', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-05-10', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-05-10', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-05-10', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-05-10', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-05-10', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-05-10', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-05-10', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-05-10', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-05-10', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-05-10', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-05-10', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 2, '2025-05-10', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 3, '2025-05-10', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-05-10', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-05-10', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-05-10', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-05-10', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-05-10', 14, 2800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-05-10', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-05-10', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-05-10', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-05-10', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-05-10', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-05-10', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-05-11', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-05-11', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-05-11', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-05-11', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-05-11', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-05-11', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-05-11', 17, 3400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-05-11', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-05-11', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-05-11', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 2, '2025-05-11', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 3, '2025-05-11', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-05-11', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-05-11', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-05-11', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-05-11', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-05-11', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-05-11', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-05-11', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-05-11', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-05-11', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-05-11', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-05-11', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-05-11', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-05-12', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-05-12', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-05-12', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-05-12', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-05-12', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-05-12', 14, 2800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-05-12', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-05-12', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-05-12', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-05-12', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-05-12', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-05-12', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 3, '2025-05-12', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-05-12', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-05-12', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-05-12', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-05-12', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-05-12', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-05-12', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-05-12', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-05-12', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-05-12', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-05-12', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-05-12', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-05-13', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-05-13', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-05-13', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-05-13', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-05-13', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-05-13', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-05-13', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-05-13', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-05-13', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-05-13', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-05-13', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-05-13', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-05-13', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 2, '2025-05-13', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-05-13', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-05-13', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-05-13', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-05-13', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-05-13', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-05-13', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-05-13', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-05-13', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-05-13', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-05-13', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-05-13', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-05-13', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-05-14', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-05-14', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-05-14', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-05-14', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-05-14', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-05-14', 17, 3400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-05-14', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 3, '2025-05-14', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-05-14', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-05-14', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-05-14', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-05-14', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-05-14', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-05-14', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-05-14', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-05-14', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-05-14', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-05-14', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-05-14', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-05-14', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-05-14', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-05-14', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-05-14', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-05-14', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-05-14', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-05-14', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-05-15', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-05-15', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-05-15', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-05-15', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-05-15', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-05-15', 17, 3400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-05-15', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-05-15', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-05-15', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-05-15', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-05-15', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-05-15', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-05-15', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-05-15', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-05-15', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-05-15', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-05-15', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-05-15', 14, 2800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-05-15', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-05-15', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-05-15', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-05-15', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-05-15', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-05-15', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-05-15', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-05-16', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-05-16', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-05-16', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-05-16', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-05-16', 14, 2800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-05-16', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-05-16', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-05-16', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-05-16', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-05-16', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-05-16', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-05-16', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-05-16', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-05-16', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-05-16', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-05-16', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-05-16', 14, 2800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-05-16', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-05-16', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-05-16', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-05-16', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-05-16', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-05-16', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-05-17', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-05-17', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-05-17', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-05-17', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-05-17', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-05-17', 15, 3000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-05-17', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-05-17', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-05-17', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-05-17', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-05-17', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-05-17', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-05-17', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-05-17', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-05-17', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-05-17', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-05-17', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-05-17', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-05-17', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-05-17', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-05-17', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-05-17', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-05-17', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-05-17', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-05-17', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-05-18', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-05-18', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-05-18', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-05-18', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-05-18', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-05-18', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-05-18', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-05-18', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-05-18', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-05-18', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-05-18', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-05-18', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-05-18', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-05-18', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-05-18', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-05-18', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-05-18', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-05-18', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-05-18', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-05-18', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-05-18', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-05-18', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-05-18', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-05-18', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-05-18', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-05-18', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-05-18', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-05-19', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-05-19', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-05-19', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-05-19', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-05-19', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-05-19', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-05-19', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-05-19', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-05-19', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 3, '2025-05-19', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-05-19', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-05-19', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-05-19', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-05-19', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-05-19', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-05-19', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-05-19', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-05-19', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-05-19', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-05-19', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-05-19', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-05-19', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-05-19', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-05-19', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-05-19', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-05-19', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-05-19', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-05-20', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-05-20', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-05-20', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-05-20', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-05-20', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-05-20', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-05-20', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-05-20', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-05-20', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 3, '2025-05-20', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-05-20', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-05-20', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-05-20', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-05-20', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-05-20', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 2, '2025-05-20', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-05-20', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-05-20', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-05-20', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-05-20', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-05-20', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-05-20', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-05-20', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-05-20', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-05-20', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-05-20', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-05-20', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-05-20', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-05-20', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-05-21', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-05-21', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-05-21', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-05-21', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-05-21', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-05-21', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-05-21', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-05-21', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-05-21', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-05-21', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-05-21', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-05-21', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-05-21', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-05-21', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-05-21', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-05-21', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-05-21', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-05-21', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-05-21', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-05-21', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-05-21', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-05-21', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-05-21', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-05-21', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-05-21', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-05-21', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-05-22', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-05-22', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-05-22', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-05-22', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-05-22', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-05-22', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-05-22', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-05-22', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-05-22', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-05-22', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-05-22', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-05-22', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-05-22', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-05-22', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-05-22', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-05-22', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-05-22', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-05-22', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-05-22', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-05-22', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-05-22', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-05-22', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-05-22', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-05-22', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-05-22', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-05-23', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-05-23', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-05-23', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-05-23', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-05-23', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-05-23', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-05-23', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 3, '2025-05-23', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-05-23', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-05-23', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-05-23', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-05-23', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-05-23', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-05-23', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-05-23', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-05-23', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-05-23', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-05-23', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-05-23', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-05-23', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-05-23', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-05-23', 14, 2800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-05-23', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-05-24', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-05-24', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-05-24', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-05-24', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-05-24', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-05-24', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-05-24', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 3, '2025-05-24', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-05-24', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-05-24', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-05-24', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-05-24', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-05-24', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-05-24', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-05-24', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-05-24', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-05-24', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-05-24', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-05-24', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-05-24', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-05-24', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-05-24', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-05-24', 15, 3000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-05-24', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-05-25', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-05-25', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-05-25', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-05-25', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-05-25', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-05-25', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-05-25', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-05-25', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-05-25', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-05-25', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-05-25', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-05-25', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-05-25', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-05-25', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-05-25', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-05-25', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-05-25', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-05-25', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-05-25', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-05-25', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-05-25', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-05-25', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-05-25', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-05-25', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-05-25', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-05-25', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-05-26', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-05-26', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-05-26', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-05-26', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-05-26', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-05-26', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-05-26', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-05-26', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-05-26', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-05-26', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-05-26', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-05-26', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-05-26', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-05-26', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-05-26', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-05-26', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-05-26', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-05-26', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-05-26', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-05-26', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-05-26', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-05-26', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-05-26', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-05-26', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-05-26', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-05-27', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-05-27', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-05-27', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-05-27', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-05-27', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-05-27', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-05-27', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-05-27', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-05-27', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-05-27', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-05-27', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-05-27', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-05-27', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-05-27', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-05-27', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-05-27', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-05-27', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-05-27', 15, 3000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-05-27', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-05-27', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-05-27', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-05-27', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-05-27', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-05-27', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-05-27', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-05-28', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-05-28', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-05-28', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-05-28', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-05-28', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-05-28', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-05-28', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-05-28', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-05-28', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-05-28', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-05-28', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-05-28', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-05-28', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-05-28', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-05-28', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-05-28', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-05-28', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-05-28', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-05-28', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-05-28', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-05-28', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-05-28', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-05-28', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-05-28', 6, 1200.0, 'admin', 'admin');