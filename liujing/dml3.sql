-- 插入溜井放矿数据到PostgreSQL数据库
-- 生成日期: 2025-07-18 21:41:17
-- 自动从数据库查询溜井ID

-- 清空现有数据（如果需要）
-- TRUNCATE TABLE data_orepass_operation CASCADE;

-- 插入溜井放矿数据
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-03-01', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-03-01', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-03-01', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-03-01', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-03-01', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-03-01', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-03-01', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-03-01', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-03-01', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-03-01', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 3, '2025-03-01', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-03-01', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-03-01', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-03-01', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-03-01', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-03-01', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-03-01', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-03-01', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-03-01', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-03-01', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-03-01', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-03-01', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-03-01', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-03-01', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-03-01', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-03-01', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-03-01', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-03-02', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-03-02', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-03-02', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-03-02', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-03-02', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-03-02', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-03-02', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-03-02', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-03-02', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 3, '2025-03-02', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-03-02', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-03-02', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-03-02', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-03-02', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-03-02', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 1, '2025-03-02', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 2, '2025-03-02', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-03-02', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-03-02', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-03-02', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-03-02', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-03-02', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-03-02', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-03-02', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-03-02', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-03-02', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-03-02', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-03-02', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-03-03', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-03-03', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-03-03', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-03-03', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-03-03', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-03-03', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-03-03', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-03-03', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-03-03', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-03-03', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 3, '2025-03-03', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-03-03', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-03-03', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-03-03', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-03-03', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 1, '2025-03-03', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 2, '2025-03-03', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-03-03', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-03-03', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-03-03', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-03-03', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-03-03', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-03-03', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-03-03', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-03-03', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-03-03', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-03-03', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-03-03', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-03-03', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-03-03', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-03-04', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-03-04', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-03-04', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-03-04', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-03-04', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-03-04', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-03-04', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-03-04', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-03-04', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-03-04', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-03-04', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-03-04', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-03-04', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-03-04', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-03-04', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-03-04', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-03-04', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-03-04', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-03-04', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-03-04', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-03-04', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-03-04', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-03-04', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-03-04', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-03-04', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-03-04', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-03-04', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-03-05', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-03-05', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-03-05', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-03-05', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-03-05', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-03-05', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-03-05', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 3, '2025-03-05', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-03-05', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-03-05', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-03-05', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-03-05', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-03-05', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 2, '2025-03-05', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-03-05', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-03-05', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-03-05', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-03-05', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-03-05', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-03-05', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-03-05', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-03-05', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-03-05', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-03-05', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-03-05', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-03-05', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-03-06', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-03-06', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-03-06', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-03-06', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-03-06', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-03-06', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-03-06', 14, 2800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-03-06', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-03-06', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-03-06', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-03-06', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-03-06', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-03-06', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-03-06', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-03-06', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 1, '2025-03-06', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 2, '2025-03-06', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 3, '2025-03-06', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-03-06', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-03-06', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-03-06', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-03-06', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-03-06', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-03-06', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-03-06', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-03-06', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-03-06', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-03-06', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-03-06', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-03-07', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-03-07', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-03-07', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-03-07', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-03-07', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-03-07', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-03-07', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-03-07', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 1, '2025-03-07', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-03-07', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-03-07', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-03-07', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-03-07', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-03-07', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-03-07', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-03-07', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-03-07', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-03-07', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-03-07', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-03-07', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-03-08', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-03-08', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-03-08', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-03-08', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-03-08', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-03-08', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-03-08', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-03-08', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-03-08', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-03-08', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-03-08', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-03-08', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-03-08', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 1, '2025-03-08', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 3, '2025-03-08', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-03-08', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-03-08', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-03-08', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-03-08', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-03-08', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-03-08', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-03-08', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-03-08', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-03-08', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-03-08', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-03-08', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-03-08', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-03-09', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-03-09', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-03-09', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-03-09', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-03-09', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-03-09', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-03-09', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-03-09', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-03-09', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 2, '2025-03-09', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-03-09', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-03-09', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-03-09', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-03-09', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-03-09', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-03-09', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-03-09', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-03-09', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-03-09', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-03-09', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-03-09', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-03-09', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-03-10', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-03-10', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-03-10', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-03-10', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-03-10', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-03-10', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-03-10', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-03-10', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-03-10', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-03-10', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-03-10', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-03-10', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-03-10', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 1, '2025-03-10', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 2, '2025-03-10', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 3, '2025-03-10', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-03-10', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-03-10', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-03-10', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-03-10', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-03-10', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-03-10', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-03-10', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-03-10', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-03-10', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-03-10', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-03-11', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-03-11', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-03-11', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-03-11', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-03-11', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-03-11', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-03-11', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-03-11', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-03-11', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-03-11', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-03-11', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-03-11', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-03-11', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 1, '2025-03-11', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 2, '2025-03-11', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 3, '2025-03-11', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-03-11', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-03-11', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-03-11', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-03-11', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-03-11', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-03-11', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-03-11', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-03-12', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-03-12', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-03-12', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-03-12', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-03-12', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-03-12', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-03-12', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-03-12', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-03-12', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 1, '2025-03-12', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 2, '2025-03-12', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-03-12', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-03-12', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-03-12', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-03-12', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-03-12', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-03-13', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-03-13', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-03-13', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-03-13', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-03-13', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-03-13', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-03-13', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-03-13', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-03-13', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-03-13', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 1, '2025-03-13', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 2, '2025-03-13', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 3, '2025-03-13', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-03-13', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-03-13', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-03-13', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-03-13', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-03-13', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-03-13', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-03-13', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-03-13', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-03-13', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-03-13', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-03-13', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-03-14', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-03-14', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-03-14', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-03-14', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-03-14', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-03-14', 14, 2800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-03-14', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-03-14', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-03-14', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-03-14', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-03-14', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-03-14', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-03-14', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 2, '2025-03-14', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-03-14', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-03-14', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-03-14', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-03-14', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-03-14', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-03-14', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-03-14', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-03-14', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-03-14', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-03-14', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-03-14', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-03-14', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-03-14', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-03-14', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-03-15', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-03-15', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-03-15', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-03-15', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-03-15', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-03-15', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-03-15', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-03-15', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-03-15', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-03-15', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-03-15', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-03-15', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-03-15', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 2, '2025-03-15', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 3, '2025-03-15', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-03-15', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-03-15', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-03-15', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-03-15', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-03-15', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-03-15', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-03-15', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-03-15', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-03-15', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-03-15', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-03-15', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-03-15', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-03-15', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-03-15', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-03-16', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-03-16', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-03-16', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-03-16', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-03-16', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-03-16', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-03-16', 15, 3000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-03-16', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-03-16', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-03-16', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-03-16', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-03-16', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-03-16', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 2, '2025-03-16', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 3, '2025-03-16', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-03-16', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-03-16', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-03-16', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-03-16', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-03-16', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-03-16', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-03-16', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-03-16', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-03-16', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-03-16', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-03-16', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-03-16', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-03-16', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-03-17', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-03-17', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-03-17', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-03-17', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-03-17', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-03-17', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-03-17', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-03-17', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-03-25', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-03-25', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-03-25', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-03-25', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-03-25', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-03-25', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-03-25', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-03-25', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-03-25', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-03-25', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-03-25', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-03-25', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-03-25', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-03-25', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-03-25', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-03-25', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-03-25', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-03-26', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-03-26', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-03-26', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-03-26', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-03-26', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-03-26', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-03-26', 15, 3000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-03-26', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-03-26', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-03-26', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-03-26', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-03-26', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-03-26', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-03-26', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-03-26', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-03-26', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-03-26', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-03-26', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-03-26', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-03-26', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-03-26', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-03-26', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-03-26', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-03-26', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-03-26', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-03-26', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-03-26', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-03-27', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-03-27', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-03-27', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-03-27', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-03-27', 17, 3400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-03-27', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-03-27', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-03-27', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-03-27', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-03-27', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-03-27', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-03-27', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-03-27', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-03-27', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-03-27', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-03-27', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-03-27', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-03-27', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-03-28', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-03-28', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-03-28', 18, 3600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-03-28', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-03-28', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-03-28', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-03-28', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-03-28', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 3, '2025-03-28', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-03-28', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-03-28', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-03-28', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-03-28', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-03-28', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-03-28', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-03-28', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-03-28', 3, 600.0, 'admin', 'admin');