-- 插入溜井放矿数据到PostgreSQL数据库
-- 生成日期: 2025-07-18 21:48:29
-- 自动从数据库查询溜井ID

-- 清空现有数据（如果需要）
-- TRUNCATE TABLE data_orepass_operation CASCADE;

-- 插入溜井放矿数据
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-03-29', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-03-29', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-03-29', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-03-29', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-03-29', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-03-29', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-03-29', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-03-29', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-03-29', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-03-29', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-03-29', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-03-29', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 1, '2025-03-29', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 2, '2025-03-29', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-03-29', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-03-29', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-03-29', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-03-29', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-03-29', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-03-29', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-03-30', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-03-30', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-03-30', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-03-30', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-03-30', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-03-30', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-03-30', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-03-30', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-03-30', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-03-30', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 3, '2025-03-30', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-03-30', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-03-30', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-03-30', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-03-30', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-03-30', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-03-30', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-03-30', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-03-30', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-03-31', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-03-31', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-03-31', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-03-31', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-03-31', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-03-31', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-03-31', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-03-31', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-03-31', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-03-31', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-03-31', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-03-31', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-03-31', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-03-31', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-03-31', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-03-31', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-03-31', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-04-01', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-04-01', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-04-01', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-04-01', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-04-01', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-04-01', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-04-01', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-04-01', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-04-01', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 2, '2025-04-01', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 3, '2025-04-01', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-04-01', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-04-01', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-04-01', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-04-01', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-04-01', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-04-01', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-04-01', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-04-02', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-04-02', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-04-02', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-04-02', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-04-02', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-04-02', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-04-02', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-04-02', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 3, '2025-04-02', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-04-02', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-04-02', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-04-02', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-04-02', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-04-02', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-04-02', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-04-02', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-04-02', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-04-02', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-04-02', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-04-02', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-04-02', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-04-02', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-04-02', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-04-03', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-04-03', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-04-03', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-04-03', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-04-03', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-04-03', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-04-03', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-04-03', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-04-03', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-04-03', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-04-03', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 1, '2025-04-03', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 2, '2025-04-03', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-04-03', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-04-03', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-04-03', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-04-03', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-04-03', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-04-03', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-04-03', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-04-03', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-04-03', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-04-03', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-04-04', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-04-04', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-04-04', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-04-04', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-04-04', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-04-04', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-04-04', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-04-04', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-04-04', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-04-04', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-04-04', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-04-04', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-04-04', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 1, '2025-04-04', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 2, '2025-04-04', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 3, '2025-04-04', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-04-04', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-04-04', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-04-04', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-04-04', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-04-04', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-04-04', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-04-04', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-04-04', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-04-04', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-04-04', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-04-04', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-04-04', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-04-04', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-04-05', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-04-05', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-04-05', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-04-05', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-04-05', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-04-05', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-04-05', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-04-05', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 1, '2025-04-05', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-04-05', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-04-05', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-04-05', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-04-05', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-04-05', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-04-05', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-04-05', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-04-05', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-04-09', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-04-09', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-04-09', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-04-09', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-04-09', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-04-09', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 3, '2025-04-09', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-04-09', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-04-09', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-04-09', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-04-09', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-04-09', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-04-09', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-04-10', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-04-10', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-04-10', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-04-10', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-04-10', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-04-10', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-04-10', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-04-10', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-04-10', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-04-10', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-04-10', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-04-10', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-04-10', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 2, '2025-04-10', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-04-10', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-04-10', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-04-10', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-04-10', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-04-10', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-04-10', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-04-10', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-04-10', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-04-10', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-04-11', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-04-11', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-04-11', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-04-11', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-04-11', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-04-11', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-04-11', 16, 3200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-04-11', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-04-11', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 3, '2025-04-11', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-04-11', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-04-11', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-04-11', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-04-11', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-04-11', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-04-11', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-04-11', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-04-11', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-04-11', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-04-11', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-04-11', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-04-11', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-04-11', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-04-11', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-04-11', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-04-11', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-04-11', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-04-11', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-04-12', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-04-12', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-04-12', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-04-12', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-04-12', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-04-12', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-04-12', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-04-12', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-04-12', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-04-12', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-04-12', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-04-12', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-04-12', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-04-12', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-04-12', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-04-12', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-04-12', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-04-12', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-04-12', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-04-12', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-04-12', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-04-12', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-04-12', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-04-12', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-04-12', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-04-12', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-04-12', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-04-13', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-04-13', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-04-13', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-04-13', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-04-13', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-04-13', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-04-13', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-04-13', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-04-13', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-04-13', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-04-13', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-04-13', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 2, '2025-04-13', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-04-13', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-04-13', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-04-13', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-04-13', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-04-13', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-04-13', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-04-13', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-04-13', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-04-13', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-04-13', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-04-13', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-04-14', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-04-14', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-04-14', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-04-14', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-04-14', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-04-14', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-04-14', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-04-14', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-04-14', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-04-14', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-04-14', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-04-14', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-04-14', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 3, '2025-04-14', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-04-14', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-04-14', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-04-14', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-04-14', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-04-14', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-04-14', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-04-14', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-04-14', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-04-14', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-04-14', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-04-14', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-04-14', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-04-14', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-04-15', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-04-15', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-04-15', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-04-15', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-04-15', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-04-15', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-04-15', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-04-15', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-04-15', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-04-15', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-04-15', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 3, '2025-04-15', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-04-15', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-04-15', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-04-15', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-04-15', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-04-15', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-04-15', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-04-15', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-04-15', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-04-15', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-04-15', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-04-15', 14, 2800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-04-15', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-04-15', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-04-15', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-04-15', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-04-15', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-04-15', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-04-15', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-04-16', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-04-16', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-04-16', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-04-16', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-04-16', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-04-16', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-04-16', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-04-16', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-04-16', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-04-16', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 3, '2025-04-16', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-04-16', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-04-16', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-04-16', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-04-16', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-04-16', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-04-16', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-04-16', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-04-16', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-04-16', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-04-16', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-04-16', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-04-16', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-04-16', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-04-16', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-04-16', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-04-16', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-04-16', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-04-17', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-04-17', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-04-17', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-04-17', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-04-17', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-04-17', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-04-17', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-04-17', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-04-17', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-04-17', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-04-17', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-04-17', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-04-17', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-04-17', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-04-17', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-04-17', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-04-17', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-04-17', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-04-17', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-04-17', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-04-17', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-04-17', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-04-17', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-04-17', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-04-17', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-04-17', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-04-17', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-04-18', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-04-18', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-04-18', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-04-18', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-04-18', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-04-18', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-04-18', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-04-18', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-04-18', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-04-18', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-04-18', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-04-18', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-04-18', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-04-18', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-04-18', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-04-18', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-04-18', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-04-18', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-04-18', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-04-18', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-04-18', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-04-18', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-04-18', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-04-18', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-04-18', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-04-19', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-04-19', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-04-19', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-04-19', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-04-19', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-04-19', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-04-19', 16, 3200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-04-19', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-04-19', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-04-19', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 3, '2025-04-19', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-04-19', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-04-19', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-04-19', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-04-19', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-04-19', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-04-19', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-04-19', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-04-19', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-04-19', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-04-19', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-04-19', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-04-19', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-04-19', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-04-19', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-04-19', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-04-19', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-04-19', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-04-19', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-04-20', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-04-20', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-04-20', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-04-20', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-04-20', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-04-20', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-04-20', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-04-20', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-04-20', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-04-20', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-04-20', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-04-20', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-04-20', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-04-20', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-04-20', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-04-20', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-04-20', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-04-20', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-04-20', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-04-20', 14, 2800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-04-20', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-04-20', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-04-20', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-04-20', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-04-20', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-04-20', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-04-21', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-04-21', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-04-21', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-04-21', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-04-21', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-04-21', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-04-21', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-04-21', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-04-21', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-04-21', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-04-21', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-04-21', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-04-21', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-04-21', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-04-21', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-04-21', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-04-21', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-04-21', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-04-21', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-04-21', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-04-21', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-04-21', 17, 3400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-04-21', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-04-21', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-04-21', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-04-21', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-04-21', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-04-21', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-04-21', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-04-22', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-04-22', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-04-22', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-04-22', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-04-22', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-04-22', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-04-22', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 3, '2025-04-22', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-04-22', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-04-22', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-04-22', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-04-22', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-04-22', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-04-22', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-04-22', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-04-22', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-04-22', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-04-22', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-04-22', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-04-22', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-04-22', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-04-22', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-04-22', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-04-23', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-04-23', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-04-23', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-04-23', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-04-23', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-04-23', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-04-23', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-04-23', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-04-23', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-04-23', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-04-23', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-04-23', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 2, '2025-04-23', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-04-23', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-04-23', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-04-23', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-04-23', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-04-23', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-04-23', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-04-23', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-04-23', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-04-23', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-04-23', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-04-23', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-04-23', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-04-23', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-04-23', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-04-23', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-04-24', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-04-24', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-04-24', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-04-24', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-04-24', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-04-24', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-04-24', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-04-24', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-04-24', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-04-24', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-04-24', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-04-24', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-04-24', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-04-24', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-04-24', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-04-24', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-04-24', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-04-24', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-04-24', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-04-24', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-04-24', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-04-24', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-04-24', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-04-24', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-04-24', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-04-25', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-04-25', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-04-25', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-04-25', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-04-25', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-04-25', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-04-25', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-04-25', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-04-25', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-04-25', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-04-25', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-04-25', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-04-25', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-04-25', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-04-25', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-04-25', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-04-25', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-04-25', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-04-25', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-04-25', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-04-25', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-04-25', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-04-25', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-04-25', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-04-25', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-04-26', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-04-26', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-04-26', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-04-26', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-04-26', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-04-26', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-04-26', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-04-26', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-04-26', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-04-26', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-04-26', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-04-26', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-04-26', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-04-26', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-04-26', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-04-26', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-04-26', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-04-26', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-04-26', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-04-26', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-04-26', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-04-26', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-04-26', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-04-26', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-04-26', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-04-27', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-04-27', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-04-27', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-04-27', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-04-27', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-04-27', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-04-27', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 3, '2025-04-27', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-04-27', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-04-27', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-04-27', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-04-27', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 2, '2025-04-27', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-04-27', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-04-27', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-04-27', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-04-27', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-04-27', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-04-27', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-04-27', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-04-27', 15, 3000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-04-27', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-04-27', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-04-27', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-04-27', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-04-27', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-04-27', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-04-27', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-04-28', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-04-28', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-04-28', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-04-28', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-04-28', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-04-28', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-04-28', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-04-28', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-04-28', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-04-28', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-04-28', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 1, '2025-04-28', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-04-28', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-04-28', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-04-28', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-04-28', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-04-28', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-04-28', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-04-28', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-04-28', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-04-28', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-04-28', 3, 600.0, 'admin', 'admin');