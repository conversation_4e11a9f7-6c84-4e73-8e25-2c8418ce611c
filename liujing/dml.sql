-- 插入溜井放矿数据到PostgreSQL数据库
-- 生成日期: 2025-07-08 12:19:33
-- 自动从数据库查询溜井ID

-- 清空现有数据（如果需要）
-- TRUNCATE TABLE data_orepass_operation CASCADE;

-- 插入溜井放矿数据
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-05-29', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-05-29', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-05-29', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-05-29', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-05-29', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-05-29', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-05-29', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-05-29', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-05-29', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-05-29', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-05-29', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-05-30', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-05-31', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-05-31', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-05-31', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-05-31', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-05-31', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-05-31', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-05-31', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-05-31', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-05-31', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-05-31', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-05-31', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-06-01', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-06-01', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-06-01', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-06-01', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-06-01', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-06-01', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-06-01', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-06-01', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-06-02', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-06-05', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-06-05', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-06-05', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-06-05', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-06-05', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-06-05', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-06-05', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 2, '2025-06-05', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 9, 3, '2025-06-05', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-06-05', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-06-05', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-06-05', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-06-05', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-06-05', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-06-05', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-06-06', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-06-06', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-06-06', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-06-06', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-06-06', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-06-06', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-06-06', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-06-06', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-06-06', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-06-06', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-06-06', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-06-06', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-06-06', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-06-06', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-06-06', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-06-06', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-06-06', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-06-06', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-06-06', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-06-06', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-06-06', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-06-06', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-06-07', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-06-07', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-06-07', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-06-07', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-06-07', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-06-07', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-06-07', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-06-07', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-06-07', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-06-07', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-06-07', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 3, '2025-06-07', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-06-07', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-06-07', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-06-07', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-06-07', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-06-07', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-06-07', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-06-07', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-06-07', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-06-07', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-06-07', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-06-07', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-06-07', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-06-07', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-06-07', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-06-07', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-06-07', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-06-08', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-06-08', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-06-08', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-06-08', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-06-08', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-06-08', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-06-08', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-06-08', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-06-08', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 3, '2025-06-08', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-06-08', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-06-08', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-06-08', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-06-08', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-06-08', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-06-08', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-06-08', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-06-08', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-06-08', 18, 3600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-06-08', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-06-08', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-06-08', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-06-08', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-06-08', 15, 3000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-06-08', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-06-09', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-06-09', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-06-09', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-06-09', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-06-09', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-06-09', 14, 2800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-06-09', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-06-09', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-06-09', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-06-09', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-06-09', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-06-09', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-06-09', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-06-09', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-06-09', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-06-09', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-06-09', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-06-09', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-06-09', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-06-09', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-06-09', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-06-09', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-06-09', 18, 3600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-06-09', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-06-10', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-06-10', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-06-10', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-06-10', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-06-10', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-06-10', 15, 3000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-06-10', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-06-10', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 3, '2025-06-10', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-06-10', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-06-10', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-06-10', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-06-10', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-06-10', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-06-10', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-06-10', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-06-10', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-06-10', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-06-10', 17, 3400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-06-10', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-06-10', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-06-10', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-06-10', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-06-10', 17, 3400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-06-10', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-06-11', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-06-11', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-06-11', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-06-11', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-06-11', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-06-11', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-06-11', 17, 3400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-06-11', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-06-11', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-06-11', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-06-11', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-06-11', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-06-11', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-06-11', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-06-11', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-06-11', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-06-11', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-06-11', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-06-11', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-06-11', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-06-11', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-06-11', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-06-11', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-06-11', 14, 2800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-06-11', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-06-12', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-06-12', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-06-12', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-06-12', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-06-12', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-06-12', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-06-12', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-06-12', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 3, '2025-06-12', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-06-12', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-06-12', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-06-12', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-06-12', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-06-12', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-06-12', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-06-12', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-06-12', 15, 3000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-06-12', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-06-12', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-06-12', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-06-12', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-06-12', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-06-12', 15, 3000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-06-12', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-06-13', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-06-13', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-06-13', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-06-13', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-06-13', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-06-13', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-06-13', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-06-13', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-06-13', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-06-13', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-06-13', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-06-13', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-06-13', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-06-13', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-06-13', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-06-13', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-06-13', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-06-13', 15, 3000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-06-13', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-06-13', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-06-13', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-06-13', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-06-13', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-06-13', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-06-13', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-06-14', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-06-14', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-06-14', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-06-14', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-06-14', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-06-14', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-06-14', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-06-14', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-06-14', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-06-14', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-06-14', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-06-14', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-06-14', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-06-14', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-06-14', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-06-14', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-06-14', 15, 3000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-06-14', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-06-14', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-06-14', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-06-14', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-06-14', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-06-14', 14, 2800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-06-14', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-06-15', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-06-15', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-06-15', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-06-15', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-06-15', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-06-15', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-06-15', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-06-15', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-06-15', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-06-15', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-06-15', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-06-15', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-06-15', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-06-15', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-06-15', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-06-15', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-06-15', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-06-15', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-06-15', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-06-15', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-06-15', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-06-15', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-06-15', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-06-15', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-06-15', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-06-15', 14, 2800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-06-15', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-06-16', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-06-16', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-06-16', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-06-16', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-06-16', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-06-16', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-06-16', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-06-16', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-06-16', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-06-16', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-06-16', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-06-16', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-06-16', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-06-16', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-06-16', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-06-16', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-06-16', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-06-16', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-06-16', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-06-16', 15, 3000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-06-16', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-06-17', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-06-17', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-06-17', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-06-17', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-06-17', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-06-17', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-06-17', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-06-17', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-06-17', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-06-17', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-06-17', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-06-17', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-06-17', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-06-17', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-06-17', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-06-17', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-06-17', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-06-17', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-06-17', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-06-17', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-06-17', 14, 2800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-06-17', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-06-18', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-06-18', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-06-18', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-06-18', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-06-18', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-06-18', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-06-18', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-06-18', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-06-18', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-06-18', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-06-18', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-06-18', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-06-18', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-06-18', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-06-18', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-06-18', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-06-18', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-06-18', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-06-18', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-06-18', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-06-18', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-06-18', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-06-18', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-06-19', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-06-19', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-06-19', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-06-19', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-06-19', 17, 3400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-06-19', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-06-19', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-06-19', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-06-19', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-06-19', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-06-19', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-06-19', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-06-19', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-06-19', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-06-19', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-06-19', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-06-19', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-06-19', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-06-19', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-06-19', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-06-19', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-06-20', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-06-20', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-06-20', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-06-20', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-06-20', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-06-20', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-06-20', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-06-20', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 3, '2025-06-20', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-06-20', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-06-20', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-06-20', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-06-20', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-06-20', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-06-20', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-06-20', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-06-20', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-06-20', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-06-20', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-06-20', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-06-20', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-06-20', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-06-20', 14, 2800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-06-20', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-06-21', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-06-21', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-06-21', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-06-21', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-06-21', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-06-21', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-06-21', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-06-21', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-06-21', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 3, '2025-06-21', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-06-21', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-06-21', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-06-21', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-06-21', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-06-21', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-06-21', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-06-21', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-06-21', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-06-21', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-06-21', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-06-21', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-06-21', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-06-21', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-06-21', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-06-21', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-06-21', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-06-22', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-06-22', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-06-22', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-06-22', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-06-22', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-06-22', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-06-22', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-06-22', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-06-22', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-06-22', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-06-22', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-06-22', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-06-22', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 3, '2025-06-22', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-06-22', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-06-22', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-06-22', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-06-22', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-06-22', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-06-22', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-06-22', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-06-22', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-06-22', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-06-22', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-06-22', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-06-23', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-06-23', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-06-23', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-06-23', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-06-23', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-06-23', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-06-23', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-06-23', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-06-23', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-06-23', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-06-23', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-06-23', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-06-23', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-06-23', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-06-23', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-06-23', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-06-23', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-06-23', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-06-23', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-06-23', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-06-23', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-06-23', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-06-23', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-06-23', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-06-24', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-06-24', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-06-24', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-06-24', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-06-24', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-06-24', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-06-24', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-06-24', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-06-24', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-06-24', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-06-24', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-06-24', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-06-24', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-06-24', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-06-24', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-06-24', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-06-24', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-06-24', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-06-24', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-06-24', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-06-24', 12, 2400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-06-24', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-06-25', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-06-25', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-06-25', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-06-25', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-06-25', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-06-25', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-06-25', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-06-25', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-06-25', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-06-25', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-06-25', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-06-25', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-06-25', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-06-25', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-06-25', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-06-25', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-06-25', 13, 2600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-06-25', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-06-25', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-06-25', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-06-25', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-06-25', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-06-25', 17, 3400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-06-25', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-06-26', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-06-26', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-06-26', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-06-26', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-06-26', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-06-26', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-06-26', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-06-26', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-06-26', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-06-26', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 1, '2025-06-26', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 8, 3, '2025-06-26', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-06-26', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 2, '2025-06-26', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-06-26', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-06-26', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-06-26', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-06-26', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-06-26', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-06-26', 10, 2000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-06-26', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-06-26', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-06-27', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 2, '2025-06-27', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-06-27', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-06-27', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-06-27', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-06-27', 7, 1400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-06-27', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 1, '2025-06-27', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 3, '2025-06-27', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 3, '2025-06-27', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-06-27', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 1, '2025-06-27', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-06-27', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-06-27', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-06-27', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-06-27', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-06-27', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-06-27', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-06-27', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-06-27', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-06-27', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 1, '2025-06-28', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 2, 3, '2025-06-28', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 1, '2025-06-28', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 2, '2025-06-28', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 3, 3, '2025-06-28', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 1, '2025-06-28', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 2, '2025-06-28', 11, 2200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(2, 4, 3, '2025-06-28', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 5, 2, '2025-06-28', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 1, '2025-06-28', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 6, 2, '2025-06-28', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 2, '2025-06-28', 5, 1000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(1, 7, 3, '2025-06-28', 2, 400.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 10, 3, '2025-06-28', 1, 200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 1, '2025-06-28', 9, 1800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 2, '2025-06-28', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 11, 3, '2025-06-28', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 1, '2025-06-28', 8, 1600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 2, '2025-06-28', 15, 3000.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 12, 3, '2025-06-28', 4, 800.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 1, '2025-06-28', 3, 600.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 2, '2025-06-28', 6, 1200.0, 'admin', 'admin');
INSERT INTO data_orepass_operation 
(project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, update_by)
VALUES
(3, 13, 3, '2025-06-28', 2, 400.0, 'admin', 'admin');