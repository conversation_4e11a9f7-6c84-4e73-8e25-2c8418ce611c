#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
收集溜井名称脚本
从extracted_data.csv中提取唯一的溜井名称，并生成SQL插入语句
用于填充base_ore_pass表
"""

import pandas as pd
import os
import re
from datetime import datetime

def collect_ore_pass_names():
    """从extracted_data.csv中收集唯一的溜井名称"""
    
    # 检查文件是否存在
    csv_file = 'extracted_data.csv'
    if not os.path.exists(csv_file):
        print(f"错误: 找不到文件 {csv_file}")
        return None
    
    try:
        # 读取CSV文件
        df = pd.read_csv(csv_file, encoding='utf-8')
        
        # 溜井名称在第四列（索引为3）
        if len(df.columns) < 4:
            print("错误: CSV文件格式不正确，缺少溜井名称列")
            return None
        
        # 获取第四列数据（溜井名称）
        ore_pass_names = df.iloc[:, 3].dropna().unique()
        
        # 过滤掉不是溜井名称的行（如标题行、合计行等）
        valid_ore_pass_names = []
        for name in ore_pass_names:
            name_str = str(name).strip()
            # 跳过包含"溜井"、"合计"、"总计"等关键词的行
            if not any(keyword in name_str for keyword in ["溜井名称", "合计", "总计", "小计"]):
                if name_str and not name_str.isdigit():  # 确保不是空字符串或纯数字
                    valid_ore_pass_names.append(name_str)
        
        return valid_ore_pass_names
    
    except Exception as e:
        print(f"处理CSV文件时出错: {str(e)}")
        return None

def generate_sql_inserts(ore_pass_names):
    """生成SQL插入语句"""
    
    if not ore_pass_names:
        return ""
    
    sql_statements = []
    sql_statements.append("-- 生成时间: " + datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    sql_statements.append("-- 溜井名称数量: " + str(len(ore_pass_names)))
    sql_statements.append("\n-- 插入溜井基础数据")
    
    for name in ore_pass_names:
        # 生成溜井编码（简单地从名称中提取数字，如果有的话）
        code = ''.join(re.findall(r'\d+', name)) if re.search(r'\d+', name) else ''
        
        sql = f"INSERT INTO base_ore_pass (ore_pass_name, ore_pass_code, ore_pass_desc, create_by, update_by) " \
              f"VALUES ('{name}', '{code}', '', 'system', 'system') " \
              f"ON CONFLICT (ore_pass_name) DO NOTHING;"
        
        sql_statements.append(sql)
    
    return "\n".join(sql_statements)

def main():
    """主函数"""
    
    print("开始收集溜井名称...")
    ore_pass_names = collect_ore_pass_names()
    
    if ore_pass_names:
        print(f"找到 {len(ore_pass_names)} 个唯一的溜井名称")
        
        # 生成SQL插入语句
        sql_content = generate_sql_inserts(ore_pass_names)
        
        # 将SQL语句写入文件
        output_file = 'ore_pass_dml.sql'
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(sql_content)
        
        print(f"SQL语句已写入文件: {output_file}")
    else:
        print("未找到有效的溜井名称")

if __name__ == "__main__":
    main()
