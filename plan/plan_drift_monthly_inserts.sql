-- 掘进计划月度数据插入语句
-- 生成时间: 2025-07-21 13:55:44
-- 数据条数: 340

INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 45.0, 
 901.035, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-4充填联巷' LIMIT 1),
 45.0, 
 710.685, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-5-3号充填巷' LIMIT 1),
 20.0, 
 315.86, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-15-3切割巷' LIMIT 1),
 20.0, 
 342.14, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 135.0, 
 2703.105, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-5-8凿岩巷' LIMIT 1),
 30.0, 
 513.21, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 85.0, 
 1701.955, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-15-3矿块' LIMIT 1),
 30.0, 
 555.78, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-5-8凿岩巷' LIMIT 1),
 40.0, 
 684.28, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-7矿块
（出矿巷）' LIMIT 1),
 30.0, 
 555.78, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-11矿块
（出矿巷）' LIMIT 1),
 30.0, 
 555.78, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-4出矿联巷' LIMIT 1),
 20.0, 
 370.52, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '4#穿' LIMIT 1),
 60.0, 
 904.56, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿上盘侧' LIMIT 1),
 45.0, 
 678.42, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿措施井侧' LIMIT 1),
 90.0, 
 1356.84, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '溜井工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-1060m水平6-4溜井硐室' LIMIT 1),
 7.3, 
 0, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '溜井工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-1060m水平4-3溜井硐室' LIMIT 1),
 7.3, 
 0, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '措施井车场' LIMIT 1),
 45.0, 
 901.035, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-960m/-840m斜坡道
(通-840m中段)' LIMIT 1),
 60.0, 
 987.12, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '胶带斜井尾部硐室联巷' LIMIT 1),
 30.0, 
 500.22, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-1080m/-1123m
斜坡道' LIMIT 1),
 60.0, 
 1261.5, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-1100m破碎配电硐室' LIMIT 1),
 15.0, 
 0, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿脉' LIMIT 1),
 105.0, 
 2102.415, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-2号充填巷' LIMIT 1),
 32.0, 
 505.376, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 45.0, 
 769.815, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20采场联巷' LIMIT 1),
 45.0, 
 769.815, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-9凿岩巷' LIMIT 1),
 45.0, 
 769.815, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2采场联巷' LIMIT 1),
 45.0, 
 769.815, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-9凿岩巷' LIMIT 1),
 15.0, 
 256.605, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿脉' LIMIT 1),
 105.0, 
 2102.415, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-1矿块' LIMIT 1),
 60.0, 
 1111.56, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-9矿块' LIMIT 1),
 65.0, 
 1204.19, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m中段石门巷
（副井侧）' LIMIT 1),
 30.0, 
 0, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 30.0, 
 600.69, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-4充填联巷' LIMIT 1),
 25.0, 
 394.825, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-1充填巷' LIMIT 1),
 15.0, 
 236.895, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-2充填巷' LIMIT 1),
 15.0, 
 236.895, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-3充填巷' LIMIT 1),
 15.0, 
 236.895, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '混凝土搅拌硐室联巷' LIMIT 1),
 30.0, 
 473.79, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '中转成品矿仓联巷' LIMIT 1),
 45.0, 
 858.96, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '胶带斜井尾部硐室联巷' LIMIT 1),
 30.0, 
 526.71, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 60.0, 
 1201.38, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-5-8凿岩巷' LIMIT 1),
 40.0, 
 684.28, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-3凿岩巷' LIMIT 1),
 15.0, 
 256.605, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-7凿岩巷' LIMIT 1),
 40.0, 
 684.28, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-11凿岩巷' LIMIT 1),
 15.0, 
 256.605, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 60.0, 
 1201.38, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿下盘沿脉探矿巷' LIMIT 1),
 20.0, 
 370.52, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-5-8矿块' LIMIT 1),
 40.0, 
 741.04, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-11矿块' LIMIT 1),
 40.0, 
 741.04, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '4#穿' LIMIT 1),
 30.0, 
 452.28, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿上盘侧' LIMIT 1),
 30.0, 
 452.28, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿措施井侧' LIMIT 1),
 60.0, 
 904.56, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '措施井车场' LIMIT 1),
 45.0, 
 901.035, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-960m/-840m斜坡道
(通-840m中段)' LIMIT 1),
 50.0, 
 822.6, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-1080m/-1123m
斜坡道' LIMIT 1),
 60.0, 
 1261.5, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-1100m破碎配电硐室' LIMIT 1),
 15.0, 
 0, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿脉' LIMIT 1),
 105.0, 
 2102.415, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-22-3号充填巷' LIMIT 1),
 20.0, 
 315.86, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-3号充填巷' LIMIT 1),
 20.0, 
 315.86, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 45.0, 
 769.815, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20采场联巷' LIMIT 1),
 20.0, 
 342.14, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-9凿岩巷' LIMIT 1),
 30.0, 
 513.21, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-7凿岩巷' LIMIT 1),
 30.0, 
 513.21, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2采场联巷' LIMIT 1),
 45.0, 
 769.815, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-7凿岩巷' LIMIT 1),
 15.0, 
 256.605, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿脉' LIMIT 1),
 105.0, 
 2102.415, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-9矿块' LIMIT 1),
 15.0, 
 277.89, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-5矿块' LIMIT 1),
 15.0, 
 277.89, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-1矿块' LIMIT 1),
 50.0, 
 926.3, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-7矿块' LIMIT 1),
 40.0, 
 741.04, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m中段石门巷
（副井侧）' LIMIT 1),
 30.0, 
 0, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-15探矿巷' LIMIT 1),
 30.0, 
 473.79, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 20.0, 
 400.46, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-1号充填巷' LIMIT 1),
 45.0, 
 710.685, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-2号充填巷' LIMIT 1),
 30.0, 
 473.79, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '进风井联巷' LIMIT 1),
 26.0, 
 0, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 110.0, 
 2202.53, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-5-4凿岩巷' LIMIT 1),
 20.0, 
 342.14, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-3凿岩巷' LIMIT 1),
 50.0, 
 855.35, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-7凿岩巷' LIMIT 1),
 30.0, 
 513.21, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-11凿岩巷' LIMIT 1),
 15.0, 
 256.605, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-15探矿巷' LIMIT 1),
 45.0, 
 769.815, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 120.0, 
 2402.76, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-15探矿巷' LIMIT 1),
 45.0, 
 833.67, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-5-4矿块' LIMIT 1),
 55.0, 
 1018.93, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-5-8矿块' LIMIT 1),
 55.0, 
 1018.93, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-11矿块' LIMIT 1),
 30.0, 
 555.78, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '4#穿' LIMIT 1),
 60.0, 
 904.56, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿上盘侧' LIMIT 1),
 45.0, 
 678.42, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿措施井侧' LIMIT 1),
 90.0, 
 1356.84, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#主井粉矿回收巷' LIMIT 1),
 35.0, 
 0, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-1277m水平沉淀池、水仓' LIMIT 1),
 47.4, 
 0, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m措施井石门巷' LIMIT 1),
 60.0, 
 1201.38, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-960m/-840m斜坡道' LIMIT 1),
 60.0, 
 987.12, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿脉' LIMIT 1),
 50.0, 
 1001.2, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-22-3号充填巷' LIMIT 1),
 20.0, 
 315.9, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-3号充填巷' LIMIT 1),
 20.0, 
 315.9, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-1号充填巷' LIMIT 1),
 10.0, 
 157.9, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-3号充填巷' LIMIT 1),
 10.0, 
 157.9, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-20-1充填巷' LIMIT 1),
 50.0, 
 789.7, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-1充填巷' LIMIT 1),
 30.0, 
 473.8, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-2充填巷' LIMIT 1),
 15.0, 
 236.9, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-3充填巷' LIMIT 1),
 15.0, 
 236.9, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 40.0, 
 684.28, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-3凿岩巷' LIMIT 1),
 25.0, 
 427.675, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-7凿岩巷' LIMIT 1),
 30.0, 
 513.21, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-3凿岩巷' LIMIT 1),
 45.0, 
 769.815, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-7凿岩巷' LIMIT 1),
 30.0, 
 513.21, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-9凿岩巷' LIMIT 1),
 20.0, 
 342.14, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-12凿岩巷' LIMIT 1),
 15.0, 
 256.605, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-22-12凿岩巷' LIMIT 1),
 15.0, 
 256.605, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2采场联巷' LIMIT 1),
 60.0, 
 1026.42, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-5凿岩巷' LIMIT 1),
 15.0, 
 256.605, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿脉' LIMIT 1),
 50.0, 
 1001.15, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-5矿块' LIMIT 1),
 40.0, 
 741.04, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-1矿块' LIMIT 1),
 70.0, 
 1296.82, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-22-11出矿巷' LIMIT 1),
 30.0, 
 555.78, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-7矿块' LIMIT 1),
 45.0, 
 833.67, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-11出矿巷' LIMIT 1),
 50.0, 
 926.3, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-3矿块' LIMIT 1),
 60.0, 
 1111.56, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m副井石门巷' LIMIT 1),
 45.0, 
 0, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-15探矿巷' LIMIT 1),
 30.0, 
 473.8, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-1号充填巷' LIMIT 1),
 45.0, 
 710.7, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-2号充填巷' LIMIT 1),
 15.0, 
 236.9, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-3号充填巷' LIMIT 1),
 15.0, 
 236.9, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '进风井联巷' LIMIT 1),
 20.0, 
 0, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 133.0, 
 2663.059, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-3凿岩巷' LIMIT 1),
 55.0, 
 940.885, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-7凿岩巷' LIMIT 1),
 10.0, 
 171.07, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-11凿岩巷' LIMIT 1),
 15.0, 
 256.605, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-4-5凿岩巷' LIMIT 1),
 15.0, 
 256.605, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-15探矿巷' LIMIT 1),
 35.0, 
 598.745, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 87.0, 
 1742.001, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-15探矿巷' LIMIT 1),
 45.0, 
 833.67, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-5-4矿块' LIMIT 1),
 20.0, 
 370.52, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-5-8矿块' LIMIT 1),
 20.0, 
 370.52, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-3矿块' LIMIT 1),
 80.0, 
 1482.08, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-7矿块' LIMIT 1),
 20.0, 
 370.52, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-4-5矿块' LIMIT 1),
 20.0, 
 370.52, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '4#穿' LIMIT 1),
 35.0, 
 527.66, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿上盘侧' LIMIT 1),
 45.0, 
 678.42, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿措施井侧' LIMIT 1),
 90.0, 
 1356.84, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#主井粉矿回收巷' LIMIT 1),
 35.0, 
 0, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-1277m水平沉淀池、水仓' LIMIT 1),
 47.4, 
 0, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '溜井工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6-3溜井硐室' LIMIT 1),
 7.3, 
 0, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m措施井石门巷' LIMIT 1),
 45.0, 
 901.035, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-960m/-840m斜坡道' LIMIT 1),
 60.0, 
 987.12, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 45.0, 
 901.035, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-3号充填巷' LIMIT 1),
 30.0, 
 473.79, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-20-1充填巷' LIMIT 1),
 40.0, 
 631.72, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-1充填巷' LIMIT 1),
 30.0, 
 473.79, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-2充填巷' LIMIT 1),
 15.0, 
 236.895, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-3充填巷' LIMIT 1),
 15.0, 
 236.895, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-7凿岩巷' LIMIT 1),
 45.0, 
 769.815, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-3凿岩巷' LIMIT 1),
 40.0, 
 684.28, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-7凿岩巷' LIMIT 1),
 30.0, 
 513.21, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-9凿岩巷' LIMIT 1),
 35.0, 
 598.745, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-12凿岩巷' LIMIT 1),
 45.0, 
 769.815, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-22-12凿岩巷' LIMIT 1),
 30.0, 
 513.21, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2凿岩联巷' LIMIT 1),
 60.0, 
 1026.42, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-5凿岩巷' LIMIT 1),
 30.0, 
 513.21, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿脉' LIMIT 1),
 55.0, 
 1101.265, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-5矿块' LIMIT 1),
 40.0, 
 741.04, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-1矿块' LIMIT 1),
 75.0, 
 1389.45, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-22-11出矿巷' LIMIT 1),
 30.0, 
 555.78, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-7矿块' LIMIT 1),
 30.0, 
 555.78, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-9出矿巷' LIMIT 1),
 25.0, 
 463.15, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-11出矿巷' LIMIT 1),
 30.0, 
 555.78, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-7矿块' LIMIT 1),
 50.0, 
 926.3, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m副井石门巷' LIMIT 1),
 45.0, 
 0, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-15探矿巷' LIMIT 1),
 24.0, 
 379.0, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '下盘沿脉（往0#穿改）' LIMIT 1),
 40.0, 
 937.4, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改' LIMIT 1),
 30.0, 
 600.7, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-1号充填巷' LIMIT 1),
 40.0, 
 631.7, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-2号充填巷' LIMIT 1),
 30.0, 
 473.8, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-3号充填巷' LIMIT 1),
 30.0, 
 473.8, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 45.0, 
 769.815, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-7凿岩巷' LIMIT 1),
 35.0, 
 598.745, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-11凿岩巷' LIMIT 1),
 35.0, 
 598.745, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-4-5凿岩巷' LIMIT 1),
 15.0, 
 256.605, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-15探矿巷' LIMIT 1),
 40.0, 
 684.28, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改' LIMIT 1),
 30.0, 
 513.21, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 40.0, 
 800.92, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-15探矿巷' LIMIT 1),
 15.0, 
 277.89, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改' LIMIT 1),
 60.0, 
 1201.38, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '下盘沿脉（往0#穿改）' LIMIT 1),
 30.0, 
 703.08, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-5-4矿块' LIMIT 1),
 36.0, 
 666.936, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-5-8矿块' LIMIT 1),
 55.0, 
 1018.93, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-3矿块' LIMIT 1),
 80.0, 
 1482.08, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-7矿块' LIMIT 1),
 30.0, 
 555.78, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-4-5矿块' LIMIT 1),
 20.0, 
 370.52, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '4-1溜井卸矿硐室' LIMIT 1),
 14.0, 
 0, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿措施井侧' LIMIT 1),
 45.0, 
 678.42, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿副井侧' LIMIT 1),
 45.0, 
 678.42, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '下盘沿脉（往6#穿）' LIMIT 1),
 45.0, 
 678.42, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#主井粉矿回收巷' LIMIT 1),
 19.0, 
 414.2, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-1277m水平沉淀池、水仓' LIMIT 1),
 30.0, 
 603.0, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '4-1溜井振动放矿机硐室' LIMIT 1),
 7.3, 
 0, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m措施井石门巷
（往2号穿）' LIMIT 1),
 45.0, 
 901.035, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m措施井0号穿' LIMIT 1),
 30.0, 
 600.69, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m措施井0号穿倒车硐室' LIMIT 1),
 10.0, 
 200.23, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-865m辅助运输巷' LIMIT 1),
 30.0, 
 0, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-960m/-840m斜坡道' LIMIT 1),
 60.0, 
 987.12, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改' LIMIT 1),
 15.0, 
 300.345, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 30.0, 
 600.69, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-20-2号充填巷' LIMIT 1),
 15.0, 
 236.895, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-3号充填巷' LIMIT 1),
 30.0, 
 473.79, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-1充填巷' LIMIT 1),
 15.0, 
 236.895, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-2充填巷' LIMIT 1),
 15.0, 
 236.895, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-3充填巷' LIMIT 1),
 15.0, 
 236.895, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-1充填巷' LIMIT 1),
 25.0, 
 394.825, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 30.0, 
 513.21, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-20-7凿岩巷' LIMIT 1),
 15.0, 
 256.605, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-3凿岩巷' LIMIT 1),
 45.0, 
 769.815, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-3凿岩巷' LIMIT 1),
 20.0, 
 342.14, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-7凿岩巷' LIMIT 1),
 10.0, 
 171.07, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-9凿岩巷' LIMIT 1),
 20.0, 
 342.14, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-12凿岩巷' LIMIT 1),
 45.0, 
 769.815, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-22-12凿岩巷' LIMIT 1),
 30.0, 
 513.21, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2凿岩联巷' LIMIT 1),
 50.0, 
 855.35, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-1凿岩巷' LIMIT 1),
 15.0, 
 256.605, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改' LIMIT 1),
 30.0, 
 513.21, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿脉' LIMIT 1),
 30.0, 
 600.69, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-1矿块' LIMIT 1),
 30.0, 
 555.78, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-20-7矿块' LIMIT 1),
 15.0, 
 277.89, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-22-11出矿巷' LIMIT 1),
 10.0, 
 185.26, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-7矿块' LIMIT 1),
 40.0, 
 741.04, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-9矿块' LIMIT 1),
 65.0, 
 1204.19, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-11出矿巷' LIMIT 1),
 30.0, 
 555.78, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-3矿块' LIMIT 1),
 30.0, 
 555.78, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-7矿块' LIMIT 1),
 45.0, 
 833.67, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-3矿块' LIMIT 1),
 50.0, 
 926.3, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改' LIMIT 1),
 15.0, 
 300.345, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '无轨维修硐室' LIMIT 1),
 74.8, 
 2987.4372, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '无轨维修硐室联巷' LIMIT 1),
 54.3, 
 1264.9185, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '无轨维修其他硐室' LIMIT 1),
 48.0, 
 821.136, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m副井石门巷' LIMIT 1),
 45.0, 
 1400.85, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改' LIMIT 1),
 110.0, 
 2202.5, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-19探矿巷' LIMIT 1),
 40.0, 
 937.4, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-2号充填巷' LIMIT 1),
 50.0, 
 789.7, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-3号充填巷' LIMIT 1),
 30.0, 
 473.8, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 20.0, 
 400.46, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改' LIMIT 1),
 80.0, 
 1601.84, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-5探矿巷' LIMIT 1),
 40.0, 
 684.28, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-7凿岩巷' LIMIT 1),
 20.0, 
 342.14, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-11凿岩巷' LIMIT 1),
 35.0, 
 598.745, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-4-5凿岩巷' LIMIT 1),
 35.0, 
 598.745, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改' LIMIT 1),
 150.0, 
 3003.45, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-5-8矿块' LIMIT 1),
 15.0, 
 277.89, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-3矿块' LIMIT 1),
 30.0, 
 555.78, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-7矿块' LIMIT 1),
 60.0, 
 1111.56, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-4-5矿块' LIMIT 1),
 30.0, 
 555.78, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-5-3矿块' LIMIT 1),
 30.0, 
 555.78, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿上盘侧' LIMIT 1),
 45.0, 
 678.42, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿措施井侧' LIMIT 1),
 45.0, 
 678.42, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-1277m水平沉淀池、水仓' LIMIT 1),
 30.0, 
 600.69, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m措施井石门巷' LIMIT 1),
 70.0, 
 1401.61, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m措施井0#穿' LIMIT 1),
 50.0, 
 1001.15, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-960m/-840m斜坡道' LIMIT 1),
 60.0, 
 987.12, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改' LIMIT 1),
 45.0, 
 901.035, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 25.0, 
 500.575, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-1号充填巷' LIMIT 1),
 45.0, 
 710.685, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-2号充填巷' LIMIT 1),
 15.0, 
 236.895, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-20-2号充填巷' LIMIT 1),
 40.0, 
 631.72, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-1号充填巷' LIMIT 1),
 25.0, 
 394.825, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-3号充填巷' LIMIT 1),
 45.0, 
 710.685, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 30.0, 
 513.21, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改' LIMIT 1),
 50.0, 
 855.35, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2凿岩联巷' LIMIT 1),
 50.0, 
 855.35, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-1凿岩巷' LIMIT 1),
 60.0, 
 1026.42, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-20-7凿岩巷' LIMIT 1),
 35.0, 
 598.745, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-3凿岩巷' LIMIT 1),
 20.0, 
 342.14, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-3凿岩巷' LIMIT 1),
 30.0, 
 513.21, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-9凿岩巷' LIMIT 1),
 20.0, 
 342.14, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-12凿岩巷' LIMIT 1),
 50.0, 
 855.35, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿脉' LIMIT 1),
 30.0, 
 600.69, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改' LIMIT 1),
 45.0, 
 901.035, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿探矿' LIMIT 1),
 60.0, 
 1026.42, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-20-7矿块' LIMIT 1),
 30.0, 
 555.78, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-3矿块' LIMIT 1),
 10.0, 
 185.26, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-7矿块' LIMIT 1),
 45.0, 
 833.67, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-22-12凿岩巷' LIMIT 1),
 40.0, 
 741.04, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-9矿块' LIMIT 1),
 10.0, 
 185.26, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-11出矿巷' LIMIT 1),
 30.0, 
 555.78, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-12凿岩巷' LIMIT 1),
 15.0, 
 256.605, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-1矿块' LIMIT 1),
 30.0, 
 555.78, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-3矿块' LIMIT 1),
 60.0, 
 1111.56, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-5矿块' LIMIT 1),
 15.0, 
 277.89, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-7矿块' LIMIT 1),
 30.0, 
 555.78, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m副井石门巷' LIMIT 1),
 30.0, 
 933.9, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m副井进风井联巷' LIMIT 1),
 15.0, 
 0, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改' LIMIT 1),
 180.0, 
 3604.1, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-3号充填巷' LIMIT 1),
 28.0, 
 442.2, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改' LIMIT 1),
 80.0, 
 1601.84, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-5探矿巷' LIMIT 1),
 40.0, 
 684.28, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2-2-11凿岩巷' LIMIT 1),
 40.0, 
 684.28, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-4-5凿岩巷' LIMIT 1),
 40.0, 
 684.28, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-5-7凿岩巷' LIMIT 1),
 30.0, 
 513.21, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改' LIMIT 1),
 175.0, 
 3504.025, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改探矿巷（0#-15区域）' LIMIT 1),
 15.0, 
 300.345, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改探矿巷（0#-5区域）' LIMIT 1),
 15.0, 
 300.345, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-1矿块' LIMIT 1),
 35.0, 
 648.41, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-11矿块' LIMIT 1),
 60.0, 
 1111.56, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-5-3矿块' LIMIT 1),
 40.0, 
 741.04, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-5-7矿块' LIMIT 1),
 40.0, 
 741.04, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060中段及以下' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿副井侧' LIMIT 1),
 45.0, 
 678.42, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060中段及以下' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿措施井侧' LIMIT 1),
 30.0, 
 452.28, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060中段及以下' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-1277m沉淀池、水仓' LIMIT 1),
 30.0, 
 600.69, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m措施井侧0#穿' LIMIT 1),
 45.0, 
 901.035, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m措施井侧2#穿' LIMIT 1),
 130.0, 
 2602.99, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m/-765m斜坡道' LIMIT 1),
 45.0, 
 895.6, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-960m/-840m斜坡道' LIMIT 1),
 60.0, 
 987.12, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-1号充填巷' LIMIT 1),
 50.0, 
 789.65, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-2号充填巷' LIMIT 1),
 30.0, 
 473.79, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-1号充填巷' LIMIT 1),
 35.0, 
 552.755, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-2号充填巷' LIMIT 1),
 24.0, 
 379.032, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-22-3号充填巷' LIMIT 1),
 30.0, 
 473.79, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-3号充填巷' LIMIT 1),
 10.0, 
 157.93, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿探矿巷' LIMIT 1),
 40.0, 
 684.28, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改' LIMIT 1),
 50.0, 
 855.35, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2凿岩联巷' LIMIT 1),
 50.0, 
 855.35, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-1凿岩巷' LIMIT 1),
 35.0, 
 598.745, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-5凿岩巷' LIMIT 1),
 27.0, 
 461.889, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-7凿岩巷' LIMIT 1),
 35.0, 
 598.745, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-11凿岩巷' LIMIT 1),
 40.0, 
 684.28, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-3凿岩巷' LIMIT 1),
 40.0, 
 684.28, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-12凿岩巷' LIMIT 1),
 50.0, 
 855.35, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改' LIMIT 1),
 45.0, 
 901.035, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿探矿' LIMIT 1),
 60.0, 
 1026.42, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-20-7矿块' LIMIT 1),
 30.0, 
 555.78, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-7矿块' LIMIT 1),
 22.0, 
 407.572, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-22-12凿岩巷' LIMIT 1),
 60.0, 
 1111.56, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-12凿岩巷' LIMIT 1),
 20.0, 
 370.52, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-1矿块' LIMIT 1),
 30.0, 
 555.78, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-3矿块' LIMIT 1),
 70.0, 
 1296.82, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-5矿块' LIMIT 1),
 40.0, 
 741.04, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020中段' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-7矿块' LIMIT 1),
 30.0, 
 555.78, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m副井侧石门巷' LIMIT 1),
 130.0, 
 4046.9, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m进风井联巷' LIMIT 1),
 20.0, 
 0.0, 
 '202507', 
 'admin', 
 'admin');