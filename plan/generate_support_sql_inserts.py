#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
生成SQL插入语句脚本
从CSV文件生成PostgreSQL数据库的INSERT语句 - 支护计划数据
"""

import pandas as pd
import re
import os
from datetime import datetime
from decimal import Decimal

def safe_value(value, default=0, as_decimal=True):
    """安全地转换值为数字"""
    if pd.isna(value) or value == '' or value is None:
        return Decimal(default) if as_decimal else default
    
    try:
        if isinstance(value, str):
            value = value.replace(',', '').strip()
            if value == '':
                return Decimal(default) if as_decimal else default
            return Decimal(value) if as_decimal else float(value)
        elif isinstance(value, (int, float)):
            return Decimal(str(value)) if as_decimal else value
        else:
            return Decimal(str(value)) if as_decimal else float(value)
    except (ValueError, TypeError):
        print(f"警告: 无法将值 '{value}' 转换为数字，使用默认值 {default}")
        return Decimal(default) if as_decimal else default

def clean_stope_name(stope_name):
    """清理采场名称，去除换行符和多余空格"""
    if not stope_name or pd.isna(stope_name):
        return ""
    
    # 转换为字符串并去除换行符
    cleaned = str(stope_name).replace('\n', '').replace('\r', '').strip()
    # 去除多余的空格
    cleaned = ' '.join(cleaned.split())
    return cleaned

def extract_month_from_filename(filename):
    """从文件名中提取月份"""
    match = re.search(r'Sheet(\d+)', filename)
    if match:
        return match.group(1)
    return "01"

def parse_csv_data(csv_file):
    """解析CSV文件数据"""
    print(f"处理文件: {csv_file}")
    
    # 从文件名提取月份
    month = extract_month_from_filename(csv_file)
    plan_date = f"2025{month.zfill(2)}"
    
    # 读取CSV文件
    df = pd.read_csv(csv_file, encoding='utf-8')
    
    processed_data = []
    current_department = None
    current_working_face = None
    
    for index, row in df.iterrows():
        # 跳过前3行标题
        if index < 3:
            continue
            
        # 获取单位（部门）
        unit = str(row.iloc[0]).strip() if not pd.isna(row.iloc[0]) else ""
        if unit and unit not in ['nan', '']:
            if '中矿' in unit:
                current_department = 2  # 中矿
            elif '涟邵' in unit:
                current_department = 3  # 涟邵
            else:
                current_department = None
        
        # 获取中段（工作面）
        working_face = str(row.iloc[1]).strip() if not pd.isna(row.iloc[1]) else ""
        if working_face and working_face not in ['nan', '']:
            current_working_face = working_face
        
        # 获取位置（采场）- 清理换行符
        stope_name = clean_stope_name(row.iloc[2])
        
        # 跳过小计行和空行
        if not stope_name or stope_name in ['nan', ''] or '小计' in stope_name or '合计' in stope_name:
            continue
            
        # 跳过没有部门或工作面的行
        if not current_department or not current_working_face:
            continue
        
        # 获取支护数据 - 第7列是支护米数，第8列是支护体积，使用Decimal处理
        support_meter = safe_value(row.iloc[7], 0, as_decimal=True)  # 支护米数
        support_volume = safe_value(row.iloc[8], 0, as_decimal=True)  # 支护体积
        
        # 只处理有支护数据的行
        if support_meter > 0 or support_volume > 0:
            processed_data.append({
                'project_department_id': current_department,
                'working_face_name': current_working_face,
                'stope_name': stope_name,
                'support_meter': support_meter,
                'support_volume': support_volume,
                'plan_date': plan_date
            })
    
    return processed_data

def generate_sql_inserts(csv_files, output_file='plan_support_monthly_inserts.sql'):
    """生成SQL插入语句"""
    all_data = []
    
    # 处理所有CSV文件
    for csv_file in csv_files:
        if os.path.exists(csv_file):
            data = parse_csv_data(csv_file)
            all_data.extend(data)
        else:
            print(f"文件不存在: {csv_file}")
    
    if not all_data:
        print("没有找到有效数据")
        return
    
    # 生成SQL语句
    sql_statements = []
    sql_statements.append("-- 支护计划月度数据插入语句")
    sql_statements.append(f"-- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    sql_statements.append(f"-- 数据条数: {len(all_data)}")
    sql_statements.append("")
    
    for data in all_data:
        # 转义单引号以防SQL注入
        stope_name_escaped = data['stope_name'].replace("'", "''")
        working_face_escaped = data['working_face_name'].replace("'", "''")
        
        sql = f"""INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
({data['project_department_id']}, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '{working_face_escaped}' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '{stope_name_escaped}' LIMIT 1),
 {data['support_meter']}, 
 {data['support_volume']}, 
 '{data['plan_date']}', 
 'admin', 
 'admin');"""
        sql_statements.append(sql)
    
    # 写入文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(sql_statements))
    
    print(f"SQL语句已生成到文件: {output_file}")
    print(f"共生成 {len(all_data)} 条插入语句")

if __name__ == "__main__":
    csv_files = [
        'Book2_Sheet1.csv',
        'Book2_Sheet2.csv',
        'Book2_Sheet3.csv',
        'Book2_Sheet4.csv',
        'Book2_Sheet5.csv',
        'Book2_Sheet6.csv',
        'Book2_Sheet7.csv'
    ]
    
    generate_sql_inserts(csv_files)