create table public.plan_drift_monthly
(
    id                    bigserial
        primary key,
    project_department_id bigint,
    working_face_id       bigint,
    stope_id              bigint,
    drift_meter           numeric,
    drift_volume          numeric,
    is_priority           smallint     default 0,
    plan_date             varchar(6),
    create_by             varchar(64),
    create_time           timestamp(6),
    update_by             varchar(64),
    update_time           timestamp(6) default now()
);

comment on table public.plan_drift_monthly is '掘进月计划';

comment on column public.plan_drift_monthly.id is 'ID';

comment on column public.plan_drift_monthly.project_department_id is '项目部门ID';

comment on column public.plan_drift_monthly.working_face_id is '工作面ID';

comment on column public.plan_drift_monthly.stope_id is '采场ID';

comment on column public.plan_drift_monthly.drift_meter is '掘进米数';

comment on column public.plan_drift_monthly.drift_volume is '掘进方量';

comment on column public.plan_drift_monthly.is_priority is '是否重点';

comment on column public.plan_drift_monthly.plan_date is '计划月份';

comment on column public.plan_drift_monthly.create_by is '创建人';

comment on column public.plan_drift_monthly.create_time is '创建时间';

comment on column public.plan_drift_monthly.update_by is '操作人';

comment on column public.plan_drift_monthly.update_time is '更新时间';

alter table public.plan_drift_monthly
    owner to postgres;


create table public.base_working_face
(
    working_face_id   bigserial
        primary key,
    working_face_name varchar(64),
    status            smallint,
    create_by         varchar(64),
    create_time       timestamp(6),
    update_by         varchar(64),
    update_time       timestamp(6) default now(),
    start_time        timestamp(6),
    end_time          timestamp(6),
    is_delete         integer      default 0 not null
);

comment on table public.base_working_face is '中段-工作面配置';

comment on column public.base_working_face.working_face_id is '工作面ID';

comment on column public.base_working_face.working_face_name is '工作面名称';

comment on column public.base_working_face.status is '状态';

comment on column public.base_working_face.create_by is '创建人';

comment on column public.base_working_face.create_time is '创建时间';

comment on column public.base_working_face.update_by is '操作人';

comment on column public.base_working_face.update_time is '更新时间';

comment on column public.base_working_face.start_time is '工作面开始时间';

comment on column public.base_working_face.end_time is '工作面结束时间';

comment on column public.base_working_face.is_delete is '是否删除';

alter table public.base_working_face
    owner to postgres;

create table public.base_stope
(
    stope_id        bigserial
        primary key,
    stope_name      varchar(64),
    working_face_id bigint,
    status          smallint,
    create_by       varchar(64),
    create_time     timestamp(6),
    update_by       varchar(64),
    update_time     timestamp(6) default now(),
    start_time      timestamp(6),
    end_time        timestamp(6),
    is_delete       integer      default 0 not null
);

comment on table public.base_stope is '采场配置';

comment on column public.base_stope.stope_id is '采场ID';

comment on column public.base_stope.stope_name is '采场名称';

comment on column public.base_stope.working_face_id is '工作面ID';

comment on column public.base_stope.status is '状态';

comment on column public.base_stope.create_by is '创建人';

comment on column public.base_stope.create_time is '创建时间';

comment on column public.base_stope.update_by is '操作人';

comment on column public.base_stope.update_time is '更新时间';

comment on column public.base_stope.start_time is '采场开始时间';

comment on column public.base_stope.end_time is '采场结束时间';

comment on column public.base_stope.is_delete is '是否删除';

alter table public.base_stope
    owner to postgres;

create table public.plan_support_monthly
(
    id                      bigserial
        primary key,
    project_department_id   bigint,
    bolt_mesh_support_meter numeric,
    shotcrete_support_meter numeric,
    support_meter           numeric,
    plan_date               varchar(6),
    create_by               varchar(64),
    create_time             timestamp(6),
    update_by               varchar(64),
    update_time             timestamp(6) default now()
);

comment on table public.plan_support_monthly is '支护月计划';

comment on column public.plan_support_monthly.id is 'ID';

comment on column public.plan_support_monthly.project_department_id is '项目部门ID';

comment on column public.plan_support_monthly.bolt_mesh_support_meter is '锚网支护米数';

comment on column public.plan_support_monthly.shotcrete_support_meter is '喷浆支护米数';

comment on column public.plan_support_monthly.support_meter is '支护米数';

comment on column public.plan_support_monthly.plan_date is '计划月份';

comment on column public.plan_support_monthly.create_by is '创建人';

comment on column public.plan_support_monthly.create_time is '创建时间';

comment on column public.plan_support_monthly.update_by is '操作人';

comment on column public.plan_support_monthly.update_time is '更新时间';

alter table public.plan_support_monthly
    owner to postgres;









