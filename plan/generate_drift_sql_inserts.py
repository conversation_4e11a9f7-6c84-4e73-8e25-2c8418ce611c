#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
生成SQL插入语句脚本
从CSV文件生成PostgreSQL数据库的INSERT语句 - 掘进计划数据
"""

import pandas as pd
import re
import os
from datetime import datetime

def safe_value(value, default=0):
    """安全地转换值为数字"""
    if pd.isna(value) or value == '' or value is None:
        return default
    try:
        return float(value)
    except (ValueError, TypeError):
        return default

def extract_month_from_filename(filename):
    """从文件名中提取月份"""
    match = re.search(r'Sheet(\d+)', filename)
    if match:
        return match.group(1)
    return "01"

def parse_csv_data(csv_file):
    """解析CSV文件数据"""
    print(f"处理文件: {csv_file}")
    
    # 从文件名提取月份
    month = extract_month_from_filename(csv_file)
    plan_date = f"2025{month.zfill(2)}"
    
    # 读取CSV文件
    df = pd.read_csv(csv_file, encoding='utf-8')
    
    processed_data = []
    current_department = None
    current_working_face = None
    
    for index, row in df.iterrows():
        # 跳过前3行标题
        if index < 3:
            continue
            
        # 获取单位（部门）
        unit = str(row.iloc[0]).strip() if not pd.isna(row.iloc[0]) else ""
        if unit and unit not in ['nan', '']:
            if '中矿' in unit:
                current_department = 2  # 中矿
            elif '涟邵' in unit:
                current_department = 3  # 涟邵
            else:
                current_department = None
        
        # 获取中段（工作面）
        working_face = str(row.iloc[1]).strip() if not pd.isna(row.iloc[1]) else ""
        if working_face and working_face not in ['nan', '']:
            current_working_face = working_face
        
        # 获取位置（采场）
        stope_name = str(row.iloc[2]).strip() if not pd.isna(row.iloc[2]) else ""
        
        # 跳过小计行和空行
        if not stope_name or stope_name in ['nan', ''] or '小计' in stope_name or '合计' in stope_name:
            continue
            
        # 跳过没有部门或工作面的行
        if not current_department or not current_working_face:
            continue
        
        # 获取掘进数据 - 第5列是掘进米数，第6列是掘进体积
        drift_meter = safe_value(row.iloc[5], 0)  # 掘进米数
        drift_volume = safe_value(row.iloc[6], 0)  # 掘进体积
        
        # 只处理有掘进数据的行
        if drift_meter > 0 or drift_volume > 0:
            processed_data.append({
                'project_department_id': current_department,
                'working_face_name': current_working_face,
                'stope_name': stope_name,
                'drift_meter': drift_meter,
                'drift_volume': drift_volume,
                'plan_date': plan_date
            })
    
    return processed_data

def generate_sql_inserts(csv_files, output_file='plan_drift_monthly_inserts.sql'):
    """生成SQL插入语句"""
    all_data = []
    
    # 处理所有CSV文件
    for csv_file in csv_files:
        if os.path.exists(csv_file):
            data = parse_csv_data(csv_file)
            all_data.extend(data)
        else:
            print(f"文件不存在: {csv_file}")
    
    if not all_data:
        print("没有找到有效数据")
        return
    
    # 生成SQL语句
    sql_statements = []
    sql_statements.append("-- 掘进计划月度数据插入语句")
    sql_statements.append(f"-- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    sql_statements.append(f"-- 数据条数: {len(all_data)}")
    sql_statements.append("")
    
    for data in all_data:
        sql = f"""INSERT INTO plan_drift_monthly 
(project_department_id, working_face_id, stope_id, drift_meter, drift_volume, plan_date, create_by, update_by)
VALUES 
({data['project_department_id']}, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '{data['working_face_name']}' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '{data['stope_name']}' LIMIT 1),
 {data['drift_meter']}, 
 {data['drift_volume']}, 
 '{data['plan_date']}', 
 'admin', 
 'admin');"""
        sql_statements.append(sql)
    
    # 写入文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(sql_statements))
    
    print(f"SQL语句已生成到文件: {output_file}")
    print(f"共生成 {len(all_data)} 条插入语句")

if __name__ == "__main__":
    csv_files = [
        'Book2_Sheet1.csv',
        'Book2_Sheet2.csv',
        'Book2_Sheet3.csv',
        'Book2_Sheet4.csv',
        'Book2_Sheet5.csv',
        'Book2_Sheet6.csv',
        'Book2_Sheet7.csv'
    ]
    
    generate_sql_inserts(csv_files)