-- 支护计划月度数据插入语句
-- 生成时间: 2025-07-21 13:56:03
-- 数据条数: 341

INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 45, 
 94.725, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-4充填联巷' LIMIT 1),
 45, 
 47.475, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-5-3号充填巷' LIMIT 1),
 20, 
 21.1, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-15-3切割巷' LIMIT 1),
 20, 
 22.28, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 125, 
 290.625, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-5-8凿岩巷' LIMIT 1),
 40, 
 44.56, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 80, 
 186, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-15-3矿块' LIMIT 1),
 30, 
 86.07, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-5-8凿岩巷' LIMIT 1),
 45, 
 50.13, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-7矿块（出矿巷）' LIMIT 1),
 35, 
 100.415, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-11矿块（出矿巷）' LIMIT 1),
 40, 
 114.76, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-4出矿联巷' LIMIT 1),
 20, 
 57.38, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '4#穿' LIMIT 1),
 55, 
 87.505, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿上盘侧' LIMIT 1),
 40, 
 63.64, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿措施井侧' LIMIT 1),
 85, 
 135.235, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '溜井工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-1060m水平6-4溜井硐室' LIMIT 1),
 7.3, 
 0, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '溜井工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-1060m水平4-3溜井硐室' LIMIT 1),
 7.3, 
 0, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '措施井车场' LIMIT 1),
 40, 
 93, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-960m/-840m斜坡道(通-840m中段)' LIMIT 1),
 60, 
 65.640, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '胶带斜井尾部硐室联巷' LIMIT 1),
 30, 
 48.99, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-1080m/-1123m斜坡道' LIMIT 1),
 60, 
 170.34, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-1100m破碎配电硐室' LIMIT 1),
 10, 
 0, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿脉' LIMIT 1),
 130, 
 302.25, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-22-3号充填巷' LIMIT 1),
 40, 
 42.2, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-2号充填巷' LIMIT 1),
 30, 
 31.65, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-3号充填巷' LIMIT 1),
 40, 
 42.2, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 40, 
 44.56, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20采场联巷' LIMIT 1),
 40, 
 44.56, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-9凿岩巷' LIMIT 1),
 40, 
 44.56, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2采场联巷' LIMIT 1),
 40, 
 44.56, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-9凿岩巷' LIMIT 1),
 15, 
 16.71, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿脉' LIMIT 1),
 130, 
 302.25, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-1矿块' LIMIT 1),
 60, 
 172.14, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-9矿块' LIMIT 1),
 65, 
 186.485, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m中段石门巷（副井侧）' LIMIT 1),
 30, 
 0, 
 '202501', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 30, 
 63.15, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-4充填联巷' LIMIT 1),
 25, 
 26.375, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-1充填巷' LIMIT 1),
 15, 
 15.825, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-2充填巷' LIMIT 1),
 15, 
 15.825, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-3充填巷' LIMIT 1),
 15, 
 15.825, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '混凝土搅拌硐室联巷' LIMIT 1),
 30, 
 31.65, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '中转成品矿仓联巷' LIMIT 1),
 45, 
 118.845, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '胶带斜井尾部硐室联巷' LIMIT 1),
 30, 
 75.63, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 55, 
 127.875, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-5-8凿岩巷' LIMIT 1),
 40, 
 44.56, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-3凿岩巷' LIMIT 1),
 15, 
 16.71, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-7凿岩巷' LIMIT 1),
 40, 
 44.56, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-11凿岩巷' LIMIT 1),
 15, 
 16.71, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 55, 
 127.875, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿下盘沿脉探矿巷' LIMIT 1),
 20, 
 57.38, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-5-8矿块' LIMIT 1),
 40, 
 114.76, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-11矿块' LIMIT 1),
 40, 
 114.76, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '4#穿' LIMIT 1),
 30, 
 47.73, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿上盘侧' LIMIT 1),
 30, 
 47.73, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿措施井侧' LIMIT 1),
 55, 
 87.505, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '措施井车场' LIMIT 1),
 40, 
 93, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-960m/-840m斜坡道(通-840m中段)' LIMIT 1),
 50, 
 54.700, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-1080m/-1123m斜坡道' LIMIT 1),
 50, 
 141.95, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-1100m破碎配电硐室' LIMIT 1),
 15, 
 0, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿脉' LIMIT 1),
 95, 
 220.875, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-22-3号充填巷' LIMIT 1),
 35, 
 36.925, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-3号充填巷' LIMIT 1),
 40, 
 42.2, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 40, 
 44.56, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20采场联巷' LIMIT 1),
 20, 
 22.28, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-9凿岩巷' LIMIT 1),
 30, 
 33.42, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-7凿岩巷' LIMIT 1),
 15, 
 16.71, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2采场联巷' LIMIT 1),
 40, 
 44.56, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-7凿岩巷' LIMIT 1),
 15, 
 16.71, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿脉' LIMIT 1),
 95, 
 220.875, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-9矿块' LIMIT 1),
 15, 
 43.035, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-5矿块' LIMIT 1),
 15, 
 43.035, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-1矿块' LIMIT 1),
 50, 
 143.45, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-7矿块' LIMIT 1),
 15, 
 43.035, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m中段石门巷（副井侧）' LIMIT 1),
 30, 
 0, 
 '202502', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-15探矿巷' LIMIT 1),
 30, 
 31.65, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 20, 
 42.1, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-1号充填巷' LIMIT 1),
 45, 
 47.475, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-2号充填巷' LIMIT 1),
 30, 
 31.65, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '进风井联巷' LIMIT 1),
 26, 
 0, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 100, 
 232.5, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-5-4凿岩巷' LIMIT 1),
 20, 
 22.28, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-3凿岩巷' LIMIT 1),
 45, 
 50.13, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-7凿岩巷' LIMIT 1),
 30, 
 33.42, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-11凿岩巷' LIMIT 1),
 15, 
 16.71, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-15探矿巷' LIMIT 1),
 40, 
 44.56, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 110, 
 255.75, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-15探矿巷' LIMIT 1),
 40, 
 114.76, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-5-4矿块' LIMIT 1),
 50, 
 143.45, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-5-8矿块' LIMIT 1),
 50, 
 143.45, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-11矿块' LIMIT 1),
 30, 
 86.07, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '4#穿' LIMIT 1),
 55, 
 87.505, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿上盘侧' LIMIT 1),
 45, 
 71.595, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿措施井侧' LIMIT 1),
 85, 
 135.235, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#主井粉矿回收巷' LIMIT 1),
 35, 
 0, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-1277m水平沉淀池、水仓' LIMIT 1),
 47.4, 
 0, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m措施井石门巷' LIMIT 1),
 55, 
 127.875, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-960m/-840m斜坡道' LIMIT 1),
 55, 
 60.170, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿脉' LIMIT 1),
 50, 
 116.3, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-22-3号充填巷' LIMIT 1),
 20, 
 21.1, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-3号充填巷' LIMIT 1),
 20, 
 21.1, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-1号充填巷' LIMIT 1),
 10, 
 10.6, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-3号充填巷' LIMIT 1),
 10, 
 10.6, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-20-1充填巷' LIMIT 1),
 45, 
 47.5, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-1充填巷' LIMIT 1),
 30, 
 31.7, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-2充填巷' LIMIT 1),
 15, 
 15.8, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-3充填巷' LIMIT 1),
 15, 
 15.8, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 40, 
 44.56, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-3凿岩巷' LIMIT 1),
 25, 
 27.85, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-7凿岩巷' LIMIT 1),
 30, 
 33.42, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-3凿岩巷' LIMIT 1),
 45, 
 50.13, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-7凿岩巷' LIMIT 1),
 30, 
 33.42, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-9凿岩巷' LIMIT 1),
 20, 
 22.28, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-12凿岩巷' LIMIT 1),
 15, 
 16.71, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-22-12凿岩巷' LIMIT 1),
 15, 
 16.71, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2采场联巷' LIMIT 1),
 55, 
 61.27, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-5凿岩巷' LIMIT 1),
 15, 
 16.71, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿脉' LIMIT 1),
 45, 
 104.625, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-5矿块' LIMIT 1),
 35, 
 100.415, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-1矿块' LIMIT 1),
 65, 
 186.485, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-22-11出矿巷' LIMIT 1),
 30, 
 86.07, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-7矿块' LIMIT 1),
 40, 
 114.76, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-11出矿巷' LIMIT 1),
 45, 
 129.105, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-3矿块' LIMIT 1),
 55, 
 157.795, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m副井石门巷' LIMIT 1),
 45, 
 0, 
 '202503', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-15探矿巷' LIMIT 1),
 30, 
 31.7, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-1号充填巷' LIMIT 1),
 40, 
 42.2, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-2号充填巷' LIMIT 1),
 15, 
 15.8, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-3号充填巷' LIMIT 1),
 15, 
 15.8, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '进风井联巷' LIMIT 1),
 20, 
 0, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 125, 
 290.625, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-3凿岩巷' LIMIT 1),
 50, 
 55.7, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-7凿岩巷' LIMIT 1),
 10, 
 11.14, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-11凿岩巷' LIMIT 1),
 15, 
 16.71, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-4-5凿岩巷' LIMIT 1),
 15, 
 16.71, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-15探矿巷' LIMIT 1),
 35, 
 38.99, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 80, 
 186, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-15探矿巷' LIMIT 1),
 40, 
 114.76, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-5-4矿块' LIMIT 1),
 20, 
 57.38, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-5-8矿块' LIMIT 1),
 20, 
 57.38, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-3矿块' LIMIT 1),
 70, 
 200.83, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-7矿块' LIMIT 1),
 20, 
 57.38, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-4-5矿块' LIMIT 1),
 20, 
 57.38, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '4#穿' LIMIT 1),
 35, 
 55.685, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿上盘侧' LIMIT 1),
 45, 
 71.595, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿措施井侧' LIMIT 1),
 85, 
 135.235, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#主井粉矿回收巷' LIMIT 1),
 35, 
 0, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-1277m水平沉淀池、水仓' LIMIT 1),
 47.4, 
 0, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m措施井石门巷' LIMIT 1),
 45, 
 104.625, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-960m/-840m斜坡道' LIMIT 1),
 55, 
 60.170, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 45, 
 104.625, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-3号充填巷' LIMIT 1),
 30, 
 31.65, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-20-1充填巷' LIMIT 1),
 35, 
 36.925, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-1充填巷' LIMIT 1),
 30, 
 31.65, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-2充填巷' LIMIT 1),
 15, 
 15.825, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-3充填巷' LIMIT 1),
 15, 
 15.825, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-7凿岩巷' LIMIT 1),
 40, 
 44.56, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-3凿岩巷' LIMIT 1),
 35, 
 38.99, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-7凿岩巷' LIMIT 1),
 30, 
 33.42, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-9凿岩巷' LIMIT 1),
 35, 
 38.99, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-12凿岩巷' LIMIT 1),
 40, 
 44.56, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-22-12凿岩巷' LIMIT 1),
 30, 
 33.42, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2凿岩联巷' LIMIT 1),
 55, 
 61.27, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-5凿岩巷' LIMIT 1),
 30, 
 33.42, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿脉' LIMIT 1),
 50, 
 116.25, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-5矿块' LIMIT 1),
 35, 
 100.415, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-1矿块' LIMIT 1),
 65, 
 186.485, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-22-11出矿巷' LIMIT 1),
 30, 
 86.07, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-7矿块' LIMIT 1),
 30, 
 86.07, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-9出矿巷' LIMIT 1),
 25, 
 71.725, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-11出矿巷' LIMIT 1),
 30, 
 86.07, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-7矿块' LIMIT 1),
 40, 
 114.76, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m副井石门巷' LIMIT 1),
 45, 
 0, 
 '202504', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-15探矿巷' LIMIT 1),
 24, 
 25.3, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '下盘沿脉（往0#穿改）' LIMIT 1),
 30, 
 69.1, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改' LIMIT 1),
 25, 
 58.1, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-1号充填巷' LIMIT 1),
 40, 
 42.2, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-2号充填巷' LIMIT 1),
 30, 
 31.7, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-3号充填巷' LIMIT 1),
 30, 
 31.7, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 40, 
 44.56, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-7凿岩巷' LIMIT 1),
 35, 
 38.99, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-11凿岩巷' LIMIT 1),
 30, 
 33.42, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-4-5凿岩巷' LIMIT 1),
 15, 
 16.71, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-15探矿巷' LIMIT 1),
 40, 
 44.56, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改' LIMIT 1),
 30, 
 33.42, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 35, 
 81.375, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-15探矿巷' LIMIT 1),
 15, 
 43.035, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改' LIMIT 1),
 55, 
 127.875, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '下盘沿脉（往0#穿改）' LIMIT 1),
 25, 
 57.6, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-5-4矿块' LIMIT 1),
 36, 
 103.284, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-5-8矿块' LIMIT 1),
 55, 
 157.795, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-3矿块' LIMIT 1),
 70, 
 200.83, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-7矿块' LIMIT 1),
 25, 
 71.725, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-4-5矿块' LIMIT 1),
 20, 
 57.38, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '4-1溜井卸矿硐室' LIMIT 1),
 14, 
 0, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿措施井侧' LIMIT 1),
 45, 
 71.595, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿副井侧' LIMIT 1),
 45, 
 71.595, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '下盘沿脉（往6#穿）' LIMIT 1),
 45, 
 71.595, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#主井粉矿回收巷' LIMIT 1),
 19, 
 54.15, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-1277m水平沉淀池、水仓' LIMIT 1),
 30, 
 28.8, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '4-1溜井振动放矿机硐室' LIMIT 1),
 7.3, 
 0, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m措施井石门巷（往2号穿）' LIMIT 1),
 45, 
 120.645, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m措施井0号穿' LIMIT 1),
 30, 
 80.43, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m措施井0号穿倒车硐室' LIMIT 1),
 10, 
 26.81, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-865m辅助运输巷' LIMIT 1),
 30, 
 0, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-960m/-840m斜坡道' LIMIT 1),
 55, 
 60.170, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改' LIMIT 1),
 15, 
 34.875, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 30, 
 69.75, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-20-2号充填巷' LIMIT 1),
 15, 
 15.825, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-3号充填巷' LIMIT 1),
 25, 
 26.375, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-1充填巷' LIMIT 1),
 15, 
 15.825, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-2充填巷' LIMIT 1),
 15, 
 15.825, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-3充填巷' LIMIT 1),
 15, 
 15.825, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-1充填巷' LIMIT 1),
 25, 
 26.375, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 30, 
 33.42, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-20-7凿岩巷' LIMIT 1),
 15, 
 16.71, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-3凿岩巷' LIMIT 1),
 40, 
 44.56, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-3凿岩巷' LIMIT 1),
 20, 
 22.28, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-7凿岩巷' LIMIT 1),
 10, 
 11.14, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-9凿岩巷' LIMIT 1),
 20, 
 22.28, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-12凿岩巷' LIMIT 1),
 40, 
 44.56, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-22-12凿岩巷' LIMIT 1),
 30, 
 33.42, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2凿岩联巷' LIMIT 1),
 45, 
 50.13, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-1凿岩巷' LIMIT 1),
 15, 
 16.71, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改' LIMIT 1),
 25, 
 27.85, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿脉' LIMIT 1),
 25, 
 58.125, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-1矿块' LIMIT 1),
 30, 
 86.07, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-20-7矿块' LIMIT 1),
 15, 
 43.035, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-22-11出矿巷' LIMIT 1),
 10, 
 28.69, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-7矿块' LIMIT 1),
 35, 
 100.415, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-9矿块' LIMIT 1),
 55, 
 157.795, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-11出矿巷' LIMIT 1),
 30, 
 86.07, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-3矿块' LIMIT 1),
 30, 
 86.07, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-7矿块' LIMIT 1),
 40, 
 114.76, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-3矿块' LIMIT 1),
 45, 
 129.105, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改' LIMIT 1),
 15, 
 34.875, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '无轨维修硐室' LIMIT 1),
 74.8, 
 189.3936, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '无轨维修硐室联巷' LIMIT 1),
 54.3, 
 102.1926, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '无轨维修其他硐室' LIMIT 1),
 48, 
 53.808,
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m副井石门巷' LIMIT 1),
 45, 
 153.9, 
 '202505', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改' LIMIT 1),
 90, 
 209.3, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-19探矿巷' LIMIT 1),
 35, 
 80.6, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-2号充填巷' LIMIT 1),
 40, 
 42.2, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-3号充填巷' LIMIT 1),
 30, 
 31.7, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 20, 
 46.5, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改' LIMIT 1),
 60, 
 139.5, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-5探矿巷' LIMIT 1),
 40, 
 44.56, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-7凿岩巷' LIMIT 1),
 30, 
 33.42, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-11凿岩巷' LIMIT 1),
 30, 
 33.42, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-4-5凿岩巷' LIMIT 1),
 30, 
 33.42, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改' LIMIT 1),
 130, 
 302.25, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-5-8矿块' LIMIT 1),
 15, 
 43.035, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-3矿块' LIMIT 1),
 30, 
 86.07, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-7矿块' LIMIT 1),
 55, 
 157.795, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-4-5矿块' LIMIT 1),
 30, 
 86.07, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-5-3矿块' LIMIT 1),
 30, 
 86.07, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿上盘侧' LIMIT 1),
 45, 
 71.595, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿措施井侧' LIMIT 1),
 45, 
 71.595, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-1277m水平沉淀池、水仓' LIMIT 1),
 30, 
 69.75, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m措施井石门巷' LIMIT 1),
 65, 
 151.125, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m措施井0#穿' LIMIT 1),
 45, 
 104.625, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-960m/-840m斜坡道' LIMIT 1),
 55, 
 60.170, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改' LIMIT 1),
 40, 
 93, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 25, 
 58.125, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-1号充填巷' LIMIT 1),
 40, 
 42.2, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-2号充填巷' LIMIT 1),
 15, 
 15.825, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-20-2号充填巷' LIMIT 1),
 40, 
 42.2, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-1号充填巷' LIMIT 1),
 25, 
 26.375, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-3号充填巷' LIMIT 1),
 45, 
 47.475, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿' LIMIT 1),
 20, 
 22.28, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改' LIMIT 1),
 45, 
 50.13, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2凿岩联巷' LIMIT 1),
 45, 
 50.13, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-1凿岩巷' LIMIT 1),
 55, 
 61.27, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-20-7凿岩巷' LIMIT 1),
 35, 
 38.99, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-3凿岩巷' LIMIT 1),
 20, 
 22.28, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-3凿岩巷' LIMIT 1),
 30, 
 33.42, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-9凿岩巷' LIMIT 1),
 20, 
 22.28, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-12凿岩巷' LIMIT 1),
 50, 
 55.7, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿脉' LIMIT 1),
 30, 
 69.75, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改' LIMIT 1),
 40, 
 93, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿探矿' LIMIT 1),
 50, 
 55.7, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-20-7矿块' LIMIT 1),
 30, 
 86.07, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-3矿块' LIMIT 1),
 10, 
 28.69, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-7矿块' LIMIT 1),
 40, 
 114.76, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-22-12凿岩巷' LIMIT 1),
 35, 
 100.415, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-9矿块' LIMIT 1),
 10, 
 28.69, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-11出矿巷' LIMIT 1),
 25, 
 71.725, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-12凿岩巷' LIMIT 1),
 15, 
 16.71, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-1矿块' LIMIT 1),
 30, 
 86.07, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-3矿块' LIMIT 1),
 60, 
 172.14, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-5矿块' LIMIT 1),
 15, 
 43.035, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-7矿块' LIMIT 1),
 30, 
 86.07, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m副井石门巷' LIMIT 1),
 30, 
 102.6, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m副井进风井联巷' LIMIT 1),
 15, 
 0, 
 '202506', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改' LIMIT 1),
 170, 
 395.3, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-3号充填巷' LIMIT 1),
 28, 
 29.5, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改' LIMIT 1),
 70, 
 162.75, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-5探矿巷' LIMIT 1),
 40, 
 44.56, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2-2-11凿岩巷' LIMIT 1),
 40, 
 44.56, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-4-5凿岩巷' LIMIT 1),
 40, 
 44.56, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-5-7凿岩巷' LIMIT 1),
 25, 
 27.85, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改' LIMIT 1),
 170, 
 395.25, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改探矿巷（0#-15区域）' LIMIT 1),
 15, 
 34.875, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改探矿巷（0#-5区域）' LIMIT 1),
 15, 
 34.875, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-1矿块' LIMIT 1),
 35, 
 100.415, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-11矿块' LIMIT 1),
 60, 
 172.14, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-5-3矿块' LIMIT 1),
 35, 
 100.415, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-5-7矿块' LIMIT 1),
 35, 
 100.415, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿副井侧' LIMIT 1),
 45, 
 71.595, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿措施井侧' LIMIT 1),
 30, 
 47.73, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1060m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-1277m沉淀池、水仓' LIMIT 1),
 30, 
 69.75, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m措施井侧0#穿' LIMIT 1),
 40, 
 93, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m措施井侧2#穿' LIMIT 1),
 120, 
 279, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m/-765m斜坡道' LIMIT 1),
 40, 
 74.1, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(2, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-960m/-840m斜坡道' LIMIT 1),
 55, 
 60.170, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-1号充填巷' LIMIT 1),
 45, 
 47.475, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-2号充填巷' LIMIT 1),
 30, 
 31.65, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-1号充填巷' LIMIT 1),
 35, 
 36.925, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-2号充填巷' LIMIT 1),
 24, 
 25.32, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-22-3号充填巷' LIMIT 1),
 30, 
 31.65, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-960m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-3号充填巷' LIMIT 1),
 10, 
 10.55, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿探矿巷' LIMIT 1),
 40, 
 44.56, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改' LIMIT 1),
 45, 
 50.13, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2凿岩联巷' LIMIT 1),
 45, 
 50.13, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-1凿岩巷' LIMIT 1),
 35, 
 38.99, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-2-5凿岩巷' LIMIT 1),
 27, 
 30.078, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-7凿岩巷' LIMIT 1),
 35, 
 38.99, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-2-11凿岩巷' LIMIT 1),
 35, 
 38.99, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-3凿岩巷' LIMIT 1),
 40, 
 44.56, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-992m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-12凿岩巷' LIMIT 1),
 50, 
 55.7, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#穿改' LIMIT 1),
 40, 
 93, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '6#穿探矿' LIMIT 1),
 50, 
 55.7, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-20-7矿块' LIMIT 1),
 30, 
 86.07, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '0#-26-7矿块' LIMIT 1),
 22, 
 63.118, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-22-12凿岩巷' LIMIT 1),
 60, 
 172.14, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-20-12凿岩巷' LIMIT 1),
 20, 
 57.38, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-1矿块' LIMIT 1),
 30, 
 86.07, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-3矿块' LIMIT 1),
 60, 
 172.14, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-5矿块' LIMIT 1),
 40, 
 114.76, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '-1020m' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '2#-12-7矿块' LIMIT 1),
 30, 
 86.07, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m副井侧石门巷' LIMIT 1),
 120, 
 410.4, 
 '202507', 
 'admin', 
 'admin');
INSERT INTO plan_support_monthly 
(project_department_id, working_face_id, stope_id, support_meter, support_volume, plan_date, create_by, update_by)
VALUES 
(3, 
 (SELECT working_face_id FROM base_working_face WHERE working_face_name = '上采区工程' LIMIT 1),
 (SELECT stope_id FROM base_stope WHERE stope_name = '-840m进风井联巷' LIMIT 1),
 20, 
 0, 
 '202507', 
 'admin', 
 'admin');