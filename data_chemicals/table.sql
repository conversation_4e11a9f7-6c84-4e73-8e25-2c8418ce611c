create table public.data_chemicals
(
    id                     serial
        primary key,
    report_date            date not null
        constraint data_chemicals_pk
            unique,
    flocculant_usage       numeric(10, 2),
    auxiliary_filter_usage numeric(10, 2),
    create_time            timestamp(6) default now(),
    update_time            timestamp(6) default now(),
    create_by              varchar(64),
    update_by              varchar(64)
);

comment on table public.data_chemicals is '药剂';

comment on column public.data_chemicals.report_date is '日期';

comment on column public.data_chemicals.flocculant_usage is '絮凝剂-使用量（kg）';

comment on column public.data_chemicals.auxiliary_filter_usage is '助滤剂-使用量（kg）';

alter table public.data_chemicals
    owner to postgres;

