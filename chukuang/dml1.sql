INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-01', 1, 27, 1, 495.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-01', 1, 27, 2, 957.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-01', 1, 27, 3, 264.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-01', 1, 28, 2, 1012.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-01', 1, 28, 3, 550.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-01', 1, 3, 1, 320.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-01', 1, 3, 2, 539.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-01', 1, 3, 3, 308.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-01', 1, 30, 1, 715.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-01', 1, 30, 2, 847.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-01', 1, 30, 3, 385.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-01', 3, 5, 1, 832.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-01', 3, 5, 2, 56.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-01', 3, 4, 1, 976.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-01', 3, 4, 2, 544.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-01', 3, 6, 1, 640.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-01', 3, 6, 2, 832.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-01', 2, 10, 1, 756.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-01', 2, 10, 2, 782.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-01', 2, 37, 1, 184.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-01', 2, 13, 1, 459.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-01', 2, 13, 2, 810.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-01', 2, 14, 1, 864.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-01', 2, 14, 2, 528.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-01', 3, 17, 2, 1947.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-01', 2, 17, 1, 468.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-01', 2, 17, 2, 820.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-02', 1, 27, 1, 506.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-02', 1, 27, 2, 330.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-02', 1, 27, 3, 440.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-02', 1, 28, 1, 550.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-02', 1, 28, 2, 1210.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-02', 1, 28, 3, 396.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-02', 1, 3, 1, 572.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-02', 1, 3, 2, 385.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-02', 1, 3, 3, 220.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-02', 1, 30, 1, 506.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-02', 1, 30, 2, 561.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-02', 1, 30, 3, 330.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-02', 3, 5, 1, 420.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-02', 3, 5, 2, 456.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-02', 3, 4, 1, 736.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-02', 3, 4, 2, 416.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-02', 3, 6, 1, 680.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-02', 3, 6, 2, 544.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-02', 3, 35, 2, 384.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-02', 2, 10, 1, 1080.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-02', 2, 10, 2, 828.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-02', 2, 37, 2, 208.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-02', 2, 13, 1, 522.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-02', 2, 13, 2, 871.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-02', 2, 14, 2, 216.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-02', 3, 17, 1, 1906.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-02', 3, 17, 2, 1338.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-02', 2, 17, 1, 1241.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-02', 2, 17, 2, 765.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-03', 1, 27, 1, 572.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-03', 1, 27, 2, 990.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-03', 1, 27, 3, 429.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-03', 1, 28, 1, 594.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-03', 1, 28, 2, 781.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-03', 1, 28, 3, 572.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-03', 1, 3, 1, 583.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-03', 1, 3, 2, 132.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-03', 1, 3, 3, 220.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-03', 1, 30, 1, 451.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-03', 1, 30, 2, 858.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-03', 1, 30, 3, 495.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-03', 3, 5, 1, 280.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-03', 3, 5, 2, 1040.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-03', 3, 4, 1, 1360.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-03', 3, 4, 2, 1248.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-03', 3, 6, 1, 776.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-03', 3, 6, 2, 184.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-03', 3, 35, 2, 120.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-03', 2, 10, 1, 927.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-03', 2, 10, 2, 405.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-03', 2, 37, 1, 464.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-03', 2, 37, 2, 360.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-03', 2, 13, 1, 558.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-03', 2, 13, 2, 1632.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-03', 2, 14, 1, 552.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-03', 2, 14, 2, 435.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-03', 2, 15, 1, 333.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-03', 3, 17, 1, 1770.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-03', 3, 17, 2, 390.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-03', 2, 17, 1, 1827.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-03', 2, 17, 2, 1163.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-04', 1, 27, 1, 407.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-04', 1, 27, 2, 825.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-04', 1, 27, 3, 495.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-04', 1, 28, 1, 572.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-04', 1, 28, 2, 187.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-04', 1, 28, 3, 550.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-04', 1, 3, 1, 495.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-04', 1, 3, 2, 495.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-04', 1, 3, 3, 187.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-04', 1, 30, 1, 561.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-04', 1, 30, 2, 737.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-04', 1, 30, 3, 231.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-04', 3, 5, 2, 208.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-04', 3, 4, 1, 760.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-04', 3, 4, 2, 944.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-04', 3, 6, 1, 656.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-04', 3, 6, 2, 960.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-04', 2, 10, 1, 855.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-04', 2, 10, 2, 1232.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-04', 2, 11, 2, 450.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-04', 2, 37, 1, 256.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-04', 2, 37, 2, 256.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-04', 2, 13, 1, 459.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-04', 2, 13, 2, 180.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-04', 2, 14, 1, 856.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-04', 2, 14, 2, 315.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-04', 2, 15, 2, 72.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-04', 3, 17, 1, 1809.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-04', 3, 17, 2, 1633.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-04', 2, 17, 1, 1572.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-04', 2, 17, 2, 1119.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-05', 1, 27, 1, 506.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-05', 1, 27, 2, 902.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-05', 1, 27, 3, 451.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-05', 1, 28, 1, 639.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-05', 1, 28, 2, 110.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-05', 1, 28, 3, 561.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-05', 1, 3, 1, 330.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-05', 1, 3, 2, 220.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-05', 1, 3, 3, 110.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-05', 1, 30, 1, 473.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-05', 1, 30, 2, 935.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-05', 1, 30, 3, 275.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-05', 3, 5, 1, 420.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-05', 3, 5, 2, 448.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-05', 3, 4, 1, 1280.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-05', 3, 4, 2, 896.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-05', 3, 6, 2, 576.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-05', 2, 10, 1, 1461.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-05', 2, 10, 2, 828.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-05', 2, 11, 2, 387.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-05', 2, 37, 1, 296.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-05', 2, 13, 1, 486.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-05', 2, 13, 2, 225.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-05', 2, 14, 1, 972.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-05', 2, 14, 2, 270.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-05', 2, 15, 2, 567.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-05', 3, 17, 1, 1810.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-05', 3, 17, 2, 875.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-05', 2, 17, 1, 927.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-05', 2, 17, 2, 1280.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-06', 1, 27, 1, 825.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-06', 1, 27, 2, 926.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-06', 1, 27, 3, 352.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-06', 1, 28, 1, 649.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-06', 1, 28, 2, 1133.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-06', 1, 28, 3, 495.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-06', 1, 3, 1, 462.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-06', 1, 3, 2, 55.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-06', 1, 3, 3, 198.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-06', 1, 30, 1, 550.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-06', 1, 30, 2, 253.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-06', 1, 30, 3, 440.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-06', 1, 35, 2, 1000.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-06', 1, 35, 3, 320.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-06', 3, 5, 1, 600.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-06', 3, 5, 2, 248.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-06', 3, 4, 1, 1120.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-06', 3, 4, 2, 584.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-06', 3, 6, 1, 888.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-06', 3, 6, 2, 992.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-06', 2, 10, 1, 855.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-06', 2, 10, 2, 738.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-06', 2, 37, 2, 416.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-06', 2, 13, 1, 1215.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-06', 2, 13, 2, 198.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-06', 2, 14, 1, 234.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-06', 2, 14, 2, 168.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-06', 2, 15, 2, 180.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-06', 3, 17, 1, 1504.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-06', 3, 17, 2, 675.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-06', 2, 17, 1, 1479.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-06', 2, 17, 2, 1710.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-07', 1, 27, 1, 165.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-07', 1, 27, 2, 809.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-07', 1, 27, 3, 256.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-07', 1, 28, 1, 616.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-07', 1, 28, 2, 506.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-07', 1, 28, 3, 660.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-07', 1, 3, 1, 220.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-07', 1, 3, 2, 330.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-07', 1, 30, 1, 462.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-07', 1, 30, 2, 341.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-07', 1, 35, 1, 296.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-07', 1, 35, 2, 295.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-07', 1, 35, 3, 120.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-07', 3, 4, 1, 432.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-07', 3, 4, 2, 440.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-07', 3, 6, 1, 544.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-07', 3, 6, 2, 488.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-07', 2, 37, 1, 1151.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-07', 2, 37, 2, 280.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-07', 2, 13, 1, 399.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-07', 2, 13, 2, 539.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-07', 2, 14, 1, 688.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-07', 2, 14, 2, 520.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-07', 2, 15, 1, 144.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-07', 2, 15, 2, 162.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-07', 3, 17, 1, 344.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-07', 3, 17, 2, 645.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-07', 2, 17, 1, 1371.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-07', 2, 17, 2, 690.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-08', 1, 27, 1, 216.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-08', 1, 27, 3, 462.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-08', 1, 28, 1, 660.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-08', 1, 28, 2, 990.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-08', 1, 28, 3, 583.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-08', 1, 3, 2, 220.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-08', 1, 3, 3, 220.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-08', 1, 30, 2, 165.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-08', 1, 30, 3, 506.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-08', 1, 35, 1, 246.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-08', 1, 35, 2, 792.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-08', 1, 35, 3, 480.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-08', 3, 5, 1, 136.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-08', 3, 5, 2, 64.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-08', 3, 4, 1, 1320.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-08', 3, 4, 2, 1040.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-08', 3, 6, 2, 696.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-08', 2, 10, 1, 882.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-08', 2, 10, 2, 648.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-08', 2, 37, 1, 888.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-08', 2, 37, 2, 480.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-08', 2, 13, 1, 324.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-08', 2, 13, 2, 522.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-08', 2, 14, 2, 144.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-08', 2, 15, 1, 99.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-08', 3, 17, 1, 2254.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-08', 3, 17, 2, 1838.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-08', 2, 17, 1, 1722.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-08', 2, 17, 2, 864.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-09', 1, 27, 1, 528.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-09', 1, 27, 2, 1080.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-09', 1, 27, 3, 144.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-09', 1, 28, 1, 627.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-09', 1, 28, 2, 1020.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-09', 1, 28, 3, 588.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-09', 1, 3, 1, 726.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-09', 1, 30, 1, 594.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-09', 1, 30, 2, 840.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-09', 1, 30, 3, 192.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-09', 1, 35, 1, 627.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-09', 1, 35, 2, 816.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-09', 1, 35, 3, 518.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-09', 3, 5, 2, 360.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-09', 3, 4, 1, 1064.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-09', 3, 4, 2, 824.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-09', 3, 6, 2, 360.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-09', 2, 10, 2, 342.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-09', 2, 11, 1, 344.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-09', 2, 11, 2, 632.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-09', 2, 37, 1, 1251.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-09', 2, 37, 2, 126.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-09', 2, 13, 1, 162.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-09', 2, 13, 2, 792.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-09', 2, 14, 1, 368.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-09', 2, 14, 2, 416.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-09', 2, 15, 1, 342.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-09', 3, 17, 1, 1450.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-09', 3, 17, 2, 784.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-09', 2, 17, 1, 1847.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-09', 2, 17, 2, 1245.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-10', 1, 27, 1, 600.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-10', 1, 27, 2, 184.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-10', 1, 27, 3, 344.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-10', 1, 28, 1, 624.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-10', 1, 28, 2, 440.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-10', 1, 30, 1, 612.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-10', 1, 30, 2, 660.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-10', 1, 30, 3, 504.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-10', 1, 35, 1, 532.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-10', 1, 35, 2, 488.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-10', 1, 35, 3, 630.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-10', 3, 5, 1, 960.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-10', 3, 5, 2, 904.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-10', 3, 4, 1, 1440.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-10', 3, 6, 1, 416.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-10', 3, 6, 2, 520.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-10', 2, 10, 1, 738.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-10', 2, 10, 2, 972.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-10', 2, 11, 2, 64.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-10', 2, 37, 1, 252.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-10', 2, 37, 2, 704.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-10', 2, 13, 1, 544.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-10', 2, 13, 2, 675.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-10', 2, 14, 1, 864.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-10', 2, 14, 2, 520.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-10', 2, 15, 1, 530.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-10', 3, 17, 1, 840.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-10', 3, 17, 2, 1611.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-10', 2, 17, 1, 1440.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-10', 2, 17, 2, 1215.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-11', 1, 27, 1, 312.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-11', 1, 27, 2, 552.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-11', 1, 27, 3, 144.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-11', 1, 28, 1, 720.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-11', 1, 28, 2, 912.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-11', 1, 28, 3, 561.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-11', 1, 3, 2, 84.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-11', 1, 3, 3, 385.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-11', 1, 30, 1, 264.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-11', 1, 30, 2, 900.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-11', 1, 30, 3, 462.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-11', 1, 35, 1, 360.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-11', 1, 35, 3, 185.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-11', 3, 4, 1, 1320.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-11', 3, 4, 2, 936.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-11', 3, 6, 2, 1128.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-11', 2, 10, 1, 972.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-11', 2, 10, 2, 964.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-11', 2, 37, 2, 608.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-11', 2, 13, 1, 384.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-11', 2, 13, 2, 630.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-11', 2, 14, 1, 944.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-11', 2, 14, 2, 736.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-11', 2, 15, 1, 1044.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-11', 2, 15, 2, 45.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-11', 3, 17, 1, 3045.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-11', 3, 17, 2, 771.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-11', 2, 17, 1, 1489.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-11', 2, 17, 2, 720.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-12', 1, 27, 1, 224.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-12', 1, 27, 2, 728.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-12', 1, 27, 3, 136.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-12', 1, 28, 1, 539.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-12', 1, 28, 2, 240.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-12', 1, 3, 1, 550.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-12', 1, 3, 2, 492.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-12', 1, 3, 3, 180.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-12', 1, 30, 1, 550.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-12', 1, 30, 2, 252.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-12', 1, 37, 2, 176.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-12', 1, 37, 3, 144.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-12', 1, 35, 1, 295.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-12', 1, 35, 2, 312.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-12', 3, 5, 2, 120.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-12', 3, 4, 1, 1456.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-12', 3, 4, 2, 992.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-12', 3, 6, 1, 1264.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-12', 2, 10, 1, 981.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-12', 2, 10, 2, 1216.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-12', 2, 37, 1, 464.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-12', 2, 13, 1, 1044.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-12', 2, 13, 2, 40.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-12', 2, 14, 1, 848.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-12', 2, 14, 2, 576.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-12', 2, 15, 1, 416.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-12', 2, 15, 2, 120.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-12', 3, 17, 1, 1080.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-12', 3, 17, 2, 1018.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-12', 2, 17, 1, 256.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-12', 2, 17, 2, 1360.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-13', 1, 27, 1, 228.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-13', 1, 27, 2, 216.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-13', 1, 28, 1, 468.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-13', 1, 28, 3, 480.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-13', 1, 3, 1, 480.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-13', 1, 3, 2, 418.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-13', 1, 3, 3, 360.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-13', 1, 30, 3, 216.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-13', 1, 37, 2, 758.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-13', 1, 37, 3, 264.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-13', 3, 5, 1, 168.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-13', 3, 5, 2, 136.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-13', 3, 4, 1, 600.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-13', 3, 6, 1, 320.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-13', 3, 6, 2, 200.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-13', 2, 10, 1, 468.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-13', 2, 10, 2, 108.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-13', 2, 13, 1, 408.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-13', 2, 14, 1, 684.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-13', 2, 15, 1, 224.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-13', 3, 17, 1, 530.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-13', 3, 17, 2, 475.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-13', 2, 17, 1, 1191.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-13', 2, 17, 2, 1460.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-14', 1, 27, 3, 248.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-14', 1, 28, 1, 300.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-14', 1, 28, 2, 432.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-14', 1, 28, 3, 593.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-14', 1, 3, 2, 180.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-14', 1, 3, 3, 165.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-14', 1, 30, 1, 276.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-14', 1, 30, 2, 984.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-14', 1, 37, 1, 304.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-14', 1, 37, 2, 384.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-14', 1, 37, 3, 336.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-14', 3, 5, 1, 336.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-14', 3, 5, 2, 552.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-14', 3, 4, 1, 1168.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-14', 3, 4, 2, 160.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-14', 2, 10, 1, 774.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-14', 2, 10, 2, 918.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-14', 2, 11, 1, 704.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-14', 2, 13, 1, 392.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-14', 2, 13, 2, 400.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-14', 2, 14, 1, 963.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-14', 2, 14, 2, 774.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-14', 2, 15, 2, 520.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-14', 3, 17, 1, 655.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-14', 3, 17, 2, 1407.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-14', 2, 17, 1, 408.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-14', 2, 17, 2, 1960.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-15', 1, 27, 1, 408.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-15', 1, 27, 2, 840.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-15', 1, 27, 3, 544.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-15', 1, 28, 1, 627.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-15', 1, 28, 3, 672.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-15', 1, 3, 1, 275.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-15', 1, 3, 2, 420.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-15', 1, 3, 3, 240.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-15', 1, 30, 2, 264.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-15', 1, 30, 3, 552.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-15', 1, 37, 1, 456.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-15', 1, 37, 2, 282.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-15', 3, 5, 1, 440.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-15', 3, 4, 1, 1600.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-15', 3, 4, 2, 904.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-15', 3, 6, 2, 1104.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-15', 2, 10, 1, 1356.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-15', 2, 10, 2, 648.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-15', 2, 11, 1, 256.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-15', 2, 13, 2, 408.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-15', 2, 14, 1, 954.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-15', 2, 14, 2, 630.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-15', 2, 15, 2, 280.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-15', 3, 17, 1, 2060.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-15', 3, 17, 2, 1038.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-15', 2, 17, 1, 1073.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-15', 2, 17, 2, 1930.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-16', 1, 27, 1, 336.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-16', 1, 27, 2, 424.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-16', 1, 27, 3, 258.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-16', 1, 28, 1, 144.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-16', 1, 28, 2, 220.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-16', 1, 28, 3, 660.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-16', 1, 3, 1, 480.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-16', 1, 3, 2, 308.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-16', 1, 3, 3, 408.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-16', 1, 30, 1, 684.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-16', 1, 30, 2, 1012.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-16', 1, 30, 3, 636.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-16', 1, 35, 1, 104.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-16', 1, 35, 2, 256.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-16', 3, 5, 1, 1160.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-16', 3, 5, 2, 416.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-16', 3, 4, 1, 1240.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-16', 3, 6, 1, 1120.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-16', 3, 6, 2, 1904.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-16', 2, 10, 1, 1242.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-16', 2, 10, 2, 612.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-16', 2, 13, 1, 984.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-16', 2, 13, 2, 824.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-16', 2, 14, 1, 558.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-16', 2, 14, 2, 1053.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-16', 2, 15, 1, 624.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-16', 2, 15, 2, 192.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-16', 3, 17, 1, 811.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-16', 3, 17, 2, 1519.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-16', 2, 17, 1, 1277.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-16', 2, 17, 2, 420.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-17', 1, 27, 1, 288.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-17', 1, 27, 2, 408.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-17', 1, 27, 3, 144.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-17', 1, 28, 1, 684.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-17', 1, 28, 2, 552.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-17', 1, 28, 3, 539.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-17', 1, 3, 1, 424.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-17', 1, 3, 2, 384.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-17', 1, 3, 3, 352.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-17', 1, 30, 1, 696.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-17', 1, 30, 2, 840.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-17', 1, 30, 3, 550.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-17', 1, 35, 3, 205.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-17', 3, 5, 1, 1200.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-17', 3, 5, 2, 888.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-17', 3, 4, 1, 928.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-17', 3, 4, 2, 1304.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-17', 3, 6, 1, 1248.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-17', 3, 6, 2, 384.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-17', 2, 10, 1, 612.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-17', 2, 10, 2, 315.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-17', 2, 11, 2, 504.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-17', 2, 13, 1, 824.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-17', 2, 13, 2, 544.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-17', 2, 14, 1, 1053.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-17', 2, 14, 2, 882.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-17', 2, 15, 1, 192.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-17', 3, 17, 1, 96.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-17', 3, 17, 2, 1560.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-17', 2, 17, 1, 645.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-17', 2, 17, 2, 621.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-18', 1, 28, 1, 671.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-18', 1, 28, 2, 1320.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-18', 1, 28, 3, 576.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-18', 1, 3, 1, 110.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-18', 1, 3, 2, 600.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-18', 1, 3, 3, 360.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-18', 1, 30, 1, 605.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-18', 1, 30, 2, 708.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-18', 1, 30, 3, 516.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-18', 1, 35, 2, 378.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-18', 1, 35, 3, 224.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-18', 3, 5, 1, 736.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-18', 3, 5, 2, 824.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-18', 3, 4, 1, 1000.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-18', 3, 4, 2, 1032.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-18', 3, 6, 1, 488.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-18', 3, 6, 2, 80.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-18', 2, 10, 1, 837.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-18', 2, 10, 2, 882.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-18', 2, 13, 1, 720.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-18', 2, 13, 2, 528.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-18', 2, 14, 1, 1044.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-18', 2, 14, 2, 945.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-18', 2, 15, 2, 248.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-18', 3, 17, 1, 688.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-18', 3, 17, 2, 825.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-18', 2, 17, 1, 1200.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-18', 2, 17, 2, 1093.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-19', 1, 27, 1, 352.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-19', 1, 27, 2, 536.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-19', 1, 27, 3, 344.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-19', 1, 28, 1, 744.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-19', 1, 28, 2, 1320.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-19', 1, 28, 3, 624.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-19', 1, 3, 1, 372.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-19', 1, 3, 2, 300.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-19', 1, 3, 3, 450.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-19', 1, 30, 1, 684.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-19', 1, 30, 2, 1032.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-19', 1, 30, 3, 624.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-19', 1, 35, 2, 175.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-19', 1, 35, 3, 132.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-19', 3, 5, 1, 1168.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-19', 3, 5, 2, 1280.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-19', 3, 4, 1, 640.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-19', 3, 4, 2, 1256.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-19', 3, 6, 1, 416.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-19', 2, 10, 1, 774.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-19', 2, 10, 2, 189.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-19', 2, 11, 1, 96.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-19', 2, 11, 2, 544.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-19', 2, 13, 1, 944.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-19', 2, 13, 2, 264.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-19', 2, 14, 1, 1098.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-19', 2, 14, 2, 783.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-19', 2, 15, 2, 368.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-19', 2, 17, 1, 696.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-19', 2, 17, 2, 1099.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-20', 1, 27, 1, 320.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-20', 1, 27, 2, 592.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-20', 1, 27, 3, 432.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-20', 1, 28, 1, 720.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-20', 1, 28, 2, 1032.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-20', 1, 28, 3, 636.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-20', 1, 3, 1, 348.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-20', 1, 3, 2, 240.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-20', 1, 3, 3, 192.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-20', 1, 30, 1, 696.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-20', 1, 30, 2, 1020.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-20', 1, 30, 3, 492.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-20', 1, 35, 1, 132.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-20', 1, 35, 2, 285.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-20', 3, 5, 1, 1440.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-20', 3, 5, 2, 1024.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-20', 3, 4, 1, 1120.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-20', 3, 4, 2, 856.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-20', 3, 6, 1, 984.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-20', 3, 6, 2, 1040.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-20', 2, 10, 1, 1008.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-20', 2, 10, 2, 1080.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-20', 2, 11, 2, 744.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-20', 2, 13, 1, 1280.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-20', 2, 13, 2, 760.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-20', 2, 14, 1, 1134.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-20', 2, 14, 2, 963.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-20', 2, 17, 1, 440.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-20', 2, 17, 2, 1054.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-21', 1, 27, 1, 216.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-21', 1, 27, 2, 680.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-21', 1, 27, 3, 144.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-21', 1, 28, 1, 684.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-21', 1, 28, 2, 1428.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-21', 1, 28, 3, 588.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-21', 1, 3, 1, 384.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-21', 1, 3, 2, 372.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-21', 1, 30, 1, 696.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-21', 1, 30, 2, 936.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-21', 1, 30, 3, 528.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-21', 3, 5, 1, 1480.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-21', 3, 5, 2, 968.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-21', 3, 4, 1, 1360.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-21', 3, 4, 2, 1088.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-21', 3, 6, 1, 1200.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-21', 3, 6, 2, 1136.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-21', 2, 10, 1, 1134.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-21', 2, 10, 2, 1017.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-21', 2, 11, 2, 712.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-21', 2, 13, 1, 1504.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-21', 2, 13, 2, 528.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-21', 2, 14, 1, 1116.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-21', 2, 14, 2, 873.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-21', 2, 17, 1, 498.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-21', 2, 17, 2, 1301.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-22', 1, 27, 1, 1024.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-22', 1, 27, 2, 972.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-22', 1, 27, 3, 420.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-22', 1, 28, 1, 732.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-22', 1, 28, 2, 1260.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-22', 1, 28, 3, 480.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-22', 1, 3, 1, 444.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-22', 1, 3, 2, 276.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-22', 1, 3, 3, 360.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-22', 1, 30, 1, 684.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-22', 1, 30, 2, 1132.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-22', 1, 30, 3, 396.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-22', 3, 5, 1, 1376.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-22', 3, 5, 2, 1136.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-22', 3, 4, 1, 1464.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-22', 3, 4, 2, 1560.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-22', 3, 6, 1, 1200.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-22', 3, 6, 2, 1048.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-22', 2, 10, 1, 954.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-22', 2, 10, 2, 918.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-22', 2, 13, 1, 1696.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-22', 2, 13, 2, 158.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-22', 2, 14, 1, 54.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-22', 2, 14, 2, 801.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-22', 2, 15, 1, 64.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-22', 2, 15, 2, 704.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-22', 2, 17, 1, 352.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-22', 2, 17, 2, 450.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-23', 1, 27, 1, 432.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-23', 1, 27, 2, 792.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-23', 1, 27, 3, 352.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-23', 1, 28, 1, 627.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-23', 1, 28, 3, 312.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-23', 1, 3, 1, 480.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-23', 1, 3, 3, 132.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-23', 1, 30, 1, 744.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-23', 1, 30, 2, 348.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-23', 1, 30, 3, 336.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-23', 3, 5, 1, 1440.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-23', 3, 5, 2, 160.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-23', 3, 4, 1, 1544.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-23', 3, 4, 2, 400.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-23', 3, 6, 1, 1296.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-23', 3, 6, 2, 184.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-23', 2, 10, 1, 856.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-23', 2, 10, 2, 963.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-23', 2, 11, 1, 72.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-23', 2, 11, 2, 656.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-23', 2, 13, 1, 1264.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-23', 2, 13, 2, 304.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-23', 2, 14, 1, 688.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-23', 2, 14, 2, 801.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-23', 2, 15, 2, 304.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-23', 2, 17, 1, 624.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-23', 2, 17, 2, 392.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-24', 1, 27, 1, 565.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-24', 1, 27, 2, 896.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-24', 1, 27, 3, 520.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-24', 1, 28, 1, 624.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-24', 1, 28, 2, 192.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-24', 1, 28, 3, 384.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-24', 1, 3, 2, 60.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-24', 1, 3, 3, 300.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-24', 1, 30, 1, 588.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-24', 1, 30, 2, 864.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-24', 1, 30, 3, 506.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-24', 3, 5, 1, 1168.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-24', 3, 5, 2, 1024.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-24', 3, 4, 1, 968.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-24', 3, 4, 2, 1480.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-24', 3, 6, 1, 728.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-24', 3, 6, 2, 1120.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-24', 2, 10, 1, 400.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-24', 2, 10, 2, 882.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-24', 2, 11, 1, 688.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-24', 2, 11, 2, 344.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-24', 2, 13, 1, 1080.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-24', 2, 13, 2, 288.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-24', 2, 14, 2, 1152.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-24', 2, 15, 2, 304.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-24', 2, 17, 1, 304.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-24', 2, 17, 2, 2511.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-25', 1, 27, 1, 808.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-25', 1, 27, 2, 1439.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-25', 1, 27, 3, 408.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-25', 1, 28, 1, 480.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-25', 1, 28, 2, 912.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-25', 1, 28, 3, 540.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-25', 1, 3, 1, 540.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-25', 1, 3, 2, 660.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-25', 1, 3, 3, 396.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-25', 1, 30, 1, 684.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-25', 1, 30, 2, 1212.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-25', 1, 30, 3, 696.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-25', 1, 35, 2, 270.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-25', 3, 5, 1, 1248.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-25', 3, 5, 2, 952.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-25', 3, 4, 1, 1368.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-25', 3, 4, 2, 1096.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-25', 3, 6, 1, 1264.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-25', 3, 6, 2, 1128.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-25', 2, 10, 1, 720.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-25', 2, 10, 2, 1035.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-25', 2, 11, 1, 968.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-25', 2, 11, 2, 264.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-25', 2, 13, 1, 976.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-25', 2, 13, 2, 1112.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-25', 2, 14, 1, 531.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-25', 2, 14, 2, 972.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-25', 2, 15, 2, 368.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-25', 2, 17, 1, 400.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-25', 2, 17, 2, 320.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-26', 1, 27, 1, 536.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-26', 1, 27, 2, 1705.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-26', 1, 27, 3, 1068.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-26', 1, 28, 1, 900.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-26', 1, 28, 2, 972.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-26', 1, 28, 3, 576.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-26', 1, 3, 1, 360.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-26', 1, 3, 2, 780.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-26', 1, 3, 3, 360.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-26', 1, 30, 1, 660.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-26', 1, 30, 2, 1092.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-26', 1, 30, 3, 624.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-26', 3, 5, 1, 1128.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-26', 3, 5, 2, 992.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-26', 3, 4, 1, 1280.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-26', 3, 4, 2, 1312.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-26', 3, 6, 1, 1072.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-26', 3, 6, 2, 720.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-26', 2, 10, 1, 738.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-26', 2, 10, 2, 882.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-26', 2, 11, 1, 1088.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-26', 2, 11, 2, 672.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-26', 2, 13, 1, 1160.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-26', 2, 13, 2, 920.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-26', 2, 14, 1, 801.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-26', 2, 14, 2, 873.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-26', 2, 15, 2, 384.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-26', 2, 17, 1, 500.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-26', 2, 17, 2, 1586.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-27', 1, 27, 1, 1560.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-27', 1, 27, 2, 840.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-27', 1, 27, 3, 1072.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-27', 1, 28, 1, 864.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-27', 1, 28, 2, 840.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-27', 1, 28, 3, 672.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-27', 1, 3, 1, 720.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-27', 1, 3, 2, 246.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-27', 1, 30, 1, 576.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-27', 1, 30, 2, 942.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-27', 1, 30, 3, 432.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-27', 3, 5, 1, 1168.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-27', 3, 5, 2, 702.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-27', 3, 4, 1, 1320.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-27', 3, 6, 1, 1320.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-27', 3, 6, 2, 976.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-27', 2, 10, 1, 738.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-27', 2, 10, 2, 702.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-27', 2, 11, 1, 240.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-27', 2, 11, 2, 422.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-27', 2, 13, 1, 1080.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-27', 2, 13, 2, 1040.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-27', 2, 14, 1, 1053.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-27', 2, 14, 2, 855.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-27', 2, 15, 1, 112.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-27', 2, 15, 2, 712.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-27', 2, 17, 1, 734.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-27', 2, 17, 2, 855.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-28', 1, 27, 1, 228.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-28', 1, 28, 1, 336.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-28', 1, 30, 1, 216.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-28', 3, 5, 1, 528.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-28', 3, 4, 1, 1400.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-28', 3, 6, 1, 600.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-28', 2, 10, 1, 378.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-28', 2, 13, 1, 656.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-28', 2, 14, 1, 468.0);
INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('2025-01-28', 2, 17, 1, 64.0);
