INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0#-26-9', 2) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0#-26-5', 2) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0#-26-1', 2) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0#-20-3', 2) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0#-16-1', 2) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0#-20-1', 2) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2#-20-1', 2) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2#-4-1', 2) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0#-11-1/2', 2) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2#-11-2', 2) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0#-5-6', 2) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0#-5-10', 2) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2#-5-1', 2) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0#-7-1', 2) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0#-15-3', 2) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0#-15-7', 2) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('掘进及其它出矿', 3) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2#-5-5', 2) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2#-5-9', 2) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0#-11-1', 2) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('0#-11-1', 2) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
INSERT INTO base_stope (stope_name, working_face_id) VALUES ('2#-20-5', 2) ON CONFLICT (stope_name, working_face_id) DO NOTHING;
