-- 出矿数据表
CREATE TABLE IF NOT EXISTS data_mucking_out (
    id bigserial primary key,
    operation_date date NOT NULL,   -- 作业日期
    project_department_id bigint NOT NULL,   -- 项目部门ID
    stope_id bigint NOT NULL,   -- 采场ID (now referencing base_stope)
    working_period_id bigint NOT NULL,   -- 作业时段ID
    tons numeric(10,2),   -- 出矿吨数
    create_by varchar(64),
    create_time timestamp(6) DEFAULT now(),
    update_by varchar(64),
    update_time timestamp(6) DEFAULT now(),
    FOREIGN KEY (project_department_id) REFERENCES base_project_department(project_department_id),
    FOREIGN KEY (stope_id) REFERENCES base_stope(stope_id),
    FOREIGN KEY (working_period_id) REFERENCES base_working_period(working_period_id)
);

COMMENT ON TABLE data_mucking_out IS '出矿数据表';
COMMENT ON COLUMN data_mucking_out.id IS '出矿数据ID';
COMMENT ON COLUMN data_mucking_out.operation_date IS '作业日期';
COMMENT ON COLUMN data_mucking_out.project_department_id IS '项目部门ID';
COMMENT ON COLUMN data_mucking_out.stope_id IS '采场ID';
COMMENT ON COLUMN data_mucking_out.working_period_id IS '作业时段ID';
COMMENT ON COLUMN data_mucking_out.tons IS '出矿吨数';
COMMENT ON COLUMN data_mucking_out.create_by IS '创建人';
COMMENT ON COLUMN data_mucking_out.create_time IS '创建时间';
COMMENT ON COLUMN data_mucking_out.update_by IS '更新人';
COMMENT ON COLUMN data_mucking_out.update_time IS '更新时间';

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_data_mucking_out_date ON data_mucking_out (operation_date);
CREATE INDEX IF NOT EXISTS idx_data_mucking_out_department ON data_mucking_out (project_department_id);
CREATE INDEX IF NOT EXISTS idx_data_mucking_out_field ON data_mucking_out (stope_id);
CREATE INDEX IF NOT EXISTS idx_data_mucking_out_period ON data_mucking_out (working_period_id);

-- 创建财务月份计算函数
CREATE OR REPLACE FUNCTION get_financial_month(IN d date, OUT financial_year integer, OUT financial_month integer)
AS $$
BEGIN
    IF EXTRACT(DAY FROM d) >= 29 THEN
        financial_month := EXTRACT(MONTH FROM d) + 1;
        IF financial_month = 13 THEN
            financial_month := 1;
            financial_year := EXTRACT(YEAR FROM d) + 1;
        ELSE
            financial_year := EXTRACT(YEAR FROM d);
        END IF;
    ELSE
        financial_year := EXTRACT(YEAR FROM d);
        financial_month := EXTRACT(MONTH FROM d);
    END IF;
END;
$$ LANGUAGE plpgsql;

-- 创建财务年份计算函数
CREATE OR REPLACE FUNCTION get_financial_year(IN d date, OUT financial_year integer)
AS $$
BEGIN
    -- 如果是12月29日及以后，则将其分配到下一年
    IF EXTRACT(MONTH FROM d) = 12 AND EXTRACT(DAY FROM d) >= 29 THEN
        financial_year := EXTRACT(YEAR FROM d) + 1;
    ELSE
        financial_year := EXTRACT(YEAR FROM d);
    END IF;
END;
$$ LANGUAGE plpgsql;

-- 创建周计算函数（周四到周三为一周）
CREATE OR REPLACE FUNCTION get_week_thu_to_wed(IN d date, OUT week_year integer, OUT week_number integer, OUT week_start_date date, OUT week_end_date date)
AS $$
DECLARE
    day_of_week integer;
    days_since_thursday integer;
BEGIN
    -- 获取星期几 (0=Sunday, 1=Monday, ..., 6=Saturday)
    day_of_week := EXTRACT(DOW FROM d);
    
    -- 计算距离本周周四的天数
    -- 周四是4，如果当前是周四到周三(4,5,6,0,1,2,3)，则计算到本周周四的距离
    IF day_of_week >= 4 THEN
        -- 周四、周五、周六：距离本周周四的天数
        days_since_thursday := day_of_week - 4;
    ELSE
        -- 周日、周一、周二、周三：距离上周周四的天数
        days_since_thursday := day_of_week + 3;
    END IF;
    
    -- 计算本周的周四日期（周的开始日期）
    week_start_date := d - days_since_thursday;
    
    -- 计算本周的周三日期（周的结束日期）
    week_end_date := week_start_date + 6;
    
    -- 计算周年份（以周四所在年份为准）
    week_year := EXTRACT(YEAR FROM week_start_date);
    
    -- 计算周数（该年第几周，从1开始）
    -- 找到该年第一个周四
    DECLARE
        first_thursday date;
        jan_1_dow integer;
    BEGIN
        -- 获取1月1日是星期几
        jan_1_dow := EXTRACT(DOW FROM (week_year || '-01-01')::date);
        
        -- 计算第一个周四的日期
        IF jan_1_dow <= 4 THEN
            -- 1月1日是周日到周四，第一个周四在同一周
            first_thursday := (week_year || '-01-01')::date + (4 - jan_1_dow);
        ELSE
            -- 1月1日是周五或周六，第一个周四在下一周
            first_thursday := (week_year || '-01-01')::date + (11 - jan_1_dow);
        END IF;
        
        -- 计算周数
        week_number := ((week_start_date - first_thursday) / 7)::integer + 1;
    END;
END;
$$ LANGUAGE plpgsql;
