python "c:\Users\<USER>\doc\5\extract_excel.py" "c:\Users\<USER>\doc\5\统计\2025年1月生产及建设统计表.xlsx" --start-row 154 --end-row 177 --start-col A --end-col H --output "C:\Users\<USER>\doc\5\chukuang\extracted_data1.xlsx"

python c:\Users\<USER>\doc\5\chukuang\generate_sql_inserts.py c:\Users\<USER>\doc\5\chukuang\extracted_data1.csv -o c:\Users\<USER>\doc\5\chukuang\dml1.sql

python "c:\Users\<USER>\doc\5\extract_excel.py" "c:\Users\<USER>\doc\5\统计\2025年2月生产及建设统计表.xlsx" --start-row 160 --end-row 185 --start-col A --end-col H --output "C:\Users\<USER>\doc\5\chukuang\extracted_data2.xlsx"

python c:\Users\<USER>\doc\5\chukuang\generate_sql_inserts.py c:\Users\<USER>\doc\5\chukuang\extracted_data2.csv -o c:\Users\<USER>\doc\5\chukuang\dml2.sql

python "c:\Users\<USER>\doc\5\extract_excel.py" "c:\Users\<USER>\doc\5\统计\2025年3月份生产及建设统计表.xlsx" --start-row 180 --end-row 205 --start-col A --end-col H --output "C:\Users\<USER>\doc\5\chukuang\extracted_data3.xlsx"

python c:\Users\<USER>\doc\5\chukuang\generate_sql_inserts.py c:\Users\<USER>\doc\5\chukuang\extracted_data3.csv -o c:\Users\<USER>\doc\5\chukuang\dml3.sql

python "c:\Users\<USER>\doc\5\extract_excel.py" "c:\Users\<USER>\doc\5\统计\2025年4月生产及建设统计表.xlsx" --start-row 180 --end-row 205 --start-col A --end-col H --output "C:\Users\<USER>\doc\5\chukuang\extracted_data4.xlsx"

python c:\Users\<USER>\doc\5\chukuang\generate_sql_inserts.py c:\Users\<USER>\doc\5\chukuang\extracted_data4.csv -o c:\Users\<USER>\doc\5\chukuang\dml4.sql

python "c:\Users\<USER>\doc\5\extract_excel.py" "c:\Users\<USER>\doc\5\统计\2025年5月份生产及建设统计表.xlsx" --start-row 191 --end-row 216 --start-col A --end-col H --output "C:\Users\<USER>\doc\5\chukuang\extracted_data5.xlsx"

python c:\Users\<USER>\doc\5\chukuang\generate_sql_inserts.py c:\Users\<USER>\doc\5\chukuang\extracted_data5.csv -o c:\Users\<USER>\doc\5\chukuang\dml5.sql

python "c:\Users\<USER>\doc\5\extract_excel.py" "c:\Users\<USER>\doc\5\统计\2025年6月份生产及建设统计表.xlsx" --start-row 180 --end-row 205 --start-col A --end-col H --output "C:\Users\<USER>\doc\5\chukuang\extracted_data6.xlsx"

python c:\Users\<USER>\doc\5\chukuang\generate_sql_inserts.py c:\Users\<USER>\doc\5\chukuang\extracted_data6.csv -o c:\Users\<USER>\doc\5\chukuang\dml6.sql

python "c:\Users\<USER>\doc\5\extract_excel.py" "c:\Users\<USER>\doc\5\统计\2025年7月份生产及建设统计表.xlsx" --start-row 185 --end-row 212 --start-col A --end-col H --output "C:\Users\<USER>\doc\5\chukuang\extracted_data7.xlsx"

python c:\Users\<USER>\doc\5\chukuang\generate_sql_inserts.py c:\Users\<USER>\doc\5\chukuang\extracted_data7.csv -o c:\Users\<USER>\doc\5\chukuang\dml7.sql


python c:\Users\<USER>\doc\5\chukuang\collect_stope_names.py


-1020m(\d)-(\d+-\d+(/\d+)?)采场

$1#-$2