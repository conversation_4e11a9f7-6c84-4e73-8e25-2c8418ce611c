#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
生成SQL插入语句脚本
从CSV文件生成PostgreSQL数据库的INSERT语句 - 出矿数据
自动从数据库查询采场ID
"""

import pandas as pd
import re
from datetime import datetime
import os
import argparse
from decimal import Decimal, getcontext
import psycopg2
from psycopg2 import sql

# 设置decimal精度
getcontext().prec = 10  # 设置精度为10位

# 定义工作时段ID映射
WORKING_PERIOD_MAP = {
    '0-8': 1,
    '8-20': 2,
    '20-0': 3
}

def parse_date(date_str):
    """解析中文日期格式，转换为标准日期格式"""
    if not isinstance(date_str, str):
        return None
    
    try:
        # 提取月和日
        month_match = re.search(r'(\d+)月', date_str)
        day_match = re.search(r'(\d+)日', date_str)
        
        if month_match and day_match:
            month = int(month_match.group(1))
            day = int(day_match.group(1))
            # 假设年份是2025年
            return f"2025-{month:02d}-{day:02d}"
    except Exception as e:
        print(f"解析日期出错: {str(e)}")
    return None

def safe_value(value, default=0, as_decimal=True):
    """安全获取值，处理NaN和None，并确保返回数字类型"""
    if pd.isna(value) or value is None:
        return Decimal(default) if as_decimal else default
    
    try:
        if isinstance(value, str):
            value = value.replace(',', '').strip()
            if value == '':
                return Decimal(default) if as_decimal else default
            # 先尝试转换为浮点数，以处理科学计数法等格式
            num = float(value)
            if as_decimal:
                return Decimal(str(num))
            return num
        elif isinstance(value, (int, float)):
            return Decimal(str(value)) if as_decimal else value
        else:
            num = float(value)
            if as_decimal:
                return Decimal(str(num))
            return num
    except (ValueError, TypeError):
        print(f"警告: 无法转换值 '{value}'，使用默认值 {default}")
        return Decimal(default) if as_decimal else default

def connect_to_database(host='**********', port=5432, dbname='lxbi', user='postgres', password='admin321.'):
    """连接到PostgreSQL数据库"""
    try:
        conn = psycopg2.connect(
            host=host,
            port=port,
            dbname=dbname,
            user=user,
            password=password
        )
        print(f"成功连接到数据库: {dbname}@{host}:{port}")
        return conn
    except Exception as e:
        print(f"连接数据库失败: {e}")
        return None

def get_stope_id(conn, stope_name):
    """根据采场名称查询采场ID"""
    if not conn:
        print("数据库连接不可用，无法查询采场ID")
        return None
        
    try:
        with conn.cursor() as cursor:
            # 使用精确匹配查询采场ID
            cursor.execute(
                "SELECT stope_id FROM base_stope WHERE stope_name = %s",
                [stope_name]
            )
            result = cursor.fetchone()
            
            if result:
                stope_id = result[0]
                print(f"找到采场ID: {stope_id} 对应名称: {stope_name}")
                return stope_id
            else:
                print(f"警告: 未找到采场名称 '{stope_name}' 对应的ID")
                return None
    except Exception as e:
        print(f"查询采场ID时出错: {e}")
        return None

def map_department_name(department_name):
    """映射部门名称到数据库中的名称"""
    if not department_name:
        return None
        
    # 部门名称映射表
    department_mapping = {
        "涟邵项目部": "涟邵建工",
        "中矿项目部": "中矿建设",
        "采运作业区": "自营"
    }
    
    # 检查是否需要映射
    for key, value in department_mapping.items():
        if key in department_name:
            print(f"映射部门名称: '{department_name}' -> '{value}'")
            return value
    
    # 如果没有匹配项，返回原始名称
    return department_name

def get_project_department_id(conn, department_name):
    """从base_project_department表中查询项目部门ID"""
    if not conn:
        print("数据库连接不可用，无法查询项目部门ID")
        return None
        
    # 映射部门名称
    mapped_name = map_department_name(department_name)
    if not mapped_name:
        return None
        
    try:
        with conn.cursor() as cursor:
            # 使用模糊匹配查询项目部门ID
            cursor.execute(
                "SELECT project_department_id, project_department_name FROM base_project_department WHERE project_department_name LIKE %s",
                [f"%{mapped_name}%"]
            )
            result = cursor.fetchone()
            
            if result:
                dept_id, full_name = result
                print(f"找到项目部门ID: {dept_id} 对应名称: {full_name}")
                return dept_id
            else:
                print(f"警告: 未找到项目部门名称 '{mapped_name}' (原名: '{department_name}') 对应的ID")
                return None
    except Exception as e:
        print(f"查询项目部门ID时出错: {e}")
        return None

def generate_sql_inserts(csv_file, output_file=None, encoding='utf-8', db_connect=True):
    """从CSV文件生成SQL插入语句"""
    print(f"读取CSV文件: {csv_file}")
    
    try:
        # 连接数据库
        conn = None
        if db_connect:
            conn = connect_to_database()
            if not conn:
                print("警告: 无法连接到数据库，将使用默认ID值")
        
        # 读取CSV文件，跳过前两行（标题）
        df = pd.read_csv(csv_file, header=None, skiprows=2, encoding=encoding)
        
        print("分析CSV文件结构...")
        
        # 初始化处理数据列表
        processed_data_list = []
        
        processed_rows = 0
        skipped_rows = 0
        
        # 用于向前填充
        current_date = None
        current_department = None
        
        # 遍历CSV数据
        for index, row in df.iterrows():
            # 日期列（第一列）
            date_str = row[0]
            if not pd.isna(date_str) and isinstance(date_str, str) and '月' in date_str and '日' in date_str:
                current_date = parse_date(date_str)
            
            # 部门列（第二列）
            if not pd.isna(row[1]) and isinstance(row[1], str) and row[1].strip():
                current_department = row[1].strip()
            
            # 如果没有有效的日期或部门，跳过该行
            if not current_date or not current_department:
                skipped_rows += 1
                continue
            
            # 采场列（第四列）
            stope_name = row[3] if not pd.isna(row[3]) and isinstance(row[3], str) else None
            if not stope_name or stope_name.strip() == '':
                skipped_rows += 1
                continue
            
            # 时列（6,7,8列）
            tons_0_8 = safe_value(row[6], 0)  # 第六列是0-8时
            tons_8_20 = safe_value(row[7], 0) # 第七列是8-20时
            tons_20_0 = safe_value(row[8], 0) # 第八列是20-0时
            
            # 处理三个时间段
            for period, tons in [('0-8', tons_0_8), ('8-20', tons_8_20), ('20-0', tons_20_0)]:
                if tons == 0:
                    continue
                
                working_period_id = WORKING_PERIOD_MAP[period]
                
                # 存储处理后的数据
                processed_data = {
                    'department': current_department,
                    'stope_name': stope_name,
                    'working_period_id': working_period_id,
                    'operation_date': current_date,
                    'tons': tons
                }
                processed_data_list.append(processed_data)
                processed_rows += 1
        
        # 生成SQL插入语句
        data_inserts = []
        
        # 缓存已查询过的采场ID和部门ID，避免重复查询
        stope_id_cache = {}
        department_id_cache = {}
        
        for data in processed_data_list:
            stope_name = data['stope_name']
            department_name = data['department']
            operation_date = data['operation_date']
            working_period_id = data['working_period_id']
            tons = data['tons']
            
            # 查询采场ID
            stope_id = None
            if stope_name in stope_id_cache:
                stope_id = stope_id_cache[stope_name]
            elif conn:
                stope_id = get_stope_id(conn, stope_name)
                stope_id_cache[stope_name] = stope_id
            
            # 查询项目部门ID
            project_department_id = None
            if department_name in department_id_cache:
                project_department_id = department_id_cache[department_name]
            elif conn and department_name:
                project_department_id = get_project_department_id(conn, department_name)
                department_id_cache[department_name] = project_department_id
            
            # 如果没找到ID，跳过
            if not stope_id or not project_department_id:
                print(f"跳过记录: 采场 '{stope_name}' 或部门 '{department_name}' 未找到ID")
                continue
            
            # 生成INSERT语句
            sql = f"INSERT INTO data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons) VALUES ('{operation_date}', {project_department_id}, {stope_id}, {working_period_id}, {tons});"
            data_inserts.append(sql)
        
        # 写入SQL文件
        if not output_file:
            output_file = "chukuang_dml.sql"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            for sql in data_inserts:
                f.write(sql + "\n")
        
        print(f"成功生成 {len(data_inserts)} 条SQL插入语句到 {output_file}")
        print(f"处理行数: {processed_rows}, 跳过行数: {skipped_rows}")
        
        return True
    
    except Exception as e:
        print(f"处理过程中出错: {e}")
        return False

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='生成出矿数据的SQL插入语句')
    parser.add_argument('csv_file', help='CSV文件路径')
    parser.add_argument('-o', '--output', help='输出SQL文件路径', default='chukuang_dml.sql')
    parser.add_argument('--encoding', help='CSV文件编码', default='utf-8')
    parser.add_argument('--no-db', action='store_false', dest='db_connect', help='不连接数据库，使用默认ID')
    args = parser.parse_args()
    
    generate_sql_inserts(
        csv_file=args.csv_file,
        output_file=args.output,
        encoding=args.encoding,
        db_connect=args.db_connect
    )
