#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
收集采场名称脚本
从extracted_data.csv中提取唯一的采场名称，并生成SQL插入语句
用于填充base_stope表（仅插入新的采场名称）
"""

import pandas as pd
import os
import re

def collect_stope_names():
    """从extracted_data.csv中收集唯一的采场名称"""
    
    # 检查文件是否存在
    csv_file = 'extracted_data7.csv'
    if not os.path.exists(csv_file):
        print(f"错误: 找不到文件 {csv_file}")
        return None
    
    try:
        # 读取CSV文件，跳过前两行（标题）
        df = pd.read_csv(csv_file, encoding='utf-8', header=None, skiprows=2)
        
        # 采场名称在第四列（索引为3）
        if len(df.columns) < 4:
            print("错误: CSV文件格式不正确，缺少采场名称列")
            return None
        
        # 获取第四列数据（采场名称）
        stope_names = df.iloc[:, 3].dropna().unique()
        
        # 过滤掉不是采场名称的行（如标题行、合计行等）
        valid_stope_names = []
        for name in stope_names:
            name_str = str(name).strip()
            # 跳过包含"采场"、"合计"、"总计"等关键词的行
            if not any(keyword in name_str for keyword in ["采场名称", "合计", "总计", "小计", "出矿采场"]):
                if name_str and not name_str.isdigit():  # 确保不是空字符串或纯数字
                    valid_stope_names.append(name_str)
        
        return valid_stope_names
    
    except Exception as e:
        print(f"处理CSV文件时出错: {str(e)}")
        return None

def generate_sql_inserts(stope_names):
    """生成SQL插入语句，仅插入base_stope表中不存在的采场名称"""
    if not stope_names:
        print("没有有效的采场名称")
        return
    
    sql_inserts = []
    for name in stope_names:
        # 确定working_face_id：'掘进及其它出矿'为3，其他为2
        working_face_id = 3 if name == '掘进及其它出矿' else 2
        # 生成INSERT语句，使用ON CONFLICT DO NOTHING避免重复
        sql = f"INSERT INTO base_stope (stope_name, working_face_id) VALUES ('{name}', {working_face_id}) ON CONFLICT (stope_name, working_face_id) DO NOTHING;"
        sql_inserts.append(sql)
    
    return sql_inserts

if __name__ == "__main__":
    stope_names = collect_stope_names()
    if stope_names is not None:
        sql_statements = generate_sql_inserts(stope_names)
        if sql_statements:
            # 写入SQL文件
            output_file = "stope_dml.sql"
            with open(output_file, 'w', encoding='utf-8') as f:
                for sql in sql_statements:
                    f.write(sql + "\n")
            print(f"已生成 {len(sql_statements)} 条SQL插入语句到 {output_file}")
        else:
            print("未生成任何SQL插入语句")
