-- 出矿数据视图

-- 创建视图：按日统计出矿数据（总体）
CREATE OR REPLACE VIEW vdata_mucking_out_daily_total_stats AS
SELECT 
    operation_date,
    SUM(tons) AS total_tons
FROM 
    data_mucking_out
GROUP BY 
    operation_date
ORDER BY 
    operation_date;

-- 创建视图：按日统计出矿数据（按作业时段、项目部门、采场）
CREATE OR REPLACE VIEW vdata_mucking_out_daily_stats AS
SELECT 
    operation_date,
    working_period_id,
    project_department_id,
    stope_id,
    SUM(tons) AS total_tons
FROM 
    data_mucking_out
GROUP BY 
    operation_date,
    working_period_id,
    project_department_id,
    stope_id
ORDER BY 
    operation_date, working_period_id, project_department_id, stope_id;

-- 创建视图：按日期和作业时段统计出矿数据
CREATE OR REPLACE VIEW vdata_mucking_out_daily_period_stats AS
SELECT 
    m.operation_date,
    m.working_period_id,
    wp.working_period_name,
    SUM(m.tons) AS total_tons
FROM 
    data_mucking_out m
JOIN
    base_working_period wp ON m.working_period_id = wp.working_period_id
GROUP BY
    m.operation_date,
    m.working_period_id,
    wp.working_period_name
ORDER BY 
    m.operation_date, m.working_period_id;

-- 创建视图：按日期和项目部门统计出矿数据
CREATE OR REPLACE VIEW vdata_mucking_out_daily_department_stats AS
SELECT 
    m.operation_date,
    m.project_department_id,
    pd.project_department_name,
    SUM(m.tons) AS total_tons
FROM 
    data_mucking_out m
JOIN
    base_project_department pd ON m.project_department_id = pd.project_department_id
GROUP BY
    m.operation_date,
    m.project_department_id,
    pd.project_department_name
ORDER BY 
    m.operation_date, m.project_department_id;

-- 创建视图：按日期和采场统计出矿数据
CREATE OR REPLACE VIEW vdata_mucking_out_daily_stope_stats AS
SELECT 
    m.operation_date,
    m.stope_id,
    s.stope_name,
    SUM(m.tons) AS total_tons
FROM 
    data_mucking_out m
JOIN
    base_stope s ON m.stope_id = s.stope_id
GROUP BY
    m.operation_date,
    m.stope_id,
    s.stope_name
ORDER BY 
    m.operation_date, m.stope_id;

-- 创建视图：按月统计出矿数据（总体）（月定义为前一个月的29号到当月28号）
CREATE OR REPLACE VIEW vdata_mucking_out_monthly_total_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    SUM(tons) AS total_tons
FROM 
    data_mucking_out
CROSS JOIN LATERAL get_financial_month(operation_date) AS fm
GROUP BY 
    fm.financial_year, fm.financial_month
ORDER BY 
    fm.financial_year, fm.financial_month;

-- 创建视图：按月统计出矿数据（按作业时段、项目部门、采场）
CREATE OR REPLACE VIEW vdata_mucking_out_monthly_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    working_period_id,
    project_department_id,
    stope_id,
    SUM(tons) AS total_tons
FROM 
    data_mucking_out
CROSS JOIN LATERAL get_financial_month(operation_date) AS fm
GROUP BY 
    fm.financial_year, fm.financial_month, working_period_id, project_department_id, stope_id
ORDER BY 
    fm.financial_year, fm.financial_month, working_period_id, project_department_id, stope_id;

-- 创建视图：按月份和作业时段统计出矿数据
CREATE OR REPLACE VIEW vdata_mucking_out_monthly_period_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    m.working_period_id,
    wp.working_period_name,
    SUM(m.tons) AS total_tons
FROM 
    data_mucking_out m
CROSS JOIN LATERAL get_financial_month(m.operation_date) AS fm
JOIN
    base_working_period wp ON m.working_period_id = wp.working_period_id
GROUP BY 
    fm.financial_year, fm.financial_month, m.working_period_id, wp.working_period_name
ORDER BY 
    year, month, m.working_period_id;

-- 创建视图：按月份和项目部门统计出矿数据
CREATE OR REPLACE VIEW vdata_mucking_out_monthly_department_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    m.project_department_id,
    pd.project_department_name,
    SUM(m.tons) AS total_tons
FROM 
    data_mucking_out m
CROSS JOIN LATERAL get_financial_month(m.operation_date) AS fm
JOIN
    base_project_department pd ON m.project_department_id = pd.project_department_id
GROUP BY 
    fm.financial_year, fm.financial_month, m.project_department_id, pd.project_department_name
ORDER BY 
    year, month, m.project_department_id;

-- 创建视图：按月份和采场统计出矿数据
CREATE OR REPLACE VIEW vdata_mucking_out_monthly_stope_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    m.stope_id,
    s.stope_name,
    SUM(m.tons) AS total_tons
FROM 
    data_mucking_out m
CROSS JOIN LATERAL get_financial_month(m.operation_date) AS fm
JOIN
    base_stope s ON m.stope_id = s.stope_id
GROUP BY 
    fm.financial_year, fm.financial_month, m.stope_id, s.stope_name
ORDER BY 
    year, month, m.stope_id;

-- 创建视图：按周统计出矿数据（总体）
CREATE OR REPLACE VIEW vdata_mucking_out_nature_weekly_total_stats AS
SELECT 
    EXTRACT(YEAR FROM operation_date) AS year,
    EXTRACT(WEEK FROM operation_date) AS week_number,
    MIN(operation_date) AS week_start_date,
    MAX(operation_date) AS week_end_date,
    SUM(tons) AS total_tons
FROM 
    data_mucking_out
GROUP BY 
    EXTRACT(YEAR FROM operation_date),
    EXTRACT(WEEK FROM operation_date)
ORDER BY 
    year, week_number;

-- 创建视图：按周统计出矿数据（按作业时段、项目部门、采场）
CREATE OR REPLACE VIEW vdata_mucking_out_nature_weekly_stats AS
SELECT 
    EXTRACT(YEAR FROM operation_date) AS year,
    EXTRACT(WEEK FROM operation_date) AS week_number,
    MIN(operation_date) AS week_start_date,
    MAX(operation_date) AS week_end_date,
    working_period_id,
    project_department_id,
    stope_id,
    SUM(tons) AS total_tons
FROM 
    data_mucking_out
GROUP BY 
    EXTRACT(YEAR FROM operation_date),
    EXTRACT(WEEK FROM operation_date),
    working_period_id,
    project_department_id,
    stope_id
ORDER BY 
    year, week_number, working_period_id, project_department_id, stope_id;

-- 创建视图：按周和作业时段统计出矿数据
CREATE OR REPLACE VIEW vdata_mucking_out_nature_weekly_period_stats AS
SELECT 
    EXTRACT(YEAR FROM m.operation_date) AS year,
    EXTRACT(WEEK FROM m.operation_date) AS week_number,
    MIN(m.operation_date) AS week_start_date,
    MAX(m.operation_date) AS week_end_date,
    m.working_period_id,
    wp.working_period_name,
    SUM(m.tons) AS total_tons
FROM 
    data_mucking_out m
JOIN
    base_working_period wp ON m.working_period_id = wp.working_period_id
GROUP BY 
    EXTRACT(YEAR FROM m.operation_date),
    EXTRACT(WEEK FROM m.operation_date),
    m.working_period_id,
    wp.working_period_name
ORDER BY 
    year, week_number, m.working_period_id;

-- 创建视图：按周和项目部门统计出矿数据
CREATE OR REPLACE VIEW vdata_mucking_out_nature_weekly_department_stats AS
SELECT 
    EXTRACT(YEAR FROM m.operation_date) AS year,
    EXTRACT(WEEK FROM m.operation_date) AS week_number,
    MIN(m.operation_date) AS week_start_date,
    MAX(m.operation_date) AS week_end_date,
    m.project_department_id,
    pd.project_department_name,
    SUM(m.tons) AS total_tons
FROM 
    data_mucking_out m
JOIN
    base_project_department pd ON m.project_department_id = pd.project_department_id
GROUP BY 
    EXTRACT(YEAR FROM m.operation_date),
    EXTRACT(WEEK FROM m.operation_date),
    m.project_department_id,
    pd.project_department_name
ORDER BY 
    year, week_number, m.project_department_id;

-- 创建视图：按周和采场统计出矿数据
CREATE OR REPLACE VIEW vdata_mucking_out_nature_weekly_stope_stats AS
SELECT 
    EXTRACT(YEAR FROM m.operation_date) AS year,
    EXTRACT(WEEK FROM m.operation_date) AS week_number,
    MIN(m.operation_date) AS week_start_date,
    MAX(m.operation_date) AS week_end_date,
    m.stope_id,
    s.stope_name,
    SUM(m.tons) AS total_tons
FROM 
    data_mucking_out m
JOIN
    base_stope s ON m.stope_id = s.stope_id
GROUP BY 
    EXTRACT(YEAR FROM m.operation_date),
    EXTRACT(WEEK FROM m.operation_date),
    m.stope_id,
    s.stope_name
ORDER BY 
    year, week_number, m.stope_id;

-- 创建视图：按周统计出矿数据（总体）（周四到周三为一周）
CREATE OR REPLACE VIEW vdata_mucking_out_weekly_total_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number,
    wk.week_start_date,
    wk.week_end_date,
    SUM(tons) AS total_tons
FROM 
    data_mucking_out
CROSS JOIN LATERAL get_week_thu_to_wed(operation_date) AS wk
GROUP BY 
    wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date
ORDER BY 
    year, week_number;

-- 创建视图：按周统计出矿数据（按作业时段、项目部门、采场）（周四到周三为一周）
CREATE OR REPLACE VIEW vdata_mucking_out_weekly_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number,
    wk.week_start_date,
    wk.week_end_date,
    working_period_id,
    project_department_id,
    stope_id,
    SUM(tons) AS total_tons
FROM 
    data_mucking_out
CROSS JOIN LATERAL get_week_thu_to_wed(operation_date) AS wk
GROUP BY 
    wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date,
    working_period_id,
    project_department_id,
    stope_id
ORDER BY 
    year, week_number, working_period_id, project_department_id, stope_id;

-- 创建视图：按周和作业时段统计出矿数据（周四到周三为一周）
CREATE OR REPLACE VIEW vdata_mucking_out_weekly_period_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number,
    wk.week_start_date,
    wk.week_end_date,
    m.working_period_id,
    wp.working_period_name,
    SUM(m.tons) AS total_tons
FROM 
    data_mucking_out m
CROSS JOIN LATERAL get_week_thu_to_wed(m.operation_date) AS wk
JOIN
    base_working_period wp ON m.working_period_id = wp.working_period_id
GROUP BY 
    wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date,
    m.working_period_id,
    wp.working_period_name
ORDER BY 
    year, week_number, m.working_period_id;

-- 创建视图：按周和项目部门统计出矿数据（周四到周三为一周）
CREATE OR REPLACE VIEW vdata_mucking_out_weekly_department_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number,
    wk.week_start_date,
    wk.week_end_date,
    m.project_department_id,
    pd.project_department_name,
    SUM(m.tons) AS total_tons
FROM 
    data_mucking_out m
CROSS JOIN LATERAL get_week_thu_to_wed(m.operation_date) AS wk
JOIN
    base_project_department pd ON m.project_department_id = pd.project_department_id
GROUP BY 
    wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date,
    m.project_department_id,
    pd.project_department_name
ORDER BY 
    year, week_number, m.project_department_id;

-- 创建视图：按周和采场统计出矿数据（周四到周三为一周）
CREATE OR REPLACE VIEW vdata_mucking_out_weekly_stope_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number,
    wk.week_start_date,
    wk.week_end_date,
    m.stope_id,
    s.stope_name,
    SUM(m.tons) AS total_tons
FROM 
    data_mucking_out m
CROSS JOIN LATERAL get_week_thu_to_wed(m.operation_date) AS wk
JOIN
    base_stope s ON m.stope_id = s.stope_id
GROUP BY 
    wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date,
    m.stope_id,
    s.stope_name
ORDER BY 
    year, week_number, m.stope_id;

-- 创建视图：按年统计出矿数据（总体）（年定义为上一年12月29号到当年12月28号）
CREATE OR REPLACE VIEW vdata_mucking_out_yearly_total_stats AS
SELECT 
    fy.financial_year AS year,
    SUM(tons) AS total_tons
FROM 
    data_mucking_out
CROSS JOIN LATERAL get_financial_year(operation_date) AS fy
GROUP BY 
    fy.financial_year
ORDER BY 
    fy.financial_year;

-- 创建视图：按年统计出矿数据（按作业时段、项目部门、采场）
CREATE OR REPLACE VIEW vdata_mucking_out_yearly_stats AS
SELECT 
    fy.financial_year AS year,
    working_period_id,
    project_department_id,
    stope_id,
    SUM(tons) AS total_tons
FROM 
    data_mucking_out
CROSS JOIN LATERAL get_financial_year(operation_date) AS fy
GROUP BY 
    fy.financial_year, working_period_id, project_department_id, stope_id
ORDER BY 
    fy.financial_year, working_period_id, project_department_id, stope_id;

-- 创建视图：按年份和作业时段统计出矿数据
CREATE OR REPLACE VIEW vdata_mucking_out_yearly_period_stats AS
SELECT 
    fy.financial_year AS year,
    m.working_period_id,
    wp.working_period_name,
    SUM(m.tons) AS total_tons
FROM 
    data_mucking_out m
CROSS JOIN LATERAL get_financial_year(m.operation_date) AS fy
JOIN
    base_working_period wp ON m.working_period_id = wp.working_period_id
GROUP BY 
    fy.financial_year, m.working_period_id, wp.working_period_name
ORDER BY 
    year, m.working_period_id;

-- 创建视图：按年份和项目部门统计出矿数据
CREATE OR REPLACE VIEW vdata_mucking_out_yearly_department_stats AS
SELECT 
    fy.financial_year AS year,
    m.project_department_id,
    pd.project_department_name,
    SUM(m.tons) AS total_tons
FROM 
    data_mucking_out m
CROSS JOIN LATERAL get_financial_year(m.operation_date) AS fy
JOIN
    base_project_department pd ON m.project_department_id = pd.project_department_id
GROUP BY 
    fy.financial_year, m.project_department_id, pd.project_department_name
ORDER BY 
    year, m.project_department_id;

-- 创建视图：按年份和采场统计出矿数据
CREATE OR REPLACE VIEW vdata_mucking_out_yearly_stope_stats AS
SELECT 
    fy.financial_year AS year,
    m.stope_id,
    s.stope_name,
    SUM(m.tons) AS total_tons
FROM 
    data_mucking_out m
CROSS JOIN LATERAL get_financial_year(m.operation_date) AS fy
JOIN
    base_stope s ON m.stope_id = s.stope_id
GROUP BY 
    fy.financial_year, m.stope_id, s.stope_name
ORDER BY 
    year, m.stope_id;

