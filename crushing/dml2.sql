-- 插入旋回破碎运行数据到PostgreSQL数据库
-- 生成日期: 2025-07-18 21:53:37

-- 清空现有数据（如果需要）
-- TRUNCATE TABLE data_crushing_operation CASCADE;

-- 插入旋回破碎运行数据
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-01-29', 0, 0, '主井停机', NULL, NULL, 0.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-01-29', 0, 0, '主井停机', NULL, NULL, 0.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-01-29', 0, 0, '主井停机', NULL, NULL, 0.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-01-31', 210, 0, NULL, NULL, NULL, 5600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-01-31', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-02-01', 30, 90, '下料口堵大块', NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-02-01', 195, 0, NULL, NULL, NULL, 5200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-02-01', 15, 150, '下料口堵大块', NULL, NULL, 400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-02-02', 60, 200, '-1130下料口堵料', NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-02-02', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-02-02', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-02-03', 120, 0, NULL, NULL, NULL, 3200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-02-03', 225, 0, NULL, NULL, NULL, 6000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-02-03', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-02-04', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-02-04', 180, 0, NULL, NULL, NULL, 4800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-02-04', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-02-05', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-02-05', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-02-05', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-02-06', 105, 0, NULL, NULL, NULL, 2800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-02-06', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-02-06', 60, 60, '震动放矿机堵大块', NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-02-07', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-02-07', 75, 0, NULL, NULL, NULL, 2000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-02-07', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-02-08', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-02-08', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-02-09', 120, 0, NULL, NULL, NULL, 3200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-02-09', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-02-09', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-02-10', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-02-10', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-02-10', 15, 0, NULL, NULL, NULL, 400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-02-11', 0, 0, '井下断电未运行', NULL, NULL, 0.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-02-11', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-02-11', 15, 0, NULL, NULL, NULL, 400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-02-12', 15, 0, NULL, NULL, NULL, 400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-02-12', 0, 0, '未运行', NULL, NULL, 0.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-02-12', 0, 0, '未运行', NULL, NULL, 0.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-02-13', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-02-13', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-02-13', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-02-14', 15, 0, NULL, NULL, NULL, 400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-02-14', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-02-14', 45, 0, NULL, NULL, NULL, 1200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-02-15', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-02-15', 210, 0, NULL, NULL, NULL, 5600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-02-15', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-02-16', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-02-16', 120, 0, NULL, NULL, NULL, 3200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-02-16', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-02-17', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-02-17', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-02-17', 45, 0, NULL, NULL, NULL, 1200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-02-18', 180, 0, NULL, NULL, NULL, 4800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-02-18', 195, 0, NULL, NULL, NULL, 5200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-02-18', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-02-19', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-02-19', 195, 0, NULL, NULL, NULL, 5200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-02-19', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-02-20', 45, 0, NULL, NULL, NULL, 1200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-02-20', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-02-20', 120, 0, NULL, NULL, NULL, 3200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-02-21', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-02-21', 195, 0, NULL, NULL, NULL, 5200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-02-21', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-02-22', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-02-22', 105, 0, NULL, NULL, NULL, 2800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-02-22', 45, 0, NULL, NULL, NULL, 1200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-02-23', 30, 90, '4：10-5：40 2号放矿机堵料', NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-02-23', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-02-23', 90, 70, '23:10-0:20   2号放矿机堵料', '23:10:00', '00:20:00', 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-02-24', 45, 40, '0:40-1:20   2号放矿机堵料', '00:40:00', '01:20:00', 1200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-02-24', 195, 0, NULL, NULL, NULL, 5200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-02-24', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-02-25', 45, 0, NULL, NULL, NULL, 1200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-02-25', 165, 0, NULL, NULL, NULL, 4400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-02-25', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-02-26', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-02-26', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-02-26', 45, 180, '21:00-24:00检修动锥', NULL, NULL, 1200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-02-27', 0, 420, '0:00-7:00，-1130检修动锥', '00:00:00', '07:00:00', 0.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-02-27', 0, 420, '7：00-19：00，-1130动锥检修', NULL, NULL, 0.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-02-27', 0, 300, NULL, NULL, NULL, 0.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-02-28', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-02-28', 225, 145, '7：30-9：45，1#放矿机堵料', NULL, NULL, 6000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-02-28', 45, 0, NULL, NULL, NULL, 1200.0, 'admin', 'admin');