#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
生成SQL插入语句脚本
从CSV文件生成PostgreSQL数据库的INSERT语句 - 旋回破碎运行数据
"""

import pandas as pd
import re
from datetime import datetime, time
import os
import argparse
from decimal import Decimal, getcontext

# 设置decimal精度
getcontext().prec = 10  # 设置精度为10位

def get_working_period_id(time_str):
    """根据时间段获取对应的工作时段ID"""
    if not isinstance(time_str, str):
        return None
    
    if '0-8' in time_str:
        return 1  # 早班
    elif '8-20' in time_str:
        return 2  # 白班
    elif '20-24' in time_str or '20-0' in time_str:
        return 3  # 夜班
    return None

def parse_date(date_str):
    """解析中文日期格式，转换为标准日期格式"""
    if not isinstance(date_str, str):
        return None
    
    try:
        # 提取月和日
        month_match = re.search(r'(\d+)月', date_str)
        day_match = re.search(r'(\d+)日', date_str)
        
        if month_match and day_match:
            month = int(month_match.group(1))
            day = int(day_match.group(1))
            # 假设年份是2025年
            return f"2025-{month:02d}-{day:02d}"
    except Exception as e:
        print(f"日期解析错误: {date_str}, 错误: {e}")
    
    return None

def parse_fault_time_range(fault_reason):
    """从故障原因中解析故障开始和结束时间"""
    if not isinstance(fault_reason, str):
        return None, None
    
    try:
        # 匹配时间范围，如 "0:00-7:00停机检修"
        time_range_match = re.search(r'(\d+):(\d+)-(\d+):(\d+)', fault_reason)
        if time_range_match:
            start_hour = int(time_range_match.group(1))
            start_minute = int(time_range_match.group(2))
            end_hour = int(time_range_match.group(3))
            end_minute = int(time_range_match.group(4))
            
            start_time = time(start_hour, start_minute)
            end_time = time(end_hour, end_minute)
            
            return start_time, end_time
    except Exception as e:
        print(f"解析故障时间范围错误: {fault_reason}, 错误: {e}")
    
    return None, None

def safe_value(value, default=0, as_decimal=True):
    """安全获取值，处理NaN和None，并确保返回数字类型
    参数:
        value: 要转换的值
        default: 默认值，当转换失败时返回
        as_decimal: 是否返回Decimal类型，如果为False则返回float
    """
    if pd.isna(value) or value is None:
        return Decimal(default) if as_decimal else default
    
    # 尝试转换为数字
    try:
        # 如果是字符串，尝试转换
        if isinstance(value, str):
            # 移除可能的逗号、空格等
            value = value.replace(',', '').strip()
            if value == '':
                return Decimal(default) if as_decimal else default
            return Decimal(value) if as_decimal else float(value)
        # 如果已经是数字类型，转换为Decimal或直接返回
        elif isinstance(value, (int, float)):
            return Decimal(str(value)) if as_decimal else value
        # 其他情况，尝试强制转换
        else:
            return Decimal(str(value)) if as_decimal else float(value)
    except (ValueError, TypeError):
        print(f"警告: 无法将值 '{value}' ({type(value)}) 转换为数字，使用默认值 {default}")
        return Decimal(default) if as_decimal else default

def generate_sql_inserts(csv_file, output_file=None, encoding='utf-8'):
    """从CSV文件生成SQL插入语句"""
    print(f"读取CSV文件: {csv_file}")
    
    try:
        # 读取CSV文件，不使用第一行作为列名
        df = pd.read_csv(csv_file, header=None, encoding=encoding)
        
        print("分析CSV文件结构...")
        
        # 初始化处理数据列表
        generate_sql_inserts.processed_data_list = []
        
        processed_rows = 0
        skipped_rows = 0
        
        # 用于向前填充
        current_date = None
        
        # 遍历CSV数据
        for index, row in df.iterrows():
            # 跳过标题行和空行
            if index == 0 or pd.isna(row[0]) or not isinstance(row[0], str):
                skipped_rows += 1
                continue
                
            # 处理日期
            if isinstance(row[0], str) and '月' in row[0] and '日' in row[0]:
                current_date = parse_date(row[0])
                
                # 跳过标题行（如"旋回破碎运行统计表"）
                if '旋回破碎运行统计表' in str(row[1]):
                    skipped_rows += 1
                    continue
                    
                # 跳过表头行（如"班次,运行时间分钟,故障处理时间（分钟）,故障原因及时间,破碎量"）
                if '班次' in str(row[1]) and '运行时间' in str(row[2]):
                    skipped_rows += 1
                    continue
                
                # 跳过日合计行
                if '日合计' in str(row[1]):
                    skipped_rows += 1
                    continue
            
            # 如果没有有效的日期，跳过该行
            if not current_date:
                skipped_rows += 1
                continue
            
            # 获取班次（工作时段）
            shift = row[1] if not pd.isna(row[1]) and isinstance(row[1], str) else None
            
            # 如果没有班次信息，跳过该行
            if not shift or not any(period in shift for period in ['0-8', '8-20', '20-0', '20-24']):
                skipped_rows += 1
                continue
            
            # 获取工作时段ID
            working_period_id = get_working_period_id(shift)
            if not working_period_id:
                skipped_rows += 1
                continue
            
            # 获取运行时间（分钟）
            operation_time = safe_value(row[2], 0, False)
            
            # 获取故障时间（分钟）
            fault_time = safe_value(row[3], 0, False)
            
            # 获取故障原因
            fault_reason = row[4] if not pd.isna(row[4]) and isinstance(row[4], str) else None
            
            # 解析故障开始和结束时间
            fault_start_time, fault_end_time = parse_fault_time_range(fault_reason)
            
            # 获取破碎量（吨）
            crushing_volume = safe_value(row[5], 0, False)
            
            # 如果运行时间和破碎量都为0，且没有故障信息，则跳过该行
            if operation_time == 0 and crushing_volume == 0 and fault_time == 0 and not fault_reason:
                skipped_rows += 1
                continue
            
            # 存储处理后的数据
            processed_data = {
                'working_period_id': working_period_id,
                'operation_date': current_date,
                'operation_time': int(operation_time),
                'fault_time': int(fault_time),
                'fault_reason': fault_reason,
                'fault_start_time': fault_start_time.strftime('%H:%M:%S') if fault_start_time else None,
                'fault_end_time': fault_end_time.strftime('%H:%M:%S') if fault_end_time else None,
                'crushing_volume': float(crushing_volume)
            }
            
            # 添加到处理过的数据列表
            generate_sql_inserts.processed_data_list.append(processed_data)
            processed_rows += 1
        
        # 生成SQL插入语句
        data_inserts = []
        
        # 生成数据插入语句 - 每行数据一条SQL语句
        for data in generate_sql_inserts.processed_data_list:
            # 处理可能的NULL值
            fault_reason_sql = f"'{data['fault_reason']}'" if data['fault_reason'] else "NULL"
            fault_start_time_sql = f"'{data['fault_start_time']}'" if data['fault_start_time'] else "NULL"
            fault_end_time_sql = f"'{data['fault_end_time']}'" if data['fault_end_time'] else "NULL"
            
            # 生成插入语句
            data_inserts.append(f"""
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
({data['working_period_id']}, '{data['operation_date']}', {data['operation_time']}, {data['fault_time']}, {fault_reason_sql}, {fault_start_time_sql}, {fault_end_time_sql}, {data['crushing_volume']}, 'admin', 'admin');""".strip())
        
        # 组合所有SQL语句
        all_inserts = [
            "-- 插入旋回破碎运行数据到PostgreSQL数据库",
            f"-- 生成日期: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "-- 清空现有数据（如果需要）",
            "-- TRUNCATE TABLE data_crushing_operation CASCADE;",
            "",
            "-- 插入旋回破碎运行数据",
            *data_inserts
        ]
        
        sql_content = '\n'.join(all_inserts)
        
        # 如果指定了输出文件，写入文件
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(sql_content)
            print(f"SQL插入语句已写入文件: {output_file}")
        else:
            print(sql_content)
        
        print(f"处理完成: 处理了 {processed_rows} 行数据，跳过了 {skipped_rows} 行")
        
    except Exception as e:
        print(f"生成SQL插入语句时出错: {e}")

def main():
    parser = argparse.ArgumentParser(description='从CSV文件生成SQL插入语句')
    parser.add_argument('csv_file', help='CSV文件路径')
    parser.add_argument('-o', '--output', help='输出SQL文件路径')
    parser.add_argument('--encoding', default='utf-8', help='CSV文件编码，默认为utf-8')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.csv_file):
        print(f"错误: 找不到CSV文件 '{args.csv_file}'")
        return
    
    generate_sql_inserts(args.csv_file, args.output, args.encoding)

if __name__ == "__main__":
    main()
