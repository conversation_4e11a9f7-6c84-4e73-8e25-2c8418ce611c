-- 插入旋回破碎运行数据到PostgreSQL数据库
-- 生成日期: 2025-07-18 21:55:24

-- 清空现有数据（如果需要）
-- TRUNCATE TABLE data_crushing_operation CASCADE;

-- 插入旋回破碎运行数据
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-06-29', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-06-29', 180, 0, NULL, NULL, NULL, 4800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-06-29', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-06-30', 135, 0, NULL, NULL, NULL, 3600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-06-30', 165, 0, NULL, NULL, NULL, 4400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-06-30', 105, 0, NULL, NULL, NULL, 2800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-07-01', 150, 0, NULL, NULL, NULL, 4000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-07-01', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-07-02', 0, 0, '计划检修', NULL, NULL, 0.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-07-02', 240, 0, NULL, NULL, NULL, 6400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-07-02', 150, 0, NULL, NULL, NULL, 4000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-07-03', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-07-03', 135, 0, NULL, NULL, NULL, 3600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-07-03', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-07-04', 75, 0, NULL, NULL, NULL, 2000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-07-04', 180, 0, NULL, NULL, NULL, 4800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-07-04', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-07-05', 75, 0, NULL, NULL, NULL, 2000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-07-05', 120, 0, NULL, NULL, NULL, 3200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-07-05', 135, 0, NULL, NULL, NULL, 3600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-07-06', 105, 0, NULL, NULL, NULL, 2800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-07-06', 150, 0, NULL, NULL, NULL, 4000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-07-06', 105, 0, NULL, NULL, NULL, 2800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-07-07', 135, 0, NULL, NULL, NULL, 3600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-07-07', 180, 0, NULL, NULL, NULL, 4800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-07-07', 75, 0, NULL, NULL, NULL, 2000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-07-08', 75, 0, NULL, NULL, NULL, 2000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-07-08', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-07-08', 120, 0, NULL, NULL, NULL, 3200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-07-09', 150, 0, NULL, NULL, NULL, 4000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-07-09', 135, 0, NULL, NULL, NULL, 3600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-07-09', 75, 0, NULL, NULL, NULL, 2000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-07-10', 135, 0, NULL, NULL, NULL, 3600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-07-10', 135, 0, NULL, NULL, NULL, 3600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-07-10', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-07-11', 105, 0, NULL, NULL, NULL, 2800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-07-11', 135, 0, NULL, NULL, NULL, 3600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-07-11', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-07-12', 150, 0, NULL, NULL, NULL, 4000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-07-12', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-07-12', 15, 0, NULL, NULL, NULL, 400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-07-13', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-07-13', 165, 0, NULL, NULL, NULL, 4400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-07-13', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-07-14', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-07-14', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-07-15', 45, 0, NULL, NULL, NULL, 1200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-07-15', 105, 0, NULL, NULL, NULL, 2800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-07-16', 105, 0, NULL, NULL, NULL, 2800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-07-16', 165, 0, NULL, NULL, NULL, 4400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-07-16', 75, 0, NULL, NULL, NULL, 2000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-07-17', 120, 0, NULL, NULL, NULL, 3200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-07-17', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-07-17', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-07-18', 120, 0, NULL, NULL, NULL, 3200.0, 'admin', 'admin');