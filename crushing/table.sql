-- Create table for crushing operation data
CREATE TABLE data_crushing_operation (
    id BIGSERIAL PRIMARY KEY,
    working_period_id BIGINT NOT NULL,            -- 作业时段ID
    operation_date DATE NOT NULL,                 -- 作业日期
    operation_time INTEGER,                       -- 运行时间（分钟）
    fault_time INTEGER,                           -- 故障时长（分钟）
    fault_reason VARCHAR(255),                    -- 故障原因
    fault_start_time TIME,                        -- 故障开始时间
    fault_end_time TIME,                          -- 故障结束时间
    crushing_volume NUMERIC(10, 2),               -- 破碎量（吨）
    create_by VARCHAR(64) DEFAULT 'admin',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(64) DEFAULT 'admin',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for faster queries
CREATE INDEX idx_data_crushing_operation_date ON data_crushing_operation(operation_date);

CREATE INDEX idx_data_crushing_operation_period ON data_crushing_operation(working_period_id);

CREATE INDEX idx_data_crushing_operation_date_period ON data_crushing_operation(operation_date, working_period_id);

-- Add comments to table and columns
COMMENT ON TABLE data_crushing_operation IS '旋回破碎运行统计数据表';
COMMENT ON COLUMN data_crushing_operation.id IS '破碎数据ID';
COMMENT ON COLUMN data_crushing_operation.working_period_id IS '作业时段ID';
COMMENT ON COLUMN data_crushing_operation.operation_date IS '作业日期';
COMMENT ON COLUMN data_crushing_operation.operation_time IS '运行时间（分钟）';
COMMENT ON COLUMN data_crushing_operation.fault_time IS '故障时长（分钟）';
COMMENT ON COLUMN data_crushing_operation.fault_reason IS '故障原因';
COMMENT ON COLUMN data_crushing_operation.fault_start_time IS '故障开始时间';
COMMENT ON COLUMN data_crushing_operation.fault_end_time IS '故障结束时间';
COMMENT ON COLUMN data_crushing_operation.crushing_volume IS '破碎量（吨）';
COMMENT ON COLUMN data_crushing_operation.create_by IS '创建人';
COMMENT ON COLUMN data_crushing_operation.create_time IS '创建时间';
COMMENT ON COLUMN data_crushing_operation.update_by IS '更新人';
COMMENT ON COLUMN data_crushing_operation.update_time IS '更新时间';
