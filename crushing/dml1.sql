-- 插入旋回破碎运行数据到PostgreSQL数据库
-- 生成日期: 2025-07-18 21:53:25

-- 清空现有数据（如果需要）
-- TRUNCATE TABLE data_crushing_operation CASCADE;

-- 插入旋回破碎运行数据
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-01-01', 45, 0, NULL, NULL, NULL, 1200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-01-01', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-01-01', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-01-02', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-01-02', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-01-02', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-01-03', 75, 0, NULL, NULL, NULL, 2000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-01-03', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-01-03', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-01-04', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-01-04', 240, 0, NULL, NULL, NULL, 6400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-01-04', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-01-05', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-01-05', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-01-05', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-01-06', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-01-06', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-01-06', 45, 0, NULL, NULL, NULL, 1200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-01-07', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-01-07', 105, 0, NULL, NULL, NULL, 2800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-01-07', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-01-08', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-01-08', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-01-08', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-01-09', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-01-09', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-01-09', 20, 0, NULL, NULL, NULL, 500.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-01-10', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-01-10', 150, 75, '17:49-19:04下料口堵料', '17:49:00', '19:04:00', 4000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-01-10', 60, 59, '19:52-20:51下料口堵料', '19:52:00', '20:51:00', 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-01-11', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-01-11', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-01-11', 40, 0, NULL, NULL, NULL, 1000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-01-12', 15, 0, NULL, NULL, NULL, 400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-01-12', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-01-13', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-01-13', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-01-13', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-01-14', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-01-14', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-01-14', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-01-15', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-01-15', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-01-15', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-01-16', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-01-16', 180, 0, NULL, NULL, NULL, 5400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-01-16', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-01-17', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-01-17', 90, 128, '15:12-17:20，-1130棚料', '15:12:00', '17:20:00', 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-01-17', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-01-18', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-01-18', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-01-18', 45, 0, NULL, NULL, NULL, 1200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-01-19', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-01-19', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-01-19', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-01-20', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-01-20', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-01-20', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-01-21', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-01-21', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-01-21', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-01-22', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-01-22', 180, 0, NULL, NULL, NULL, 4800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-01-22', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-01-23', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-01-23', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-01-23', 45, 0, NULL, NULL, NULL, 1200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-01-24', 45, 0, NULL, NULL, NULL, 1200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-01-24', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-01-24', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-01-25', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-01-25', 175, 0, NULL, NULL, NULL, 4400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-01-25', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-01-26', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-01-26', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-01-26', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-01-27', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-01-27', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-01-27', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');