
-- 创建视图：按日统计破碎和故障数据
CREATE OR REPLACE VIEW vdata_crushing_daily_stats AS
SELECT 
    operation_date,
    SUM(operation_time) AS total_operation_time,
    SUM(fault_time) AS total_fault_time,
    COUNT(CASE WHEN fault_time > 0 THEN 1 END) AS fault_count,
    SUM(crushing_volume) AS total_crushing_volume
FROM 
    data_crushing_operation
GROUP BY 
    operation_date
ORDER BY 
    operation_date;

-- 创建视图：按日期和时段统计破碎和故障数据
CREATE OR REPLACE VIEW vdata_crushing_daily_period_stats AS
SELECT 
    c.operation_date,
    p.working_period_id,
    p.working_period_name,
    SUM(c.operation_time) AS total_operation_time,
    SUM(c.fault_time) AS total_fault_time,
    COUNT(CASE WHEN c.fault_time > 0 THEN 1 END) AS fault_count,
    SUM(c.crushing_volume) AS total_crushing_volume
FROM 
    data_crushing_operation c
JOIN
    base_working_period p ON c.working_period_id = p.working_period_id
GROUP BY
    c.operation_date,
    p.working_period_id,
    p.working_period_name
ORDER BY 
    c.operation_date, p.working_period_id;


-- 创建视图：按月统计破碎和故障数据（月定义为前一个月的29号到当月28号）
CREATE OR REPLACE VIEW vdata_crushing_monthly_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    SUM(operation_time) AS total_operation_time,
    SUM(fault_time) AS total_fault_time,
    COUNT(CASE WHEN fault_time > 0 THEN 1 END) AS fault_count,
    SUM(crushing_volume) AS total_crushing_volume
FROM 
    data_crushing_operation
CROSS JOIN LATERAL get_financial_month(operation_date) AS fm
GROUP BY 
    fm.financial_year, fm.financial_month
ORDER BY 
    year, month;

-- 创建视图：按月份和时段统计破碎和故障数据（月定义为前一个月的29号到当月28号）
CREATE OR REPLACE VIEW vdata_crushing_monthly_period_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    p.working_period_id,
    p.working_period_name,
    SUM(c.operation_time) AS total_operation_time,
    SUM(c.fault_time) AS total_fault_time,
    COUNT(CASE WHEN c.fault_time > 0 THEN 1 END) AS fault_count,
    SUM(c.crushing_volume) AS total_crushing_volume
FROM 
    data_crushing_operation c
CROSS JOIN LATERAL get_financial_month(c.operation_date) AS fm
JOIN
    base_working_period p ON c.working_period_id = p.working_period_id
GROUP BY 
    fm.financial_year, fm.financial_month,
    p.working_period_id,
    p.working_period_name
ORDER BY 
    year, month, p.working_period_id;

-- 创建视图：按周统计破碎和故障数据
CREATE OR REPLACE VIEW vdata_crushing_nature_weekly_stats AS
SELECT 
    EXTRACT(YEAR FROM operation_date) AS year,
    EXTRACT(WEEK FROM operation_date) AS week_number,
    MIN(operation_date) AS week_start_date,
    MAX(operation_date) AS week_end_date,
    SUM(operation_time) AS total_operation_time,
    SUM(fault_time) AS total_fault_time,
    COUNT(CASE WHEN fault_time > 0 THEN 1 END) AS fault_count,
    SUM(crushing_volume) AS total_crushing_volume
FROM 
    data_crushing_operation
GROUP BY 
    EXTRACT(YEAR FROM operation_date),
    EXTRACT(WEEK FROM operation_date)
ORDER BY 
    year, week_number;

-- 创建视图：按周和时段统计破碎和故障数据
CREATE OR REPLACE VIEW vdata_crushing_nature_weekly_period_stats AS
SELECT 
    EXTRACT(YEAR FROM c.operation_date) AS year,
    EXTRACT(WEEK FROM c.operation_date) AS week_number,
    MIN(c.operation_date) AS week_start_date,
    MAX(c.operation_date) AS week_end_date,
    p.working_period_id,
    p.working_period_name,
    SUM(c.operation_time) AS total_operation_time,
    SUM(c.fault_time) AS total_fault_time,
    COUNT(CASE WHEN c.fault_time > 0 THEN 1 END) AS fault_count,
    SUM(c.crushing_volume) AS total_crushing_volume
FROM 
    data_crushing_operation c
JOIN
    base_working_period p ON c.working_period_id = p.working_period_id
GROUP BY 
    EXTRACT(YEAR FROM c.operation_date),
    EXTRACT(WEEK FROM c.operation_date),
    p.working_period_id,
    p.working_period_name
ORDER BY 
    year, week_number, p.working_period_id;

-- 创建视图：按周统计破碎和故障数据（周四到周三为一周）
CREATE OR REPLACE VIEW vdata_crushing_weekly_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number,
    wk.week_start_date,
    wk.week_end_date,
    SUM(operation_time) AS total_operation_time,
    SUM(fault_time) AS total_fault_time,
    COUNT(CASE WHEN fault_time > 0 THEN 1 END) AS fault_count,
    SUM(crushing_volume) AS total_crushing_volume
FROM 
    data_crushing_operation
CROSS JOIN LATERAL get_week_thu_to_wed(operation_date) AS wk
GROUP BY 
    wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date
ORDER BY 
    year, week_number;

-- 创建视图：按周和时段统计破碎和故障数据（周四到周三为一周）
CREATE OR REPLACE VIEW vdata_crushing_weekly_period_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number,
    wk.week_start_date,
    wk.week_end_date,
    p.working_period_id,
    p.working_period_name,
    SUM(c.operation_time) AS total_operation_time,
    SUM(c.fault_time) AS total_fault_time,
    COUNT(CASE WHEN c.fault_time > 0 THEN 1 END) AS fault_count,
    SUM(c.crushing_volume) AS total_crushing_volume
FROM 
    data_crushing_operation c
CROSS JOIN LATERAL get_week_thu_to_wed(c.operation_date) AS wk
JOIN
    base_working_period p ON c.working_period_id = p.working_period_id
GROUP BY 
    wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date,
    p.working_period_id,
    p.working_period_name
ORDER BY 
    year, week_number, p.working_period_id;

-- 创建视图：按年统计破碎和故障数据（年定义为上一年12月29号到当年12月28号）
CREATE OR REPLACE VIEW vdata_crushing_yearly_stats AS
SELECT 
    fy.financial_year AS year,
    SUM(operation_time) AS total_operation_time,
    SUM(fault_time) AS total_fault_time,
    COUNT(CASE WHEN fault_time > 0 THEN 1 END) AS fault_count,
    SUM(crushing_volume) AS total_crushing_volume
FROM 
    data_crushing_operation
CROSS JOIN LATERAL get_financial_year(operation_date) AS fy
GROUP BY 
    fy.financial_year
ORDER BY 
    year;

-- 创建视图：按年份和时段统计破碎和故障数据（年定义为上一年12月29号到当年12月28号）
CREATE OR REPLACE VIEW vdata_crushing_yearly_period_stats AS
SELECT 
    fy.financial_year AS year,
    p.working_period_id,
    p.working_period_name,
    SUM(c.operation_time) AS total_operation_time,
    SUM(c.fault_time) AS total_fault_time,
    COUNT(CASE WHEN c.fault_time > 0 THEN 1 END) AS fault_count,
    SUM(c.crushing_volume) AS total_crushing_volume
FROM 
    data_crushing_operation c
CROSS JOIN LATERAL get_financial_year(c.operation_date) AS fy
JOIN
    base_working_period p ON c.working_period_id = p.working_period_id
GROUP BY 
    fy.financial_year,
    p.working_period_id,
    p.working_period_name
ORDER BY 
    year, p.working_period_id;
