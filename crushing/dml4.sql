-- 插入旋回破碎运行数据到PostgreSQL数据库
-- 生成日期: 2025-07-18 21:53:56

-- 清空现有数据（如果需要）
-- TRUNCATE TABLE data_crushing_operation CASCADE;

-- 插入旋回破碎运行数据
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-03-29', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-03-29', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-03-29', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-03-30', 120, 0, NULL, NULL, NULL, 3200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-03-30', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-03-30', 15, 0, NULL, NULL, NULL, 400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-03-31', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-03-31', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-03-31', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-04-01', 0, 0, '文明生产', NULL, NULL, 0.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-04-01', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-04-01', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-04-02', 105, 0, NULL, NULL, NULL, 2800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-04-02', 135, 0, NULL, NULL, NULL, 3600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-04-02', 75, 0, NULL, NULL, NULL, 2000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-04-03', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-04-03', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-04-03', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-04-04', 105, 0, NULL, NULL, NULL, 2800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-04-04', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-04-04', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-04-05', 105, 0, NULL, NULL, NULL, 2800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-04-05', 30, 0, NULL, NULL, NULL, 800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-04-09', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-04-09', 105, 0, NULL, NULL, NULL, 2800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-04-10', 105, 0, NULL, NULL, NULL, 2800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-04-10', 210, 0, NULL, NULL, NULL, 5600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-04-10', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-04-11', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-04-11', 300, 0, NULL, NULL, NULL, 8000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-04-11', 75, 0, NULL, NULL, NULL, 2000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-04-12', 135, 0, NULL, NULL, NULL, 3600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-04-12', 45, 0, NULL, NULL, NULL, 1200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-04-12', 105, 0, NULL, NULL, NULL, 2800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-04-13', 135, 0, NULL, NULL, NULL, 3600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-04-13', 150, 0, NULL, NULL, NULL, 4000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-04-13', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-04-14', 135, 0, NULL, NULL, NULL, 3600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-04-14', 210, 0, NULL, NULL, NULL, 5600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-04-14', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-04-15', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-04-15', 210, 0, NULL, NULL, NULL, 5600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-04-15', 105, 0, NULL, NULL, NULL, 2800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-04-16', 135, 0, NULL, NULL, NULL, 3600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-04-16', 150, 0, NULL, NULL, NULL, 4000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-04-16', 120, 0, NULL, NULL, NULL, 3200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-04-17', 135, 0, NULL, NULL, NULL, 3600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-04-17', 210, 0, NULL, NULL, NULL, 5600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-04-17', 60, 0, NULL, NULL, NULL, 1600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-04-18', 135, 0, NULL, NULL, NULL, 3600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-04-18', 165, 0, NULL, NULL, NULL, 4400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-04-18', 120, 0, NULL, NULL, NULL, 3200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-04-19', 150, 0, NULL, NULL, NULL, 4000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-04-19', 150, 0, NULL, NULL, NULL, 4000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-04-19', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-04-20', 135, 0, NULL, NULL, NULL, 3600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-04-20', 225, 0, NULL, NULL, NULL, 6000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-04-20', 105, 0, NULL, NULL, NULL, 2800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-04-21', 135, 0, NULL, NULL, NULL, 3600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-04-21', 210, 0, NULL, NULL, NULL, 5600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-04-21', 120, 0, NULL, NULL, NULL, 3200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-04-22', 165, 0, NULL, NULL, NULL, 4400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-04-22', 165, 0, NULL, NULL, NULL, 4400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-04-22', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-04-23', 210, 0, NULL, NULL, NULL, 5600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-04-23', 210, 0, NULL, NULL, NULL, 5600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-04-23', 105, 0, NULL, NULL, NULL, 2800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-04-24', 135, 0, NULL, NULL, NULL, 3600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-04-24', 165, 0, NULL, NULL, NULL, 4400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-04-24', 105, 0, NULL, NULL, NULL, 2800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-04-25', 150, 0, NULL, NULL, NULL, 4000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-04-25', 150, 0, NULL, NULL, NULL, 4000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-04-25', 120, 0, NULL, NULL, NULL, 3200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-04-26', 135, 0, NULL, NULL, NULL, 3600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-04-26', 195, 0, NULL, NULL, NULL, 5200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-04-26', 105, 0, NULL, NULL, NULL, 2800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-04-27', 105, 0, NULL, NULL, NULL, 2800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-04-27', 165, 0, NULL, NULL, NULL, 4400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-04-27', 135, 0, NULL, NULL, NULL, 3600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-04-28', 180, 0, NULL, NULL, NULL, 4800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-04-28', 90, 0, '10:50因选厂66千伏变电站停电，井下断电造成设备操作电脑故障，无法启动', NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-04-28', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');