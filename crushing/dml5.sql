-- 插入旋回破碎运行数据到PostgreSQL数据库
-- 生成日期: 2025-07-18 21:55:16

-- 清空现有数据（如果需要）
-- TRUNCATE TABLE data_crushing_operation CASCADE;

-- 插入旋回破碎运行数据
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-04-29', 135, 0, NULL, NULL, NULL, 3600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-04-29', 45, 0, NULL, NULL, NULL, 1200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-04-30', 15, 0, NULL, NULL, NULL, 400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-04-30', 285, 0, NULL, NULL, NULL, 7600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-04-30', 150, 0, NULL, NULL, NULL, 4000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-05-01', 165, 0, NULL, NULL, NULL, 4400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-05-01', 210, 0, NULL, NULL, NULL, 5600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-05-01', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-05-02', 135, 0, NULL, NULL, NULL, 3600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-05-02', 165, 0, NULL, NULL, NULL, 4400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-05-02', 105, 0, NULL, NULL, NULL, 2800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-05-03', 75, 0, NULL, NULL, NULL, 2000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-05-03', 180, 0, NULL, NULL, NULL, 4800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-05-03', 75, 0, NULL, NULL, NULL, 2000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-05-04', 150, 0, NULL, NULL, NULL, 4000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-05-04', 195, 0, NULL, NULL, NULL, 5200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-05-04', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-05-05', 150, 0, NULL, NULL, NULL, 4000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-05-05', 210, 0, NULL, NULL, NULL, 5600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-05-05', 105, 0, NULL, NULL, NULL, 2800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-05-06', 75, 0, NULL, NULL, NULL, 2000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-05-06', 180, 0, NULL, NULL, NULL, 4800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-05-06', 120, 0, NULL, NULL, NULL, 3200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-05-07', 120, 0, NULL, NULL, NULL, 3200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-05-07', 165, 0, NULL, NULL, NULL, 4400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-05-07', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-05-08', 150, 0, NULL, NULL, NULL, 4000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-05-08', 195, 0, NULL, NULL, NULL, 5200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-05-08', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-05-09', 105, 0, NULL, NULL, NULL, 2800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-05-09', 240, 0, NULL, NULL, NULL, 6400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-05-09', 120, 0, NULL, NULL, NULL, 3200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-05-10', 135, 0, NULL, NULL, NULL, 3600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-05-10', 180, 0, NULL, NULL, NULL, 4800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-05-10', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-05-11', 150, 0, NULL, NULL, NULL, 4000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-05-11', 240, 0, NULL, NULL, NULL, 6400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-05-11', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-05-12', 120, 0, NULL, NULL, NULL, 3200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-05-12', 150, 0, NULL, NULL, NULL, 4000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-05-12', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-05-13', 120, 0, NULL, NULL, NULL, 3200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-05-13', 180, 0, NULL, NULL, NULL, 4800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-05-13', 105, 0, NULL, NULL, NULL, 2800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-05-14', 75, 0, NULL, NULL, NULL, 2000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-05-14', 165, 0, NULL, NULL, NULL, 4400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-05-14', 105, 0, NULL, NULL, NULL, 2800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-05-15', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-05-15', 195, 0, NULL, NULL, NULL, 5200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-05-15', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-05-16', 165, 0, NULL, NULL, NULL, 4400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-05-16', 180, 0, NULL, NULL, NULL, 4800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-05-16', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-05-17', 180, 0, NULL, NULL, NULL, 4800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-05-17', 210, 0, NULL, NULL, NULL, 5600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-05-17', 120, 0, NULL, NULL, NULL, 3200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-05-18', 120, 0, NULL, NULL, NULL, 3200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-05-18', 135, 0, NULL, NULL, NULL, 3600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-05-18', 120, 0, NULL, NULL, NULL, 3200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-05-19', 135, 0, NULL, NULL, NULL, 3600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-05-19', 150, 0, NULL, NULL, NULL, 4000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-05-19', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-05-20', 120, 0, NULL, NULL, NULL, 3200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-05-20', 180, 0, NULL, NULL, NULL, 4800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-05-20', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-05-21', 105, 0, NULL, NULL, NULL, 2800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-05-21', 135, 0, NULL, NULL, NULL, 3600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-05-21', 90, 0, NULL, NULL, NULL, 2400.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-05-22', 120, 0, NULL, NULL, NULL, 3200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-05-22', 180, 0, NULL, NULL, NULL, 4800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-05-22', 105, 0, NULL, NULL, NULL, 2800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-05-23', 195, 0, NULL, NULL, NULL, 5200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-05-23', 180, 0, NULL, NULL, NULL, 4800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-05-23', 75, 0, NULL, NULL, NULL, 2000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-05-24', 135, 0, NULL, NULL, NULL, 3600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-05-24', 150, 0, NULL, NULL, NULL, 4000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-05-24', 135, 0, NULL, NULL, NULL, 3600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-05-25', 105, 0, NULL, NULL, NULL, 2800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-05-25', 180, 0, NULL, NULL, NULL, 4800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-05-25', 105, 0, NULL, NULL, NULL, 2800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-05-26', 150, 0, NULL, NULL, NULL, 4000.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-05-26', 195, 0, NULL, NULL, NULL, 5200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-05-26', 105, 0, NULL, NULL, NULL, 2800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-05-27', 120, 0, NULL, NULL, NULL, 3200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-05-27', 210, 0, NULL, NULL, NULL, 5600.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-05-27', 120, 0, NULL, NULL, NULL, 3200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(1, '2025-05-28', 120, 0, NULL, NULL, NULL, 3200.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(2, '2025-05-28', 180, 0, NULL, NULL, NULL, 4800.0, 'admin', 'admin');
INSERT INTO data_crushing_operation 
(working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, update_by)
VALUES
(3, '2025-05-28', 120, 0, NULL, NULL, NULL, 3200.0, 'admin', 'admin');