import json

# Define the directory structure based on the images
raw_data = [
    # Image 1
    ["采厂业务", "生产计划", "01采矿计划", "整体计划"],
    ["采厂业务", "生产计划", "01采矿计划", "掘进计划-整体"],
    ["采厂业务", "生产计划", "01采矿计划", "掘进计划-每日"],
    ["采厂业务", "生产计划", "01采矿计划", "支护数据-整体"],
    ["采厂业务", "生产计划", "01采矿计划", "支护数据-每日"],
    ["采厂业务", "生产计划", "01采矿计划", "潜孔数据-整体"],
    ["采厂业务", "生产计划", "01采矿计划", "潜孔计划-每日"],
    ["采厂业务", "生产计划", "01采矿计划", "中深孔计划-整体"],
    ["采厂业务", "生产计划", "01采矿计划", "中深孔计划-每日"],
    ["采厂业务", "生产计划", "01采矿计划", "采场出矿计划-整体"],
    ["采厂业务", "生产计划", "01采矿计划", "采场出矿计划-每日"],
    ["采厂业务", "生产计划", "选矿计划", "计划-选矿厂生产"],
    ["采厂业务", "生产计划", "选矿计划", "计划-销售情况"],
    ["采厂业务", "生产计划", "03基础数据", "项目部"],
    
    # Image 2
    ["采厂业务", "生产监控", "原矿产量", "数据分析-计划增长率"],
    ["采厂业务", "生产监控", "原矿产量", "数据分析-计划计划完成度"],
    ["采厂业务", "生产监控", "原矿产量", "数据分析-计采效率"],
    ["采厂业务", "生产监控", "原矿产量", "数据分析-采量预估1"],
    ["采厂业务", "生产监控", "原矿产量", "数据分析-采量预估2"],
    ["采厂业务", "生产监控", "凿岩", "巷道掘进-计划与实际"],
    ["采厂业务", "生产监控", "凿岩", "重点工程-计划完成率"],
    ["采厂业务", "生产监控", "爆破", "潜孔数据-计划与实际"],
    ["采厂业务", "生产监控", "爆破", "潜孔数据-计划与实际-按项目部"],
    ["采厂业务", "生产监控", "爆破", "中深孔-计划与实际"],
    ["采厂业务", "生产监控", "爆破", "中深孔-计划与实际-按项目部"],
    ["采厂业务", "生产监控", "爆破", "锚网-计划与实际"],
    ["采厂业务", "生产监控", "爆破", "锚网-计划与实际-按项目部"],
    ["采厂业务", "生产监控", "爆破", "喷浆-计划与实际"],
    ["采厂业务", "生产监控", "爆破", "喷浆-计划与实际-按项目部"],
    
    # Image 3
    ["采厂业务", "生产监控", "01矿运", "采厂出矿-整体"],
    ["采厂业务", "生产监控", "01矿运", "采厂出矿-按采场"],
    ["采厂业务", "生产监控", "01矿运", "采厂出矿-按项目部"],
    ["采厂业务", "生产监控", "01矿运", "数据管理"],
    ["采厂业务", "生产监控", "02运输", "溜井放矿-总量"],
    ["采厂业务", "生产监控", "02运输", "溜井放矿-按项目部"],
    ["采厂业务", "生产监控", "02运输", "溜井放矿-按作业分时"],
    ["采厂业务", "生产监控", "02运输", "溜井放矿-按溜井编号"],
    ["采厂业务", "生产监控", "02运输", "数据管理-运输量"],
    
    # Image 4
    ["采厂业务", "生产监控", "03破碎", "破碎量-整体"],
    ["采厂业务", "生产监控", "03破碎", "破碎量-按分时作业"],
    ["采厂业务", "生产监控", "03破碎", "运行与故障处理时间"],
    ["采厂业务", "生产监控", "03破碎", "运行与故障处理-按分时"],
    ["采厂业务", "生产监控", "03破碎", "数据管理（每日）"],
    ["采厂业务", "生产监控", "04提升", "提升量-整体"],
    ["采厂业务", "生产监控", "04提升", "提升量-作业分时"],
    ["采厂业务", "生产监控", "04提升", "运行时间-整体"],
    ["采厂业务", "生产监控", "04提升", "故障处理-作业分时"],
    ["采厂业务", "生产监控", "04提升", "数据分析-运行率"],
    ["采厂业务", "生产监控", "04提升", "数据管理（每日）"],
    ["采厂业务", "生产监控", "04提升", "提升斗数"]
]

# Remove duplicates by converting to tuples and using set
data = []
seen = set()
for row in raw_data:
    row_tuple = tuple(row)
    if row_tuple not in seen:
        seen.add(row_tuple)
        data.append(row)

# Sort the data for better organization
data.sort(key=lambda x: (x[0], x[1], x[2], x[3]))

# Create a nested JSON structure
def create_nested_json():
    result = {}
    
    for row in data:
        level1, level2, level3, level4 = row
        
        # Initialize level1 if not exists
        if level1 not in result:
            result[level1] = {}
            
        # Initialize level2 if not exists
        if level2 not in result[level1]:
            result[level1][level2] = {}
            
        # Initialize level3 if not exists
        if level3 not in result[level1][level2]:
            result[level1][level2][level3] = []
            
        # Add level4 item
        if level4:  # Only add non-empty values
            result[level1][level2][level3].append(level4)
    
    return result

# Create the nested JSON structure
json_structure = create_nested_json()

# Write to JSON file with proper formatting and UTF-8 encoding
with open('c:\\Users\\<USER>\\doc\\5\\目录结构.json', 'w', encoding='utf-8') as f:
    json.dump(json_structure, f, ensure_ascii=False, indent=2)

print("JSON file created successfully at c:\\Users\\<USER>\\doc\\5\\目录结构.json")
print(f"Total items: {len(data)}")

# Print the JSON structure to console for preview
print("\nJSON Structure Preview:")
print(json.dumps(json_structure, ensure_ascii=False, indent=2)[:1000] + "...\n(preview truncated)")
