import json
import pandas as pd
import plotly.figure_factory as ff
from datetime import datetime, timedelta
import os

# Read the JSON file
with open('c:\\Users\\<USER>\\doc\\5\\目录结构.json', 'r', encoding='utf-8') as f:
    json_data = json.load(f)

# Convert nested JSON back to flat structure while preserving order
flat_data = []

# Process the JSON structure
for level1, level1_data in json_data.items():
    for level2, level2_data in level1_data.items():
        for level3, level3_items in level2_data.items():
            for level4 in level3_items:
                flat_data.append([level1, level2, level3, level4])

# Calculate total hours and workdays needed
total_tasks = len(flat_data)
hours_per_task = 3  # 3 hours per task
total_hours_frontend = total_tasks * hours_per_task
total_hours_backend = total_tasks * hours_per_task

# Calculate workdays needed (8 hours per day)
workdays_frontend = total_hours_frontend / 8
workdays_backend = total_hours_backend / 8

# Round up to nearest half day
import math
workdays_frontend = math.ceil(workdays_frontend * 2) / 2
workdays_backend = math.ceil(workdays_backend * 2) / 2

print(f"Total frontend hours: {total_hours_frontend} hours ({workdays_frontend} workdays)")
print(f"Total backend hours: {total_hours_backend} hours ({workdays_backend} workdays)")

# Start dates - July 6th, 2025
start_date = datetime(2025, 7, 6)

# Prepare data for Gantt chart
gantt_data = []

# Track the current date for frontend and backend
current_date_frontend = start_date
current_date_backend = start_date

# Create task data for Gantt chart
for i, row_data in enumerate(flat_data):
    level1, level2, level3, level4 = row_data
    
    # Task name
    task_name = f"{level1} > {level2} > {level3} > {level4}"
    
    # Calculate task position and completion time
    tasks_before = i
    hours_before_frontend = tasks_before * hours_per_task
    hours_before_backend = tasks_before * hours_per_task
    
    # Calculate days and hours for this task
    days_before_frontend = hours_before_frontend // 8
    hours_remainder_frontend = hours_before_frontend % 8
    
    days_before_backend = hours_before_backend // 8
    hours_remainder_backend = hours_before_backend % 8
    
    # For sequential tasks, we need to calculate exact start and end times
    if i == 0:
        # First task starts at the beginning
        frontend_start_date = start_date
        backend_start_date = start_date - timedelta(days=1)
    else:
        # Subsequent tasks start immediately after the previous task ends
        prev_task_end_frontend = gantt_data[2*(i-1)]['Finish']
        prev_task_end_backend = gantt_data[2*(i-1)+1]['Finish']
        frontend_start_date = prev_task_end_frontend
        backend_start_date = prev_task_end_backend
    
    # Calculate end dates (start + task duration)
    frontend_end_date = frontend_start_date + timedelta(hours=hours_per_task)
    backend_end_date = backend_start_date + timedelta(hours=hours_per_task)
    
    # Add frontend task
    gantt_data.append(dict(
        Task=task_name,
        Start=frontend_start_date,
        Finish=frontend_end_date,
        Resource='前端'
    ))
    
    # Add backend task
    gantt_data.append(dict(
        Task=task_name,
        Start=backend_start_date,
        Finish=backend_end_date,
        Resource='后端'
    ))

# Create the Gantt chart
colors = {'前端': 'rgb(46, 137, 205)', 
          '后端': 'rgb(114, 44, 121)'}

fig = ff.create_gantt(gantt_data, 
                     colors=colors, 
                     index_col='Resource', 
                     title='项目甘特图',
                     show_colorbar=True, 
                     group_tasks=True,
                     showgrid_x=True,
                     showgrid_y=True)

# Update layout for full screen width
fig.update_layout(
    autosize=True,
    width=None,  # Let it adjust to screen width
    height=800,
    margin=dict(l=20, r=20, b=100, t=100, pad=4),
    font=dict(size=12),
)

# Add custom HTML to make it responsive
html_template = '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目甘特图</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body, html {{margin: 0; padding: 0; width: 100%; height: 100%;}}
        .container {{width: 100%; height: 100%;}}
        .plotly-graph-div {{width: 100% !important; height: 100% !important;}}
    </style>
</head>
<body>
    <div class="container">
        {plot_div}
    </div>
    <script>
        document.addEventListener("DOMContentLoaded", function() {{
            window.addEventListener('resize', function() {{
                Plotly.relayout(document.getElementsByClassName('plotly-graph-div')[0], {{
                    width: window.innerWidth,
                    height: window.innerHeight
                }});
            }});
        }});
    </script>
</body>
</html>
'''

# Get the plot div
plot_div = fig.to_html(full_html=False, include_plotlyjs=False)

# Save the Gantt chart as HTML with custom template
output_file = 'c:\\Users\\<USER>\\doc\\5\\目录结构_甘特图.html'

# Use our custom HTML template
with open(output_file, 'w', encoding='utf-8') as f:
    f.write(html_template.format(plot_div=plot_div))

print(f"Gantt chart created successfully at {output_file}")
