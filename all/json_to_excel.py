import json
import pandas as pd
from openpyxl.styles import PatternFill, Font, Alignment, Border, Side
from openpyxl.utils import get_column_letter
from openpyxl.utils.dataframe import dataframe_to_rows
from datetime import datetime, timedelta

# Read the JSON file
with open('c:\\Users\\<USER>\\doc\\5\\目录结构.json', 'r', encoding='utf-8') as f:
    json_data = json.load(f)

# Convert nested JSON back to flat structure while preserving order
flat_data = []

# Process the JSON structure
for level1, level1_data in json_data.items():
    for level2, level2_data in level1_data.items():
        for level3, level3_items in level2_data.items():
            for level4 in level3_items:
                flat_data.append([level1, level2, level3, level4])

# Create a DataFrame with just the directory structure data
df = pd.DataFrame(flat_data, columns=["一级目录", "二级目录", "三级目录", "四级目录"])

# Create Excel file
output_file = 'c:\\Users\\<USER>\\doc\\5\\目录结构_从JSON生成_新版.xlsx'
writer = pd.ExcelWriter(output_file, engine='openpyxl')

# Create a new workbook and worksheet manually
workbook = writer.book
worksheet = workbook.create_sheet('目录结构')

# Add headers manually
headers = ["一级目录", "二级目录", "三级目录", "四级目录", "后端时间", "前端时间", "后端完成时间", "前端完成时间"]
for col_idx, header in enumerate(headers, 1):
    worksheet.cell(row=1, column=col_idx, value=header)

# Set column widths
worksheet.column_dimensions['A'].width = 15
worksheet.column_dimensions['B'].width = 15
worksheet.column_dimensions['C'].width = 20
worksheet.column_dimensions['D'].width = 30
worksheet.column_dimensions['E'].width = 15
worksheet.column_dimensions['F'].width = 15
worksheet.column_dimensions['G'].width = 15
worksheet.column_dimensions['H'].width = 15

# Define styles
header_fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
header_font = Font(bold=True, color="FFFFFF")
header_alignment = Alignment(horizontal='center', vertical='center')
thin_border = Border(left=Side(style='thin'), 
                     right=Side(style='thin'), 
                     top=Side(style='thin'), 
                     bottom=Side(style='thin'))

# Apply styles to header
for col in range(1, 9):  # A to H
    cell = worksheet.cell(row=1, column=col)
    cell.fill = header_fill
    cell.font = header_font
    cell.alignment = header_alignment
    cell.border = thin_border

# Add data to worksheet with cell merging
row_idx = 2  # Start from row 2 (after header)

# Track the last values and their starting rows for merging
last_level1 = None
last_level2 = None
last_level3 = None
level1_start = 2
level2_start = 2
level3_start = 2

# Calculate total hours and workdays needed
total_tasks = len(flat_data)
total_hours_frontend = total_tasks * 3  # 3 hours per task
total_hours_backend = total_tasks * 3   # 3 hours per task

# Calculate workdays needed (8 hours per day)
workdays_frontend = total_hours_frontend / 8
workdays_backend = total_hours_backend / 8

# Round up to nearest half day
import math
workdays_frontend = math.ceil(workdays_frontend * 2) / 2
workdays_backend = math.ceil(workdays_backend * 2) / 2

print(f"Total frontend hours: {total_hours_frontend} hours ({workdays_frontend} workdays)")
print(f"Total backend hours: {total_hours_backend} hours ({workdays_backend} workdays)")

# Start dates - July 6th, 2025
start_date = datetime(2025, 7, 6)  # Start from July 6th, 2025
backend_start_delay = 6  # Backend starts 6 days after frontend (to finish 1 day before frontend)

# Add data and apply borders
for i, row_data in enumerate(flat_data):
    level1, level2, level3, level4 = row_data
    
    # Set standard hours for each task (3 hours)
    frontend_hours = 3
    backend_hours = 3
    
    # Calculate task position and completion time
    tasks_before = i
    hours_before_frontend = tasks_before * 3
    hours_before_backend = tasks_before * 3
    
    # Calculate days and hours for this task
    days_before_frontend = hours_before_frontend // 8
    hours_remainder_frontend = hours_before_frontend % 8
    
    days_before_backend = hours_before_backend // 8
    hours_remainder_backend = hours_before_backend % 8
    
    # Calculate completion time for this specific task
    frontend_complete_hours = hours_remainder_frontend + 4  # Add task hours
    backend_complete_hours = hours_remainder_backend + 4    # Add task hours
    
    # If hours exceed workday, add another day
    if frontend_complete_hours > 8:
        days_before_frontend += 1
        frontend_complete_hours -= 8
    
    if backend_complete_hours > 8:
        days_before_backend += 1
        backend_complete_hours -= 8
    
    # Calculate completion dates
    frontend_complete_date = (start_date + timedelta(days=days_before_frontend))
    
    # Backend should complete one day before frontend
    backend_complete_date = (frontend_complete_date - timedelta(days=1))
    
    # Format dates without hours
    frontend_complete_str = frontend_complete_date.strftime('%Y-%m-%d')
    backend_complete_str = backend_complete_date.strftime('%Y-%m-%d')
    
    # Write data to cells
    worksheet.cell(row=row_idx, column=1, value=level1)
    worksheet.cell(row=row_idx, column=2, value=level2)
    worksheet.cell(row=row_idx, column=3, value=level3)
    worksheet.cell(row=row_idx, column=4, value=level4)
    
    # Write time data
    worksheet.cell(row=row_idx, column=5, value=backend_hours)
    worksheet.cell(row=row_idx, column=6, value=frontend_hours)
    worksheet.cell(row=row_idx, column=7, value=backend_complete_str)
    worksheet.cell(row=row_idx, column=8, value=frontend_complete_str)
    
    # Apply borders to all cells in this row
    for col in range(1, 9):  # A to H
        cell = worksheet.cell(row=row_idx, column=col)
        cell.border = thin_border
        cell.alignment = Alignment(vertical='center')
    
    # Check if we need to merge cells from previous rows
    if last_level1 != level1 and last_level1 is not None:
        if level1_start < row_idx - 1:  # Only merge if there are at least 2 rows
            worksheet.merge_cells(start_row=level1_start, start_column=1, 
                                end_row=row_idx-1, end_column=1)
        level1_start = row_idx
        level2_start = row_idx  # Reset level2 start
        level3_start = row_idx  # Reset level3 start
    elif last_level2 != level2 and last_level2 is not None:
        if level2_start < row_idx - 1:  # Only merge if there are at least 2 rows
            worksheet.merge_cells(start_row=level2_start, start_column=2, 
                                end_row=row_idx-1, end_column=2)
        level2_start = row_idx
        level3_start = row_idx  # Reset level3 start
    elif last_level3 != level3 and last_level3 is not None:
        if level3_start < row_idx - 1:  # Only merge if there are at least 2 rows
            worksheet.merge_cells(start_row=level3_start, start_column=3, 
                                end_row=row_idx-1, end_column=3)
        level3_start = row_idx
    
    # Update last values
    last_level1 = level1
    last_level2 = level2
    last_level3 = level3
    
    row_idx += 1

# Handle the last group of rows that need merging
if level1_start < row_idx - 1:
    worksheet.merge_cells(start_row=level1_start, start_column=1, 
                        end_row=row_idx-1, end_column=1)
if level2_start < row_idx - 1:
    worksheet.merge_cells(start_row=level2_start, start_column=2, 
                        end_row=row_idx-1, end_column=2)
if level3_start < row_idx - 1:
    worksheet.merge_cells(start_row=level3_start, start_column=3, 
                        end_row=row_idx-1, end_column=3)

# Save the workbook
writer.close()

print(f"Excel file created successfully at {output_file}")
print(f"Total items: {len(flat_data)}")
print("Added merged cells for identical directory entries")
print("Added 4 additional columns: 前端时间, 后端时间, 前端完成时间, 后端完成时间")
