create table public.data_filtration
(
    id                                                 serial
        primary key,
    report_date                                        date not null
        constraint data_filtration_pk
            unique,
    concentrate_filter_press_iron_ore_yield_with_water numeric(10, 2),
    concentrate_filter_press_iron_ore_yield_dry        numeric(10, 2),
    iron_concentrate_sample_test_indicators_total_iron numeric(10, 2),
    iron_concentrate_sample_test_indicators_water      numeric(10, 2),
    tailings                                           numeric(10, 2),
    operating_time                                     numeric(8, 2),
    belt_18_tailings_yield_with_water                  numeric(10, 2),
    belt_18_tailings_yield_dry                         numeric(10, 2),
    belt_18_tailings_test_indicators_total_iron        numeric(10, 2),
    belt_18_tailings_test_indicators_magnetic_iron     numeric(10, 2),
    belt_18_tailings_test_indicators_water             numeric(10, 2),
    belt_19_tailings_yield_with_water                  numeric(10, 2),
    belt_19_tailings_yield_dry                         numeric(10, 2),
    belt_19_tailings_test_indicators_total_iron        numeric(10, 2),
    belt_19_tailings_test_indicators_magnetic_iron     numeric(10, 2),
    belt_19_tailings_test_indicators_water             numeric(10, 2),
    tailings_200_mesh                                  numeric(10, 2),
    create_time                                        timestamp(6) default now(),
    update_time                                        timestamp(6) default now(),
    create_by                                          varchar(64),
    update_by                                          varchar(64),
    filling_yield                                      numeric(10, 2)
);

comment on table public.data_filtration is '过滤';

comment on column public.data_filtration.report_date is '日期';

comment on column public.data_filtration.concentrate_filter_press_iron_ore_yield_with_water is '精矿过滤机/铁精矿产量（20#皮带）/t-本日（含水）';

comment on column public.data_filtration.concentrate_filter_press_iron_ore_yield_dry is '精矿过滤机/铁精矿产量（20#皮带）/t-本日（干矿）';

comment on column public.data_filtration.iron_concentrate_sample_test_indicators_total_iron is '铁精矿（20#皮带样品）检测指标/%-全铁（TFe）';

comment on column public.data_filtration.iron_concentrate_sample_test_indicators_water is '铁精矿（20#皮带样品）检测指标/%-水份';

comment on column public.data_filtration.tailings is '尾矿量';

comment on column public.data_filtration.operating_time is '运行时间';

comment on column public.data_filtration.belt_18_tailings_yield_with_water is '18#皮带尾砂量/t-本日（含水）';

comment on column public.data_filtration.belt_18_tailings_yield_dry is '18#皮带尾砂量/t-本日（干矿）';

comment on column public.data_filtration.belt_18_tailings_test_indicators_total_iron is '18#皮带尾砂检测指标/%-全铁（TFe）';

comment on column public.data_filtration.belt_18_tailings_test_indicators_magnetic_iron is '18#皮带尾砂检测指标/%-磁性铁（MFe）';

comment on column public.data_filtration.belt_18_tailings_test_indicators_water is '18#皮带尾砂检测指标/%-水份';

comment on column public.data_filtration.belt_19_tailings_yield_with_water is '19#皮带尾矿量/t-本日（含水）';

comment on column public.data_filtration.belt_19_tailings_yield_dry is '19#皮带尾矿量/t-本日（干矿）';

comment on column public.data_filtration.belt_19_tailings_test_indicators_total_iron is '19#皮带尾矿检测指标/%-全铁（TFe）';

comment on column public.data_filtration.belt_19_tailings_test_indicators_magnetic_iron is '19#皮带尾矿检测指标/%-磁性铁（MFe）';

comment on column public.data_filtration.belt_19_tailings_test_indicators_water is '19#皮带尾矿检测指标/%-水份';

comment on column public.data_filtration.tailings_200_mesh is '尾矿-200目（%）';

comment on column public.data_filtration.filling_yield is '充填量';

alter table public.data_filtration
    owner to postgres;

