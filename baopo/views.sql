-- 爆破统计视图

-- 按日总体统计
CREATE OR REPLACE VIEW vdata_blasting_daily_total_stats AS
SELECT 
    operation_date,
    COUNT(*) as total_records,
    SUM(blasting_volume) as total_blasting_volume,
    AVG(blasting_impact) as avg_blasting_impact,
    SUM(residual_ore_recovery) as total_residual_ore_recovery,
    COUNT(DISTINCT blasting_time) as distinct_blast_times
FROM data_blasting
GROUP BY operation_date
ORDER BY operation_date;

-- 按日详细统计
CREATE OR REPLACE VIEW vdata_blasting_daily_stats AS
SELECT 
    operation_date,
    project_department_id,
    pd.project_department_name,
    stope_id,
    s.stope_name,
    COUNT(*) as record_count,
    SUM(blasting_volume) as total_blasting_volume,
    AVG(blasting_impact) as avg_blasting_impact,
    SUM(residual_ore_recovery) as total_residual_ore_recovery,
    COUNT(DISTINCT blasting_time) as distinct_blast_times,
    MIN(blasting_time) as earliest_blast_time,
    MAX(blasting_time) as latest_blast_time
FROM data_blasting b
LEFT JOIN base_project_department pd ON b.project_department_id = pd.project_department_id
LEFT JOIN base_stope s ON b.stope_id = s.stope_id
GROUP BY operation_date, project_department_id, pd.project_department_name, stope_id, s.stope_name
ORDER BY operation_date, project_department_id, stope_id;

-- 按日按部门统计
CREATE OR REPLACE VIEW vdata_blasting_daily_department_stats AS
SELECT 
    operation_date,
    project_department_id,
    pd.project_department_name,
    COUNT(*) as record_count,
    SUM(blasting_volume) as total_blasting_volume,
    AVG(blasting_impact) as avg_blasting_impact,
    SUM(residual_ore_recovery) as total_residual_ore_recovery,
    COUNT(DISTINCT blasting_time) as distinct_blast_times
FROM data_blasting b
LEFT JOIN base_project_department pd ON b.project_department_id = pd.project_department_id
GROUP BY operation_date, project_department_id, pd.project_department_name
ORDER BY operation_date, project_department_id;

-- 按日按采场统计
CREATE OR REPLACE VIEW vdata_blasting_daily_stope_stats AS
SELECT 
    operation_date,
    stope_id,
    s.stope_name,
    COUNT(*) as record_count,
    COUNT(CASE WHEN is_recycled = TRUE THEN 1 END) as recycled_count,
    COUNT(CASE WHEN is_recycled = FALSE THEN 1 END) as not_recycled_count,
    AVG(territory_peak_parameter) as avg_territory_peak_parameter,
    SUM(territory_count) as total_territory_count
FROM data_blasting b
LEFT JOIN base_stope s ON b.stope_id = s.stope_id
GROUP BY operation_date, stope_id, s.stope_name
ORDER BY operation_date, stope_id;

-- 按周总体统计
CREATE OR REPLACE VIEW vdata_blasting_weekly_total_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number,
    wk.week_start_date,
    wk.week_end_date,
    COUNT(*) as total_records,
    COUNT(CASE WHEN is_recycled = TRUE THEN 1 END) as recycled_count,
    COUNT(CASE WHEN is_recycled = FALSE THEN 1 END) as not_recycled_count,
    AVG(territory_peak_parameter) as avg_territory_peak_parameter,
    SUM(territory_count) as total_territory_count
FROM data_blasting
CROSS JOIN LATERAL get_week_thu_to_wed(operation_date) AS wk
GROUP BY wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date
ORDER BY year, week_number;

-- 按周详细统计
CREATE OR REPLACE VIEW vdata_blasting_weekly_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number,
    wk.week_start_date,
    wk.week_end_date,
    project_department_id,
    pd.project_department_name,
    stope_id,
    s.stope_name,
    COUNT(*) as record_count,
    COUNT(CASE WHEN is_recycled = TRUE THEN 1 END) as recycled_count,
    COUNT(CASE WHEN is_recycled = FALSE THEN 1 END) as not_recycled_count,
    AVG(territory_peak_parameter) as avg_territory_peak_parameter,
    SUM(territory_count) as total_territory_count
FROM data_blasting b
LEFT JOIN base_project_department pd ON b.project_department_id = pd.project_department_id
LEFT JOIN base_stope s ON b.stope_id = s.stope_id
CROSS JOIN LATERAL get_week_thu_to_wed(operation_date) AS wk
GROUP BY wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date, 
         project_department_id, pd.project_department_name, stope_id, s.stope_name
ORDER BY year, week_number, project_department_id, stope_id;

-- 按周按部门统计
CREATE OR REPLACE VIEW vdata_blasting_weekly_department_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number,
    wk.week_start_date,
    wk.week_end_date,
    project_department_id,
    pd.project_department_name,
    COUNT(*) as record_count,
    COUNT(CASE WHEN is_recycled = TRUE THEN 1 END) as recycled_count,
    COUNT(CASE WHEN is_recycled = FALSE THEN 1 END) as not_recycled_count,
    AVG(territory_peak_parameter) as avg_territory_peak_parameter,
    SUM(territory_count) as total_territory_count
FROM data_blasting b
LEFT JOIN base_project_department pd ON b.project_department_id = pd.project_department_id
CROSS JOIN LATERAL get_week_thu_to_wed(operation_date) AS wk
GROUP BY wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date, 
         project_department_id, pd.project_department_name
ORDER BY year, week_number, project_department_id;

-- 按周按采场统计
CREATE OR REPLACE VIEW vdata_blasting_weekly_stope_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number,
    wk.week_start_date,
    wk.week_end_date,
    stope_id,
    s.stope_name,
    COUNT(*) as record_count,
    COUNT(CASE WHEN is_recycled = TRUE THEN 1 END) as recycled_count,
    COUNT(CASE WHEN is_recycled = FALSE THEN 1 END) as not_recycled_count,
    AVG(territory_peak_parameter) as avg_territory_peak_parameter,
    SUM(territory_count) as total_territory_count
FROM data_blasting b
LEFT JOIN base_stope s ON b.stope_id = s.stope_id
CROSS JOIN LATERAL get_week_thu_to_wed(operation_date) AS wk
GROUP BY wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date, 
         stope_id, s.stope_name
ORDER BY year, week_number, stope_id;

-- 按月总体统计
CREATE OR REPLACE VIEW vdata_blasting_monthly_total_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    COUNT(*) as total_records,
    COUNT(CASE WHEN is_recycled = TRUE THEN 1 END) as recycled_count,
    COUNT(CASE WHEN is_recycled = FALSE THEN 1 END) as not_recycled_count,
    AVG(territory_peak_parameter) as avg_territory_peak_parameter,
    SUM(territory_count) as total_territory_count
FROM data_blasting
CROSS JOIN LATERAL get_financial_month(operation_date) AS fm
GROUP BY fm.financial_year, fm.financial_month
ORDER BY year, month;

-- 按月详细统计
CREATE OR REPLACE VIEW vdata_blasting_monthly_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    project_department_id,
    pd.project_department_name,
    stope_id,
    s.stope_name,
    COUNT(*) as record_count,
    COUNT(CASE WHEN is_recycled = TRUE THEN 1 END) as recycled_count,
    COUNT(CASE WHEN is_recycled = FALSE THEN 1 END) as not_recycled_count,
    AVG(territory_peak_parameter) as avg_territory_peak_parameter,
    SUM(territory_count) as total_territory_count
FROM data_blasting b
LEFT JOIN base_project_department pd ON b.project_department_id = pd.project_department_id
LEFT JOIN base_stope s ON b.stope_id = s.stope_id
CROSS JOIN LATERAL get_financial_month(operation_date) AS fm
GROUP BY fm.financial_year, fm.financial_month, 
         project_department_id, pd.project_department_name, stope_id, s.stope_name
ORDER BY year, month, project_department_id, stope_id;

-- 按月按部门统计
CREATE OR REPLACE VIEW vdata_blasting_monthly_department_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    project_department_id,
    pd.project_department_name,
    COUNT(*) as record_count,
    COUNT(CASE WHEN is_recycled = TRUE THEN 1 END) as recycled_count,
    COUNT(CASE WHEN is_recycled = FALSE THEN 1 END) as not_recycled_count,
    AVG(territory_peak_parameter) as avg_territory_peak_parameter,
    SUM(territory_count) as total_territory_count
FROM data_blasting b
LEFT JOIN base_project_department pd ON b.project_department_id = pd.project_department_id
CROSS JOIN LATERAL get_financial_month(operation_date) AS fm
GROUP BY fm.financial_year, fm.financial_month, 
         project_department_id, pd.project_department_name
ORDER BY year, month, project_department_id;

-- 按月按采场统计
CREATE OR REPLACE VIEW vdata_blasting_monthly_stope_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    stope_id,
    s.stope_name,
    COUNT(*) as record_count,
    COUNT(CASE WHEN is_recycled = TRUE THEN 1 END) as recycled_count,
    COUNT(CASE WHEN is_recycled = FALSE THEN 1 END) as not_recycled_count,
    AVG(territory_peak_parameter) as avg_territory_peak_parameter,
    SUM(territory_count) as total_territory_count
FROM data_blasting b
LEFT JOIN base_stope s ON b.stope_id = s.stope_id
CROSS JOIN LATERAL get_financial_month(operation_date) AS fm
GROUP BY fm.financial_year, fm.financial_month, stope_id, s.stope_name
ORDER BY year, month, stope_id;

-- 按年总体统计
CREATE OR REPLACE VIEW vdata_blasting_yearly_total_stats AS
SELECT 
    fy.financial_year AS year,
    COUNT(*) as total_records,
    COUNT(CASE WHEN is_recycled = TRUE THEN 1 END) as recycled_count,
    COUNT(CASE WHEN is_recycled = FALSE THEN 1 END) as not_recycled_count,
    AVG(territory_peak_parameter) as avg_territory_peak_parameter,
    SUM(territory_count) as total_territory_count
FROM data_blasting
CROSS JOIN LATERAL get_financial_year(operation_date) AS fy
GROUP BY fy.financial_year
ORDER BY year;

-- 按年详细统计
CREATE OR REPLACE VIEW vdata_blasting_yearly_stats AS
SELECT 
    fy.financial_year AS year,
    project_department_id,
    pd.project_department_name,
    stope_id,
    s.stope_name,
    COUNT(*) as record_count,
    COUNT(CASE WHEN is_recycled = TRUE THEN 1 END) as recycled_count,
    COUNT(CASE WHEN is_recycled = FALSE THEN 1 END) as not_recycled_count,
    AVG(territory_peak_parameter) as avg_territory_peak_parameter,
    SUM(territory_count) as total_territory_count
FROM data_blasting b
LEFT JOIN base_project_department pd ON b.project_department_id = pd.project_department_id
LEFT JOIN base_stope s ON b.stope_id = s.stope_id
CROSS JOIN LATERAL get_financial_year(operation_date) AS fy
GROUP BY fy.financial_year, 
         project_department_id, pd.project_department_name, stope_id, s.stope_name
ORDER BY year, project_department_id, stope_id;

-- 按年按部门统计
CREATE OR REPLACE VIEW vdata_blasting_yearly_department_stats AS
SELECT 
    fy.financial_year AS year,
    project_department_id,
    pd.project_department_name,
    COUNT(*) as record_count,
    COUNT(CASE WHEN is_recycled = TRUE THEN 1 END) as recycled_count,
    COUNT(CASE WHEN is_recycled = FALSE THEN 1 END) as not_recycled_count,
    AVG(territory_peak_parameter) as avg_territory_peak_parameter,
    SUM(territory_count) as total_territory_count
FROM data_blasting b
LEFT JOIN base_project_department pd ON b.project_department_id = pd.project_department_id
CROSS JOIN LATERAL get_financial_year(operation_date) AS fy
GROUP BY fy.financial_year, project_department_id, pd.project_department_name
ORDER BY year, project_department_id;

-- 按年按采场统计
CREATE OR REPLACE VIEW vdata_blasting_yearly_stope_stats AS
SELECT 
    fy.financial_year AS year,
    stope_id,
    s.stope_name,
    COUNT(*) as record_count,
    COUNT(CASE WHEN is_recycled = TRUE THEN 1 END) as recycled_count,
    COUNT(CASE WHEN is_recycled = FALSE THEN 1 END) as not_recycled_count,
    AVG(territory_peak_parameter) as avg_territory_peak_parameter,
    SUM(territory_count) as total_territory_count
FROM data_blasting b
LEFT JOIN base_stope s ON b.stope_id = s.stope_id
CROSS JOIN LATERAL get_financial_year(operation_date) AS fy
GROUP BY fy.financial_year, stope_id, s.stope_name
ORDER BY year, stope_id;

-- 按爆破物类型统计视图
CREATE OR REPLACE VIEW vdata_blasting_explosive_type_stats AS
SELECT 
    explosive_material,
    COUNT(*) as usage_count,
    SUM(blasting_volume) as total_blasting_volume,
    AVG(blasting_impact) as avg_blasting_impact,
    SUM(residual_ore_recovery) as total_residual_ore_recovery,
    COUNT(DISTINCT blasting_time) as distinct_blast_times
FROM data_blasting
WHERE explosive_material IS NOT NULL
GROUP BY explosive_material
ORDER BY usage_count DESC;

-- 按爆破时间段统计视图
CREATE OR REPLACE VIEW vdata_blasting_time_period_stats AS
SELECT 
    operation_date,
    CASE 
        WHEN blasting_time BETWEEN '00:00:00' AND '06:59:59' THEN '早班(00:00-06:59)'
        WHEN blasting_time BETWEEN '07:00:00' AND '15:59:59' THEN '白班(07:00-15:59)'
        WHEN blasting_time BETWEEN '16:00:00' AND '23:59:59' THEN '夜班(16:00-23:59)'
        ELSE '未知时段'
    END as time_period,
    COUNT(*) as blast_count,
    SUM(blasting_volume) as total_blasting_volume,
    AVG(blasting_impact) as avg_blasting_impact,
    SUM(residual_ore_recovery) as total_residual_ore_recovery
FROM data_blasting
WHERE blasting_time IS NOT NULL
GROUP BY operation_date, 
    CASE 
        WHEN blasting_time BETWEEN '00:00:00' AND '06:59:59' THEN '早班(00:00-06:59)'
        WHEN blasting_time BETWEEN '07:00:00' AND '15:59:59' THEN '白班(07:00-15:59)'
        WHEN blasting_time BETWEEN '16:00:00' AND '23:59:59' THEN '夜班(16:00-23:59)'
        ELSE '未知时段'
    END
ORDER BY operation_date, time_period;

-- 爆破效率分析视图
CREATE OR REPLACE VIEW vdata_blasting_efficiency_analysis AS
SELECT 
    operation_date,
    project_department_id,
    pd.project_department_name,
    stope_id,
    s.stope_name,
    explosive_material,
    blasting_volume,
    blasting_impact,
    residual_ore_recovery,
    CASE 
        WHEN blasting_volume > 0 THEN ROUND(blasting_impact / blasting_volume, 4)
        ELSE NULL
    END as impact_per_volume_ratio,
    CASE 
        WHEN blasting_volume > 0 THEN ROUND(residual_ore_recovery / blasting_volume, 4)
        ELSE NULL
    END as recovery_per_volume_ratio
FROM data_blasting b
LEFT JOIN base_project_department pd ON b.project_department_id = pd.project_department_id
LEFT JOIN base_stope s ON b.stope_id = s.stope_id
WHERE blasting_volume IS NOT NULL AND blasting_volume > 0
ORDER BY operation_date DESC;
