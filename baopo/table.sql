-- 爆破统计表
DROP TABLE IF EXISTS data_blasting;

CREATE TABLE data_blasting (
    id BIGSERIAL PRIMARY KEY,
    operation_date DATE NOT NULL,
    project_department_id BIGINT,
    stope_id BIGINT,
    blasting_volume NUMERIC(10,3),
    blasting_impact VARCHAR(128),
    explosive_material VARCHAR(128),
    residual_ore_recovery VARCHAR(128),
    blasting_time TIMESTAMP,
    operator_name <PERSON><PERSON><PERSON><PERSON>(64),
    remarks VARCHAR(255),
    create_by VA<PERSON><PERSON><PERSON>(64),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(64),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 添加字段注释
COMMENT ON TABLE data_blasting IS '爆破施工数据表';
COMMENT ON COLUMN data_blasting.id IS '主键ID';
COMMENT ON COLUMN data_blasting.operation_date IS '业务时间/作业日期';
COMMENT ON COLUMN data_blasting.project_department_id IS '项目部门ID';
COMMENT ON COLUMN data_blasting.stope_id IS '采场ID';
COMMENT ON COLUMN data_blasting.blasting_volume IS '爆破量(m³)';
COMMENT ON COLUMN data_blasting.blasting_impact IS '爆破影响';
COMMENT ON COLUMN data_blasting.explosive_material IS '爆破物';
COMMENT ON COLUMN data_blasting.residual_ore_recovery IS '残矿回收(t)';
COMMENT ON COLUMN data_blasting.blasting_time IS '爆破时间';
COMMENT ON COLUMN data_blasting.operator_name IS '经办人';
COMMENT ON COLUMN data_blasting.remarks IS '备注';
COMMENT ON COLUMN data_blasting.create_by IS '创建人';
COMMENT ON COLUMN data_blasting.create_time IS '创建时间';
COMMENT ON COLUMN data_blasting.update_by IS '更新人';
COMMENT ON COLUMN data_blasting.update_time IS '更新时间';

-- 添加索引
CREATE INDEX idx_blasting_date ON data_blasting(operation_date);
CREATE INDEX idx_blasting_department ON data_blasting(project_department_id);
CREATE INDEX idx_blasting_stope ON data_blasting(stope_id);
CREATE INDEX idx_blasting_operator ON data_blasting(operator_name);
CREATE INDEX idx_blasting_explosive ON data_blasting(explosive_material);
CREATE INDEX idx_blasting_time ON data_blasting(blasting_time);

-- 添加更新时间触发器
CREATE OR REPLACE FUNCTION update_blasting_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_blasting_timestamp
    BEFORE UPDATE ON data_blasting
    FOR EACH ROW
    EXECUTE FUNCTION update_blasting_timestamp();
