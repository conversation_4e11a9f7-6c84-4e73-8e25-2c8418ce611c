-- 查找在tunneling_data_insert.sql中出现但不在base_stope表中的stope_name
-- 使用VALUES子句创建临时表来对比

WITH extracted_stopes AS (
    SELECT stope_name FROM (VALUES 
        ('-1020m水平6-3溜井硐室'),
        ('-1060m/-1100原矿仓溜井'),
        ('-1060m水平4-3溜井硐室'),
        ('-1060m水平6-4溜井硐室'),
        ('-1080m/-1123m
斜坡道'),
        ('-1080m/-1128m斜坡道'),
        ('-1100m破碎硐室'),
        ('-1100m破碎站配电硐室'),
        ('-1100m破碎配电硐室'),
        ('-1100m胶带斜井联巷'),
        ('-1128尾轮硐室'),
        ('-1128尾轮硐室联络巷'),
        ('-1277m水平沉淀池、水仓'),
        ('-1277m沉淀池、水仓'),
        ('-840m/-765m斜坡道'),
        ('-840m中段石门巷
（副井侧）'),
        ('-840m副井侧倒段溜井'),
        ('-840m副井侧倒段溜井
（-840m/-960m）'),
        ('-840m副井侧石门巷'),
        ('-840m副井石门巷'),
        ('-840m副井进风井联巷'),
        ('-840m措施井0#穿'),
        ('-840m措施井0号穿'),
        ('-840m措施井0号穿倒车硐室'),
        ('-840m措施井侧0#穿'),
        ('-840m措施井侧2#穿'),
        ('-840m措施井侧倒段溜井'),
        ('-840m措施井石门巷'),
        ('-840m措施井石门巷
（往2号穿）'),
        ('-840m进风井联巷'),
        ('-840至865斜坡道'),
        ('-865m辅助运输巷'),
        ('-960m/-1020m通风天井（6#穿）'),
        ('-960m/-840m斜坡道'),
        ('-960m/-840m斜坡道
(通-840m中段)'),
        ('-960m/-840m斜坡道倒渣井'),
        ('-960m/-840m斜坡道通风井'),
        ('-960m集中高段溜井联巷'),
        ('0#-15-3切割巷'),
        ('0#-15-3矿块'),
        ('0#-15-7凿岩巷（计划外）'),
        ('0#-15探矿巷'),
        ('0#-15探矿巷（计划外）'),
        ('0#-19探矿巷'),
        ('0#-1探矿巷'),
        ('0#-2-1充填巷'),
        ('0#-2-1凿岩巷'),
        ('0#-2-1号充填巷'),
        ('0#-2-1矿块'),
        ('0#-2-2充填巷'),
        ('0#-2-2号充填巷'),
        ('0#-2-3充填巷'),
        ('0#-2-3凿岩巷'),
        ('0#-2-3号充填巷'),
        ('0#-2-5凿岩巷'),
        ('0#-2-5矿块'),
        ('0#-2-7凿岩巷'),
        ('0#-2-7矿块（计划外）'),
        ('0#-2-9凿岩巷'),
        ('0#-2-9凿岩巷（计划外）'),
        ('0#-2-9矿块'),
        ('0#-20-1充填巷'),
        ('0#-20-1凿岩巷'),
        ('0#-20-2号充填巷'),
        ('0#-20-7凿岩巷'),
        ('0#-20-7矿块'),
        ('0#-26-1充填巷'),
        ('0#-26-1号充填巷'),
        ('0#-26-2凿岩巷（风机硐室）'),
        ('0#-26-2号充填巷'),
        ('0#-26-3凿岩巷'),
        ('0#-26-3号充填巷'),
        ('0#-26-3矿块'),
        ('0#-26-7凿岩巷'),
        ('0#-26-7矿块'),
        ('0#-2凿岩联巷'),
        ('0#-2采场联巷'),
        ('0#-5-4凿岩巷'),
        ('0#-5-4矿块'),
        ('0#-5-8凿岩巷'),
        ('0#-5-8矿块'),
        ('0#-5探矿巷'),
        ('0#穿下盘沿脉探矿巷'),
        ('0#穿改'),
        ('0#穿改副井侧'),
        ('0#穿改探矿巷（0#-15区域）'),
        ('0#穿改探矿巷（0#-5区域）'),
        ('0-15-1充填巷（计划外）'),
        ('0-15-1（计划外）'),
        ('0-15-2充填巷'),
        ('0-15-2号充填巷'),
        ('0-15-2（计划外）'),
        ('0-15-3矿块'),
        ('0-15-7凿岩巷'),
        ('0-1探矿巷'),
        ('0-1溜井'),
        ('0-2-3矿块'),
        ('0-2-5矿块'),
        ('0-20-1矿块（计划外'),
        ('0-26-1充填巷（计划外）'),
        ('0-26-1矿块（计划外）'),
        ('0-26空压机硐室'),
        ('0-3溜井'),
        ('0-5-4凿岩巷（计划外'),
        ('0-5-4矿块（计划外）'),
        ('1060皮带道硐室'),
        ('1100m胶带斜井联络巷'),
        ('2#-12-11凿岩巷'),
        ('2#-12-11矿块'),
        ('2#-12-1矿块'),
        ('2#-12-2号充填巷'),
        ('2#-12-3凿岩巷'),
        ('2#-12-3号充填巷'),
        ('2#-12-3矿块'),
        ('2#-12-5矿块'),
        ('2#-12-7凿岩巷'),
        ('2#-12-7矿块'),
        ('2#-12-9矿块'),
        ('2#-2-11凿岩巷'),
        ('2#-2-11矿块'),
        ('2#-2-11矿块
（出矿巷）'),
        ('2#-2-1充填巷'),
        ('2#-2-1凿岩巷'),
        ('2#-2-1号充填巷'),
        ('2#-2-1矿块'),
        ('2#-2-2充填巷'),
        ('2#-2-2号充填巷'),
        ('2#-2-2矿块'),
        ('2#-2-3充填巷'),
        ('2#-2-3凿岩巷'),
        ('2#-2-3号充填巷'),
        ('2#-2-3号充填巷（计划外）'),
        ('2#-2-3矿块'),
        ('2#-2-4矿块'),
        ('2#-2-7凿岩巷'),
        ('2#-2-7号充填巷'),
        ('2#-2-7矿块'),
        ('2#-2-7矿块
（出矿巷）'),
        ('2#-20-11出矿巷'),
        ('2#-20-11凿岩巷（计划外）'),
        ('2#-20-11矿块'),
        ('2#-20-12凿岩巷'),
        ('2#-20-1号充填巷'),
        ('2#-20-2号充填巷'),
        ('2#-20-3凿岩巷'),
        ('2#-20-3号充填巷'),
        ('2#-20-6探矿硐室'),
        ('2#-20-9出矿巷'),
        ('2#-20-9凿岩巷'),
        ('2#-20-9矿块'),
        ('2#-20采场联巷'),
        ('2#-22-11出矿巷'),
        ('2#-22-11凿岩巷'),
        ('2#-22-11矿块'),
        ('2#-22-12凿岩巷'),
        ('2#-22-1号充填巷'),
        ('2#-22-2号充填巷'),
        ('2#-22-3号充填巷'),
        ('2#-22-3矿块'),
        ('2#-22-6探矿硐'),
        ('2#-22-9出矿巷'),
        ('2#-22-9矿块'),
        ('2#-22-9矿块（计划外）'),
        ('2#-22采场联巷'),
        ('2#-4-5凿岩巷'),
        ('2#-4-5矿块'),
        ('2#-4充填联巷'),
        ('2#-4出矿联巷'),
        ('2#-5-3号充填巷'),
        ('2#-5-3矿块'),
        ('2#-5-7凿岩巷'),
        ('2#-5-7矿块'),
        ('2#-5-9凿岩巷'),
        ('2#-5-9矿块（计划外）'),
        ('2#主井皮带道'),
        ('2#主井皮带道硐室'),
        ('2#主井粉矿回收巷'),
        ('2#措施溜井联巷'),
        ('2-12-11矿块（计划外）'),
        ('2-2-11凿岩巷'),
        ('2-2-11矿块（计划外）'),
        ('2-20-11矿块（计划外'),
        ('2-22-11矿块（计划外）'),
        ('2-22-3充填巷'),
        ('2-4采场联巷（计划外）'),
        ('2-7溜井'),
        ('4#穿'),
        ('4#穿溜井操作硐室'),
        ('4#穿高段溜井及操作硐室'),
        ('4-1溜井'),
        ('4-1溜井卸矿硐室'),
        ('4-1溜井振动放矿机硐室'),
        ('4-2溜井'),
        ('4-4溜井硐室'),
        ('6#穿'),
        ('6#穿上盘侧'),
        ('6#穿副井侧'),
        ('6#穿探矿'),
        ('6#穿探矿巷'),
        ('6#穿措施井侧'),
        ('6#穿脉'),
        ('6-1溜井'),
        ('6-3溜井硐室'),
        ('6-4溜井'),
        ('960m中转成品矿仓联巷'),
        ('960m胶带斜井头部硐室联巷'),
        ('960胶带斜井'),
        ('下盘沿脉至0#改'),
        ('下盘沿脉（往0#穿改）'),
        ('下盘沿脉（往6#穿）'),
        ('中转成品矿仓联巷'),
        ('倒段溜井联巷（计划外）'),
        ('回风井联巷'),
        ('回风井联巷（计划外）'),
        ('回风井联络巷（计划外）'),
        ('回风巷'),
        ('措施井车场'),
        ('无轨维修其他硐室'),
        ('无轨维修硐室'),
        ('无轨维修硐室联巷'),
        ('有轨维修硐室'),
        ('混凝土搅拌硐室'),
        ('混凝土搅拌硐室联巷'),
        ('混凝土搅拌站
其他硐室'),
        ('混凝土搅拌站其他硐室'),
        ('胶带斜井'),
        ('胶带斜井及联巷'),
        ('胶带斜井头部硐室联巷'),
        ('胶带斜井尾部硐室联巷'),
        ('胶带斜井联巷'),
        ('进风井联巷'),
        ('进风井（计划外）'),
        ('通风井联巷（计划外）')
    ) AS t(stope_name)
)
SELECT 
    es.stope_name AS missing_stope_name
FROM extracted_stopes es
LEFT JOIN public.base_stope bs ON es.stope_name = bs.stope_name
WHERE bs.stope_name IS NULL
ORDER BY es.stope_name;

-- 统计信息
-- SELECT 
--     COUNT(*) AS total_extracted_stopes,
--     COUNT(bs.stope_name) AS existing_in_base_stope,
--     COUNT(*) - COUNT(bs.stope_name) AS missing_stopes
-- FROM extracted_stopes es
-- LEFT JOIN public.base_stope bs ON es.stope_name = bs.stope_name;
