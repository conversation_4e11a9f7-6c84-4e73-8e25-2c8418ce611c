import json

with open('extracted_names.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

stope_names = data['stope_names']

# 生成 SQL 查询
sql_values = ',\n    '.join([f"('{name.replace(\"'\", \"''\")})" for name in stope_names])

query = f"""
WITH extracted_stopes AS (
  SELECT stope_name FROM (VALUES
    {sql_values}
  ) AS t(stope_name)
)
SELECT es.stope_name
FROM extracted_stopes es
WHERE NOT EXISTS (
  SELECT 1 FROM base_stope bs 
  WHERE bs.stope_name = es.stope_name
);
"""

print(query)