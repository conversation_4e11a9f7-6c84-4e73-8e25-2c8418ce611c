import pandas as pd
import os

# 定义文件路径列表
file_paths = [
    '/Users/<USER>/Documents/moonlighting/lxbi-data/统计/2025年1月生产及建设统计表.xlsx',
    '/Users/<USER>/Documents/moonlighting/lxbi-data/统计//2025年2月生产及建设统计表.xlsx',
    '/Users/<USER>/Documents/moonlighting/lxbi-data/统计//2025年3月份生产及建设统计表.xlsx',
    '/Users/<USER>/Documents/moonlighting/lxbi-data/统计//2025年4月生产及建设统计表.xlsx',
    '/Users/<USER>/Documents/moonlighting/lxbi-data/统计//2025年5月份生产及建设统计表.xlsx',
    '/Users/<USER>/Documents/moonlighting/lxbi-data/统计//2025年6月份生产及建设统计表.xlsx',
    '/Users/<USER>/Documents/moonlighting/lxbi-data/统计//2025年7月份生产及建设统计表.xlsx'
]

# 遍历文件路径列表
for file_path in file_paths:
    # 读取 Excel 文件
    excel_file = pd.ExcelFile(file_path)

    # 获取所有表名
    sheet_names = excel_file.sheet_names
    sheet_names
    for sheet_name in sheet_names:
        # 获取当前工作表的数据
        df = excel_file.parse(sheet_name)

        # 构建输出文件路径
        file_name = os.path.splitext(os.path.basename(file_path))[0]
        output_path = f'/Users/<USER>/Documents/moonlighting/lxbi-data/掘进/{file_name}_{sheet_name}.csv'

        # 将数据保存为 CSV 文件
        df.to_csv(output_path, index=False)