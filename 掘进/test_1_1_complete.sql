-- 1月1日完整掘进数据插入SQL
-- 生成时间: 2025-07-30 21:50:27

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-960m中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '6#穿'),
    1,
    NULL,
    '出渣',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-960m中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '6#穿'),
    2,
    NULL,
    '凿岩',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-960m中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '6#穿'),
    3,
    NULL,
    '待装药',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-960m中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-4充填联巷'),
    1,
    NULL,
    '凿岩',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-960m中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-4充填联巷'),
    2,
    3.2,
    '爆破3.2m',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-960m中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-4充填联巷'),
    3,
    NULL,
    '出渣',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-960m中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-5-3号充填巷'),
    1,
    NULL,
    '出渣',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-960m中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-5-3号充填巷'),
    2,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-960m中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-5-3号充填巷'),
    3,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-960m中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '混凝土搅拌硐室'),
    1,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-960m中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '混凝土搅拌硐室'),
    2,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-960m中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '混凝土搅拌硐室'),
    3,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-960m中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '混凝土搅拌硐室联巷'),
    1,
    NULL,
    '出渣',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-960m中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '混凝土搅拌硐室联巷'),
    2,
    NULL,
    '爆破修炮',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-960m中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '混凝土搅拌硐室联巷'),
    3,
    NULL,
    '出渣凿岩',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-960m中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '混凝土搅拌站
其他硐室'),
    1,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-960m中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '混凝土搅拌站
其他硐室'),
    2,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-960m中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '混凝土搅拌站
其他硐室'),
    3,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-992m中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '6#穿'),
    1,
    3.2,
    '爆破3.2m',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-992m中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '6#穿'),
    2,
    NULL,
    '出渣',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-992m中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '6#穿'),
    3,
    NULL,
    '凿岩',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-992m中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#-5-4凿岩巷'),
    1,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-992m中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#-5-4凿岩巷'),
    2,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-992m中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#-5-4凿岩巷'),
    3,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-992m中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#-5-8凿岩巷'),
    1,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-992m中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#-5-8凿岩巷'),
    2,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-992m中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#-5-8凿岩巷'),
    3,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#穿下盘沿脉探矿巷'),
    1,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#穿下盘沿脉探矿巷'),
    2,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#穿下盘沿脉探矿巷'),
    3,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#-15-3矿块'),
    1,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#-15-3矿块'),
    2,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#-15-3矿块'),
    3,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#-5-4凿岩巷'),
    1,
    NULL,
    '出渣',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#-5-4凿岩巷'),
    2,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#-5-4凿岩巷'),
    3,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#-5-8凿岩巷'),
    1,
    3.2,
    '爆破3.2m',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#-5-8凿岩巷'),
    2,
    NULL,
    '出渣',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#-5-8凿岩巷'),
    3,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-2-7矿块
（出矿巷）'),
    1,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-2-7矿块
（出矿巷）'),
    2,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-2-7矿块
（出矿巷）'),
    3,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-2-11矿块
（出矿巷）'),
    1,
    NULL,
    '凿岩',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-2-11矿块
（出矿巷）'),
    2,
    NULL,
    '凿岩',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-2-11矿块
（出矿巷）'),
    3,
    3.2,
    '爆破3.2m',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-4出矿联巷'),
    1,
    NULL,
    '凿岩',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-4出矿联巷'),
    2,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-4出矿联巷'),
    3,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1060中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '6#穿上盘侧'),
    1,
    NULL,
    '出渣',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1060中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '6#穿上盘侧'),
    2,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1060中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '6#穿上盘侧'),
    3,
    NULL,
    '凿岩',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1060中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '6#穿措施井侧'),
    1,
    NULL,
    '出渣',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1060中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '6#穿措施井侧'),
    2,
    NULL,
    '凿岩',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1060中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '6#穿措施井侧'),
    3,
    NULL,
    '凿岩',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '溜井工程'),
    (SELECT id FROM public.base_stope WHERE stope_name = '-1060m水平4-3溜井硐室'),
    1,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '溜井工程'),
    (SELECT id FROM public.base_stope WHERE stope_name = '-1060m水平4-3溜井硐室'),
    2,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '溜井工程'),
    (SELECT id FROM public.base_stope WHERE stope_name = '-1060m水平4-3溜井硐室'),
    3,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '溜井工程'),
    (SELECT id FROM public.base_stope WHERE stope_name = '-1020m水平6-3溜井硐室'),
    1,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '溜井工程'),
    (SELECT id FROM public.base_stope WHERE stope_name = '-1020m水平6-3溜井硐室'),
    2,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '溜井工程'),
    (SELECT id FROM public.base_stope WHERE stope_name = '-1020m水平6-3溜井硐室'),
    3,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '上采区工程'),
    (SELECT id FROM public.base_stope WHERE stope_name = '-960m/-840m斜坡道
(通-840m中段)'),
    1,
    NULL,
    '凿岩',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '上采区工程'),
    (SELECT id FROM public.base_stope WHERE stope_name = '-960m/-840m斜坡道
(通-840m中段)'),
    2,
    3.2,
    '爆破3.2m',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '上采区工程'),
    (SELECT id FROM public.base_stope WHERE stope_name = '-960m/-840m斜坡道
(通-840m中段)'),
    3,
    NULL,
    '出渣',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '上采区工程'),
    (SELECT id FROM public.base_stope WHERE stope_name = '胶带斜井尾部硐室联巷'),
    1,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '上采区工程'),
    (SELECT id FROM public.base_stope WHERE stope_name = '胶带斜井尾部硐室联巷'),
    2,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '上采区工程'),
    (SELECT id FROM public.base_stope WHERE stope_name = '胶带斜井尾部硐室联巷'),
    3,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '上采区工程'),
    (SELECT id FROM public.base_stope WHERE stope_name = '-1080m/-1123m
斜坡道'),
    1,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '上采区工程'),
    (SELECT id FROM public.base_stope WHERE stope_name = '-1080m/-1123m
斜坡道'),
    2,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '上采区工程'),
    (SELECT id FROM public.base_stope WHERE stope_name = '-1080m/-1123m
斜坡道'),
    3,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '上采区工程'),
    (SELECT id FROM public.base_stope WHERE stope_name = '-1100m破碎配电硐室'),
    1,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '上采区工程'),
    (SELECT id FROM public.base_stope WHERE stope_name = '-1100m破碎配电硐室'),
    2,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    2,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '上采区工程'),
    (SELECT id FROM public.base_stope WHERE stope_name = '-1100m破碎配电硐室'),
    3,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-960中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '6#穿脉'),
    1,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-960中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '6#穿脉'),
    2,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-960中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '6#穿脉'),
    3,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-960中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-22-3号充填巷'),
    1,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-960中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-22-3号充填巷'),
    2,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-960中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-22-3号充填巷'),
    3,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-960中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-20-2号充填巷'),
    1,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-960中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-20-2号充填巷'),
    2,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-960中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-20-2号充填巷'),
    3,
    NULL,
    '凿岩',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-960中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-20-3号充填巷'),
    1,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-960中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-20-3号充填巷'),
    2,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-960中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-20-3号充填巷'),
    3,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-960中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-20-1号充填巷'),
    1,
    NULL,
    '凿岩',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-960中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-20-1号充填巷'),
    2,
    3.2,
    '爆破3.2m',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-960中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-20-1号充填巷'),
    3,
    NULL,
    '出渣',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-992中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-20采场联巷'),
    1,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-992中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-20采场联巷'),
    2,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-992中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-20采场联巷'),
    3,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-992中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-20-9凿岩巷'),
    1,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-992中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-20-9凿岩巷'),
    2,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-992中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-20-9凿岩巷'),
    3,
    NULL,
    '凿岩',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-992中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#-2采场联巷'),
    1,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-992中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#-2采场联巷'),
    2,
    3.2,
    '凿岩爆破3.2m',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-992中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#-2采场联巷'),
    3,
    NULL,
    '出渣',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-992中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#-2-9凿岩巷'),
    1,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-992中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#-2-9凿岩巷'),
    2,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-992中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#-2-9凿岩巷'),
    3,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-992中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-20-11凿岩巷（计划外）'),
    1,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-992中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-20-11凿岩巷（计划外）'),
    2,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-992中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-20-11凿岩巷（计划外）'),
    3,
    NULL,
    '凿岩',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-992中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2-2-11矿块（计划外）'),
    1,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-992中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2-2-11矿块（计划外）'),
    2,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-992中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2-2-11矿块（计划外）'),
    3,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#-2-9矿块'),
    1,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#-2-9矿块'),
    2,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#-2-9矿块'),
    3,
    NULL,
    '凿岩',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#-2-5矿块'),
    1,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#-2-5矿块'),
    2,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#-2-5矿块'),
    3,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#-2-1矿块'),
    1,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#-2-1矿块'),
    2,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#-2-1矿块'),
    3,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-20-9矿块'),
    1,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-20-9矿块'),
    2,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-20-9矿块'),
    3,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '无轨维修硐室'),
    1,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '无轨维修硐室'),
    2,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '无轨维修硐室'),
    3,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '无轨维修硐室联巷'),
    1,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '无轨维修硐室联巷'),
    2,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '无轨维修硐室联巷'),
    3,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '无轨维修其他硐室'),
    1,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '无轨维修其他硐室'),
    2,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '无轨维修其他硐室'),
    3,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-22-9矿块（计划外）'),
    1,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-22-9矿块（计划外）'),
    2,
    3.2,
    '凿岩爆破3.2m',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '2#-22-9矿块（计划外）'),
    3,
    NULL,
    '出渣',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#-2-7矿块（计划外）'),
    1,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#-2-7矿块（计划外）'),
    2,
    NULL,
    '未施工',
    'admin'
);

INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '2025-01-01',
    3,
    (SELECT id FROM public.base_working_face WHERE working_face_name = '-1020中段'),
    (SELECT id FROM public.base_stope WHERE stope_name = '0#-2-7矿块（计划外）'),
    3,
    NULL,
    '未施工',
    'admin'
);

