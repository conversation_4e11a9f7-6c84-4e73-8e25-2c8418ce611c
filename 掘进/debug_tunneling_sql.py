import pandas as pd
import os
import re
from datetime import datetime

def extract_date_from_filename(filename):
    """从文件名中提取日期"""
    # 处理 12.29, 12.30, 12.31 格式
    if '12.29' in filename:
        return '2024-12-29'
    elif '12.30' in filename:
        return '2024-12-30'
    elif '12.31' in filename:
        return '2024-12-31'

    # 处理月日格式
    match = re.search(r'(\d{1,2})月(\d{1,2})日', filename)
    if match:
        month = int(match.group(1))
        day = int(match.group(2))
        return f'2025-{month:02d}-{day:02d}'

    return None

def extract_number(text):
    """从文本中提取数字"""
    if pd.isna(text) or text == '':
        return None

    # 转换为字符串并提取数字
    text_str = str(text)
    match = re.search(r'(\d+\.?\d*)', text_str)
    if match:
        return float(match.group(1))
    return None

def get_project_department_id(unit):
    """根据单位获取项目部门ID"""
    unit_mapping = {
        '中矿': 2,
        '涟邵': 3,
        '中煤': 4
    }
    return unit_mapping.get(unit)

def is_summary_row(unit, working_face, stope):
    """判断是否为汇总行（小计、合计、总计等）"""
    summary_keywords = ['小计', '合计', '总计', '汇总']

    # 检查各个字段是否包含汇总关键词
    for keyword in summary_keywords:
        if (unit and keyword in str(unit)) or \
           (working_face and keyword in str(working_face)) or \
           (stope and keyword in str(stope)):
            return True
    return False

def debug_generate_sql_for_csv(csv_file_path):
    """调试版本：为单个CSV文件生成SQL"""
    try:
        # 读取CSV文件
        df = pd.read_csv(csv_file_path)

        # 提取日期
        filename = os.path.basename(csv_file_path)
        operation_date = extract_date_from_filename(filename)
        if not operation_date:
            print(f"无法从文件名提取日期: {filename}")
            return []

        sql_statements = []
        current_unit = None
        current_working_face = None

        print(f"调试文件: {filename}")
        print(f"总行数: {len(df)}")

        # 遍历每一行数据
        for index, row in df.iterrows():
            unit = row.iloc[0]  # 单位
            working_face = row.iloc[1]  # 中段
            stope = row.iloc[2]  # 位置

            print(f"\n行 {index}: 单位='{unit}', 中段='{working_face}', 位置='{stope}'")

            # 跳过标题行
            if index < 4:
                print("  -> 跳过标题行")
                continue

            # 如果遇到"总计"行，停止处理
            if not pd.isna(unit) and '总计' in str(unit):
                print("  -> 遇到总计行，停止处理")
                break

            # 更新当前单位
            if not pd.isna(unit) and unit != '' and unit not in ['单位', '']:
                project_dept_id = get_project_department_id(unit)
                if project_dept_id:
                    current_unit = unit
                    print(f"  -> 更新当前单位: {current_unit}")
                    continue
                else:
                    print(f"  -> 未识别的单位: {unit}")

            # 更新当前工作面（中段）
            if not pd.isna(working_face) and working_face != '':
                # 检查是否为汇总行
                if is_summary_row(None, working_face, None):
                    print(f"  -> 跳过汇总行 (中段): {working_face}")
                    continue
                current_working_face = working_face
                print(f"  -> 更新当前工作面: {current_working_face}")
                continue

            # 处理位置行的数据
            if not pd.isna(stope) and stope != '':
                # 检查是否为汇总行
                if is_summary_row(None, None, stope):
                    print(f"  -> 跳过汇总行 (位置): {stope}")
                    continue

                # 必须有当前单位和工作面
                if not current_unit or not current_working_face:
                    print(f"  -> 缺少当前单位或工作面: 单位={current_unit}, 工作面={current_working_face}")
                    continue

                # 获取项目部门ID
                project_department_id = get_project_department_id(current_unit)
                if not project_department_id:
                    print(f"  -> 无法获取项目部门ID: {current_unit}")
                    continue

                print(f"  -> 处理位置数据: {stope}")
                print(f"      当前单位: {current_unit} (ID: {project_department_id})")
                print(f"      当前工作面: {current_working_face}")

                # 获取三个时段的数据
                periods = [
                    {'period_id': 1, 'col_idx': 11, 'name': '0-8时'},      # 0-8时
                    {'period_id': 2, 'col_idx': 12, 'name': '8至20时'},    # 8至20时
                    {'period_id': 3, 'col_idx': 13, 'name': '20时至24时'} # 20时至24时
                ]

                for period in periods:
                    try:
                        # 获取掘进长度和工作内容
                        tunneling_length = None
                        work_content = None

                        if period['col_idx'] < len(row):
                            cell_value = row.iloc[period['col_idx']]
                            if not pd.isna(cell_value) and cell_value != '':
                                work_content = str(cell_value).strip()
                                print(f"        {period['name']}: '{work_content}'")
                                # 如果是"未施工"或类似内容，不提取长度
                                if work_content not in ['未施工', '0', '']:
                                    tunneling_length = extract_number(cell_value)
                                    print(f"          提取长度: {tunneling_length}")

                        # 只有当有内容或长度时才生成SQL
                        if work_content and work_content not in ['未施工', '0', '']:
                            print(f"          生成SQL语句")
                            sql = f"""INSERT INTO public.data_tunneling (
    operation_date, 
    project_department_id, 
    working_face_id, 
    stope_id, 
    working_period_id, 
    tunneling_length, 
    work_content,
    create_by
) VALUES (
    '{operation_date}',
    {project_department_id},
    (SELECT id FROM public.base_working_face WHERE working_face_name = '{current_working_face}'),
    (SELECT id FROM public.base_stope WHERE stope_name = '{stope}'),
    {period['period_id']},
    {tunneling_length if tunneling_length else 'NULL'},
    '{work_content}',
    'admin'
);"""
                            sql_statements.append(sql)
                        else:
                            print(f"          跳过 (无内容或未施工)")

                    except Exception as e:
                        print(f"        处理时段 {period['name']} 时出错: {e}")
                        continue
            else:
                print("  -> 跳过空行或无位置数据")

    except Exception as e:
        print(f"处理文件 {csv_file_path} 时出错: {e}")
        return []

    return sql_statements

def main():
    """主函数"""
    # 测试单个文件
    test_file = '/Users/<USER>/Documents/moonlighting/lxbi-data/掘进/2025年1月生产及建设统计表_1月1日.csv'

    sql_statements = debug_generate_sql_for_csv(test_file)
    print(f"\n最终生成了 {len(sql_statements)} 条SQL语句")

if __name__ == "__main__":
    main()
