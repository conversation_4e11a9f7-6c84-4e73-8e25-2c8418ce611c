#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Generate INSERT SQL for base_stope table from extracted pairs
"""

import json
from datetime import datetime

def generate_insert_sql():
    """Generate INSERT SQL for base_stope table"""
    
    # working_face_name to working_face_id mapping
    working_face_mapping = {
        '其他': 3,
        '溜井工程': 7,
        '上采区工程': 8,
        '-992m': 5,
        '-960m': 4,
        '-992m/-960m': 12,
        '-1020m/-992m': 9,
        '-1020m': 2,
        '-1060m': 6,
        '-960m/1020m': 18,
        '-1020m/-1060m': 19,
        '-960m/992m': 27,
        '-922m': 23,
        '-960m/-992m': 25,
        '-992m/1020m': 30
    }
    
    # Load extracted pairs
    try:
        with open('extracted_pairs.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        pairs = data['pairs']
    except Exception as e:
        print(f"Error loading pairs data: {e}")
        return
    
    # Generate SQL
    sql_lines = []
    sql_lines.append("-- 插入base_stope表的SQL脚本")
    sql_lines.append("-- 生成时间: " + datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    sql_lines.append("")
    
    # Process each pair
    processed_pairs = []
    skipped_pairs = []
    
    for stope_name, working_face_name in pairs:
        # Clean up stope_name (remove newlines and extra spaces)
        clean_stope_name = stope_name.replace('\n', '').strip()
        
        # Get working_face_id
        working_face_id = working_face_mapping.get(working_face_name)
        
        if working_face_id is None:
            skipped_pairs.append((clean_stope_name, working_face_name, "未找到对应的working_face_id"))
            continue
        
        processed_pairs.append((clean_stope_name, working_face_id, working_face_name))
    
    # Generate INSERT statements with conflict handling
    sql_lines.append("-- 使用INSERT ... ON CONFLICT DO NOTHING 避免重复插入")
    sql_lines.append("INSERT INTO public.base_stope (")
    sql_lines.append("    stope_name,")
    sql_lines.append("    working_face_id,")
    sql_lines.append("    status,")
    sql_lines.append("    create_by,")
    sql_lines.append("    create_time,")
    sql_lines.append("    update_by,")
    sql_lines.append("    is_delete")
    sql_lines.append(") VALUES")
    
    # Add each record
    for i, (stope_name, working_face_id, working_face_name) in enumerate(processed_pairs):
        # Escape single quotes in stope_name
        escaped_stope_name = stope_name.replace("'", "''")
        
        comma = "," if i < len(processed_pairs) - 1 else ""
        sql_lines.append(f"    ('{escaped_stope_name}', {working_face_id}, 1, 'admin', NOW(), 'admin', 0){comma}")
        
        # Add comment for readability
        if i % 10 == 9 or i == len(processed_pairs) - 1:
            sql_lines.append(f"    -- 已处理 {i + 1} 条记录")
    
    sql_lines.append("ON CONFLICT (stope_name, working_face_id) DO NOTHING;")
    sql_lines.append("")
    
    # Add summary
    sql_lines.append("-- 处理总结:")
    sql_lines.append(f"-- 总配对数量: {len(pairs)}")
    sql_lines.append(f"-- 成功处理: {len(processed_pairs)}")
    sql_lines.append(f"-- 跳过数量: {len(skipped_pairs)}")
    sql_lines.append("")
    
    if skipped_pairs:
        sql_lines.append("-- 跳过的记录:")
        for stope_name, working_face_name, reason in skipped_pairs:
            sql_lines.append(f"-- '{stope_name}' + '{working_face_name}' - {reason}")
        sql_lines.append("")
    
    # Add verification query
    sql_lines.append("-- 验证插入结果:")
    sql_lines.append("-- SELECT COUNT(*) as inserted_count FROM public.base_stope WHERE create_by = 'admin';")
    sql_lines.append("")
    sql_lines.append("-- 查看插入的记录:")
    sql_lines.append("-- SELECT bs.stope_name, bwf.working_face_name, bs.create_time")
    sql_lines.append("-- FROM public.base_stope bs")
    sql_lines.append("-- JOIN public.base_working_face bwf ON bs.working_face_id = bwf.id")
    sql_lines.append("-- WHERE bs.create_by = 'admin'")
    sql_lines.append("-- ORDER BY bs.create_time DESC;")
    
    return '\n'.join(sql_lines)

def main():
    print("正在生成base_stope插入SQL...")
    
    sql_content = generate_insert_sql()
    
    if sql_content:
        # Save to file
        with open('insert_base_stope.sql', 'w', encoding='utf-8') as f:
            f.write(sql_content)
        
        print("SQL脚本已生成: insert_base_stope.sql")
        print("\n前几行预览:")
        lines = sql_content.split('\n')
        for i, line in enumerate(lines[:20]):
            print(f"{i+1:3d}: {line}")
        
        if len(lines) > 20:
            print("...")
            print(f"总共 {len(lines)} 行")
    else:
        print("生成失败")

if __name__ == "__main__":
    main()
