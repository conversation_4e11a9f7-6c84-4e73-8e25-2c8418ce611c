#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Extract unique working_face_name and stope_name pairs from SQL file
"""

import re
import json

def extract_name_pairs(sql_file_path):
    """Extract unique working_face_name and stope_name pairs from SQL file"""
    
    pairs = set()
    
    try:
        with open(sql_file_path, 'r', encoding='utf-8') as file:
            content = file.read()
            
            # 使用正则表达式匹配每个INSERT语句中的working_face_name和stope_name
            # 匹配模式：从working_face_name到stope_name的完整模式
            pattern = r"working_face_name = '([^']+)'.*?stope_name = '([^']+)'"
            
            matches = re.findall(pattern, content, re.DOTALL)
            
            for working_face_name, stope_name in matches:
                # 清理换行符和多余空格
                working_face_name = working_face_name.strip()
                stope_name = stope_name.strip()
                
                # 添加到集合中（自动去重）
                # 格式：(stope_name, working_face_name)
                pairs.add((stope_name, working_face_name))
            
    except FileNotFoundError:
        print(f"Error: File {sql_file_path} not found")
        return None
    except Exception as e:
        print(f"Error reading file: {e}")
        return None
    
    # 转换为列表并排序
    pairs_list = sorted(list(pairs))
    
    return pairs_list

def main():
    sql_file = "tunneling_data_insert.sql"
    
    print("正在提取 working_face_name 和 stope_name 的唯一配对...")
    
    pairs = extract_name_pairs(sql_file)
    
    if pairs is not None:
        print(f"\n找到 {len(pairs)} 个唯一的配对:")
        print("pairs = [")
        for stope_name, working_face_name in pairs:
            print(f"    ['{stope_name}', '{working_face_name}'],")
        print("]")
        
        # 也保存为JSON格式
        pairs_data = {
            "pairs": [[stope_name, working_face_name] for stope_name, working_face_name in pairs],
            "total_count": len(pairs)
        }
        
        with open("extracted_pairs.json", 'w', encoding='utf-8') as f:
            json.dump(pairs_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n结果已保存到 extracted_pairs.json 文件中")
        
        # 统计信息
        unique_stopes = set(pair[0] for pair in pairs)
        unique_working_faces = set(pair[1] for pair in pairs)
        
        print(f"\n统计信息:")
        print(f"- 唯一配对数量: {len(pairs)}")
        print(f"- 唯一stope_name数量: {len(unique_stopes)}")
        print(f"- 唯一working_face_name数量: {len(unique_working_faces)}")
        
    else:
        print("提取失败")

if __name__ == "__main__":
    main()
