#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Extract unique working_face_name and stope_name values from SQL file
"""

import re
import json

def extract_unique_names(sql_file_path):
    """Extract unique working_face_name and stope_name values from SQL file"""
    
    working_face_names = set()
    stope_names = set()
    
    # Regular expressions to match the patterns
    working_face_pattern = r"working_face_name = '([^']+)'"
    stope_name_pattern = r"stope_name = '([^']+)'"
    
    try:
        with open(sql_file_path, 'r', encoding='utf-8') as file:
            content = file.read()
            
            # Find all working_face_name values
            working_face_matches = re.findall(working_face_pattern, content)
            working_face_names.update(working_face_matches)
            
            # Find all stope_name values
            stope_name_matches = re.findall(stope_name_pattern, content)
            stope_names.update(stope_name_matches)
            
    except FileNotFoundError:
        print(f"Error: File {sql_file_path} not found")
        return None, None
    except Exception as e:
        print(f"Error reading file: {e}")
        return None, None
    
    # Convert sets to sorted lists for consistent output
    working_face_list = sorted(list(working_face_names))
    stope_name_list = sorted(list(stope_names))
    
    return working_face_list, stope_name_list

def main():
    sql_file = "tunneling_data_insert.sql"
    
    print("正在提取 working_face_name 和 stope_name 的唯一值...")
    
    working_faces, stopes = extract_unique_names(sql_file)
    
    if working_faces is not None and stopes is not None:
        print(f"\n找到 {len(working_faces)} 个唯一的 working_face_name:")
        print("working_face_names = [")
        for name in working_faces:
            print(f"    '{name}',")
        print("]")
        
        print(f"\n找到 {len(stopes)} 个唯一的 stope_name:")
        print("stope_names = [")
        for name in stopes:
            print(f"    '{name}',")
        print("]")
        
        # Also save to JSON file for easy use
        result = {
            "working_face_names": working_faces,
            "stope_names": stopes
        }
        
        with open("extracted_names.json", 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"\n结果已保存到 extracted_names.json 文件中")
        
    else:
        print("提取失败")

if __name__ == "__main__":
    main()
