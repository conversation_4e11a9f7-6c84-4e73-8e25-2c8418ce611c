-- 插入base_stope表的SQL脚本
-- 生成时间: 2025-07-31 19:55:17

-- 使用INSERT ... ON CONFLICT DO NOTHING 避免重复插入
INSERT INTO public.base_stope (
    stope_name,
    working_face_id,
    status,
    create_by,
    create_time,
    update_by,
    is_delete
) VALUES
    ('-1020m水平6-3溜井硐室', 7, 1, 'admin', NOW(), 'admin', 0),
    ('-1060m/-1100原矿仓溜井', 3, 1, 'admin', NOW(), 'admin', 0),
    ('-1060m水平4-3溜井硐室', 7, 1, 'admin', NOW(), 'admin', 0),
    ('-1060m水平6-4溜井硐室', 7, 1, 'admin', NOW(), 'admin', 0),
    ('-1080m/-1123m斜坡道', 8, 1, 'admin', NOW(), 'admin', 0),
    ('-1080m/-1128m斜坡道', 8, 1, 'admin', NOW(), 'admin', 0),
    ('-1080m/-1128m斜坡道', 3, 1, 'admin', NOW(), 'admin', 0),
    ('-1100m破碎硐室', 8, 1, 'admin', NOW(), 'admin', 0),
    ('-1100m破碎硐室', 3, 1, 'admin', NOW(), 'admin', 0),
    ('-1100m破碎站配电硐室', 8, 1, 'admin', NOW(), 'admin', 0),
    -- 已处理 10 条记录
    ('-1100m破碎配电硐室', 8, 1, 'admin', NOW(), 'admin', 0),
    ('-1100m胶带斜井联巷', 3, 1, 'admin', NOW(), 'admin', 0),
    ('-1128尾轮硐室', 3, 1, 'admin', NOW(), 'admin', 0),
    ('-1128尾轮硐室联络巷', 3, 1, 'admin', NOW(), 'admin', 0),
    ('-1277m水平沉淀池、水仓', 6, 1, 'admin', NOW(), 'admin', 0),
    ('-1277m沉淀池、水仓', 6, 1, 'admin', NOW(), 'admin', 0),
    ('-840m/-765m斜坡道', 8, 1, 'admin', NOW(), 'admin', 0),
    ('-840m中段石门巷（副井侧）', 8, 1, 'admin', NOW(), 'admin', 0),
    ('-840m副井侧倒段溜井', 8, 1, 'admin', NOW(), 'admin', 0),
    ('-840m副井侧倒段溜井（-840m/-960m）', 8, 1, 'admin', NOW(), 'admin', 0),
    -- 已处理 20 条记录
    ('-840m副井侧石门巷', 8, 1, 'admin', NOW(), 'admin', 0),
    ('-840m副井石门巷', 8, 1, 'admin', NOW(), 'admin', 0),
    ('-840m副井进风井联巷', 8, 1, 'admin', NOW(), 'admin', 0),
    ('-840m措施井0#穿', 8, 1, 'admin', NOW(), 'admin', 0),
    ('-840m措施井0号穿', 8, 1, 'admin', NOW(), 'admin', 0),
    ('-840m措施井0号穿倒车硐室', 8, 1, 'admin', NOW(), 'admin', 0),
    ('-840m措施井侧0#穿', 8, 1, 'admin', NOW(), 'admin', 0),
    ('-840m措施井侧2#穿', 8, 1, 'admin', NOW(), 'admin', 0),
    ('-840m措施井侧倒段溜井', 8, 1, 'admin', NOW(), 'admin', 0),
    ('-840m措施井石门巷', 8, 1, 'admin', NOW(), 'admin', 0),
    -- 已处理 30 条记录
    ('-840m措施井石门巷（往2号穿）', 8, 1, 'admin', NOW(), 'admin', 0),
    ('-840m进风井联巷', 8, 1, 'admin', NOW(), 'admin', 0),
    ('-840至865斜坡道', 8, 1, 'admin', NOW(), 'admin', 0),
    ('-865m辅助运输巷', 8, 1, 'admin', NOW(), 'admin', 0),
    ('-960m/-1020m通风天井（6#穿）', 7, 1, 'admin', NOW(), 'admin', 0),
    ('-960m/-840m斜坡道', 8, 1, 'admin', NOW(), 'admin', 0),
    ('-960m/-840m斜坡道(通-840m中段)', 8, 1, 'admin', NOW(), 'admin', 0),
    ('-960m/-840m斜坡道倒渣井', 8, 1, 'admin', NOW(), 'admin', 0),
    ('-960m/-840m斜坡道通风井', 8, 1, 'admin', NOW(), 'admin', 0),
    ('-960m集中高段溜井联巷', 8, 1, 'admin', NOW(), 'admin', 0),
    -- 已处理 40 条记录
    ('-960m集中高段溜井联巷', 3, 1, 'admin', NOW(), 'admin', 0),
    ('0#-15-3切割巷', 5, 1, 'admin', NOW(), 'admin', 0),
    ('0#-15-3矿块', 2, 1, 'admin', NOW(), 'admin', 0),
    ('0#-15-7凿岩巷（计划外）', 5, 1, 'admin', NOW(), 'admin', 0),
    ('0#-15探矿巷', 2, 1, 'admin', NOW(), 'admin', 0),
    ('0#-15探矿巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('0#-15探矿巷', 5, 1, 'admin', NOW(), 'admin', 0),
    ('0#-15探矿巷（计划外）', 2, 1, 'admin', NOW(), 'admin', 0),
    ('0#-15探矿巷（计划外）', 4, 1, 'admin', NOW(), 'admin', 0),
    ('0#-15探矿巷（计划外）', 5, 1, 'admin', NOW(), 'admin', 0),
    -- 已处理 50 条记录
    ('0#-19探矿巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('0#-2-1充填巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('0#-2-1凿岩巷', 5, 1, 'admin', NOW(), 'admin', 0),
    ('0#-2-1号充填巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('0#-2-1矿块', 2, 1, 'admin', NOW(), 'admin', 0),
    ('0#-2-2充填巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('0#-2-2号充填巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('0#-2-3充填巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('0#-2-3凿岩巷', 5, 1, 'admin', NOW(), 'admin', 0),
    ('0#-2-5凿岩巷', 5, 1, 'admin', NOW(), 'admin', 0),
    -- 已处理 60 条记录
    ('0#-2-5矿块', 2, 1, 'admin', NOW(), 'admin', 0),
    ('0#-2-7凿岩巷', 5, 1, 'admin', NOW(), 'admin', 0),
    ('0#-2-7矿块（计划外）', 2, 1, 'admin', NOW(), 'admin', 0),
    ('0#-2-9凿岩巷', 5, 1, 'admin', NOW(), 'admin', 0),
    ('0#-2-9凿岩巷（计划外）', 5, 1, 'admin', NOW(), 'admin', 0),
    ('0#-2-9矿块', 2, 1, 'admin', NOW(), 'admin', 0),
    ('0#-20-1充填巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('0#-20-1凿岩巷', 5, 1, 'admin', NOW(), 'admin', 0),
    ('0#-20-2号充填巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('0#-20-7凿岩巷', 5, 1, 'admin', NOW(), 'admin', 0),
    -- 已处理 70 条记录
    ('0#-20-7矿块', 2, 1, 'admin', NOW(), 'admin', 0),
    ('0#-26-1充填巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('0#-26-1号充填巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('0#-26-2凿岩巷（风机硐室）', 5, 1, 'admin', NOW(), 'admin', 0),
    ('0#-26-2号充填巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('0#-26-3凿岩巷', 5, 1, 'admin', NOW(), 'admin', 0),
    ('0#-26-3号充填巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('0#-26-3矿块', 2, 1, 'admin', NOW(), 'admin', 0),
    ('0#-26-7凿岩巷', 5, 1, 'admin', NOW(), 'admin', 0),
    ('0#-26-7矿块', 2, 1, 'admin', NOW(), 'admin', 0),
    -- 已处理 80 条记录
    ('0#-2凿岩联巷', 5, 1, 'admin', NOW(), 'admin', 0),
    ('0#-2采场联巷', 5, 1, 'admin', NOW(), 'admin', 0),
    ('0#-5-4凿岩巷', 2, 1, 'admin', NOW(), 'admin', 0),
    ('0#-5-4凿岩巷', 5, 1, 'admin', NOW(), 'admin', 0),
    ('0#-5-4矿块', 2, 1, 'admin', NOW(), 'admin', 0),
    ('0#-5-8凿岩巷', 2, 1, 'admin', NOW(), 'admin', 0),
    ('0#-5-8凿岩巷', 5, 1, 'admin', NOW(), 'admin', 0),
    ('0#-5-8矿块', 2, 1, 'admin', NOW(), 'admin', 0),
    ('0#-5探矿巷', 5, 1, 'admin', NOW(), 'admin', 0),
    ('0#穿下盘沿脉探矿巷', 2, 1, 'admin', NOW(), 'admin', 0),
    -- 已处理 90 条记录
    ('0#穿改', 2, 1, 'admin', NOW(), 'admin', 0),
    ('0#穿改', 4, 1, 'admin', NOW(), 'admin', 0),
    ('0#穿改', 5, 1, 'admin', NOW(), 'admin', 0),
    ('0#穿改副井侧', 2, 1, 'admin', NOW(), 'admin', 0),
    ('0#穿改副井侧', 4, 1, 'admin', NOW(), 'admin', 0),
    ('0#穿改探矿巷（0#-15区域）', 2, 1, 'admin', NOW(), 'admin', 0),
    ('0#穿改探矿巷（0#-5区域）', 2, 1, 'admin', NOW(), 'admin', 0),
    ('0-15-1充填巷（计划外）', 4, 1, 'admin', NOW(), 'admin', 0),
    ('0-15-1（计划外）', 4, 1, 'admin', NOW(), 'admin', 0),
    ('0-15-2号充填巷', 4, 1, 'admin', NOW(), 'admin', 0),
    -- 已处理 100 条记录
    ('0-15-2（计划外）', 4, 1, 'admin', NOW(), 'admin', 0),
    ('0-15-3矿块', 2, 1, 'admin', NOW(), 'admin', 0),
    ('0-15-7凿岩巷', 5, 1, 'admin', NOW(), 'admin', 0),
    ('0-1探矿巷', 2, 1, 'admin', NOW(), 'admin', 0),
    ('0-1探矿巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('0-1溜井', 7, 1, 'admin', NOW(), 'admin', 0),
    ('0-2-3矿块', 2, 1, 'admin', NOW(), 'admin', 0),
    ('0-2-5矿块', 2, 1, 'admin', NOW(), 'admin', 0),
    ('0-20-1矿块（计划外）', 4, 1, 'admin', NOW(), 'admin', 0),
    ('0-26-1充填巷（计划外）', 4, 1, 'admin', NOW(), 'admin', 0),
    -- 已处理 110 条记录
    ('0-26-1矿块（计划外）', 4, 1, 'admin', NOW(), 'admin', 0),
    ('0-26空压机硐室', 5, 1, 'admin', NOW(), 'admin', 0),
    ('0-3溜井', 7, 1, 'admin', NOW(), 'admin', 0),
    ('0-5-4凿岩巷（计划外', 5, 1, 'admin', NOW(), 'admin', 0),
    ('0-5-4矿块（计划外）', 2, 1, 'admin', NOW(), 'admin', 0),
    ('1060皮带道硐室', 6, 1, 'admin', NOW(), 'admin', 0),
    ('1100m胶带斜井联络巷', 3, 1, 'admin', NOW(), 'admin', 0),
    ('2#-12-11凿岩巷', 5, 1, 'admin', NOW(), 'admin', 0),
    ('2#-12-11矿块', 2, 1, 'admin', NOW(), 'admin', 0),
    ('2#-12-1矿块', 2, 1, 'admin', NOW(), 'admin', 0),
    -- 已处理 120 条记录
    ('2#-12-2号充填巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('2#-12-3凿岩巷', 5, 1, 'admin', NOW(), 'admin', 0),
    ('2#-12-3号充填巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('2#-12-3矿块', 2, 1, 'admin', NOW(), 'admin', 0),
    ('2#-12-5矿块', 2, 1, 'admin', NOW(), 'admin', 0),
    ('2#-12-7凿岩巷', 5, 1, 'admin', NOW(), 'admin', 0),
    ('2#-12-7矿块', 2, 1, 'admin', NOW(), 'admin', 0),
    ('2#-12-9矿块', 2, 1, 'admin', NOW(), 'admin', 0),
    ('2#-2-11凿岩巷', 2, 1, 'admin', NOW(), 'admin', 0),
    ('2#-2-11凿岩巷', 5, 1, 'admin', NOW(), 'admin', 0),
    -- 已处理 130 条记录
    ('2#-2-11矿块', 2, 1, 'admin', NOW(), 'admin', 0),
    ('2#-2-11矿块（出矿巷）', 2, 1, 'admin', NOW(), 'admin', 0),
    ('2#-2-1充填巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('2#-2-1凿岩巷', 2, 1, 'admin', NOW(), 'admin', 0),
    ('2#-2-1号充填巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('2#-2-1矿块', 2, 1, 'admin', NOW(), 'admin', 0),
    ('2#-2-2充填巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('2#-2-2号充填巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('2#-2-2矿块', 2, 1, 'admin', NOW(), 'admin', 0),
    ('2#-2-3充填巷', 4, 1, 'admin', NOW(), 'admin', 0),
    -- 已处理 140 条记录
    ('2#-2-3凿岩巷', 5, 1, 'admin', NOW(), 'admin', 0),
    ('2#-2-3号充填巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('2#-2-3号充填巷（计划外）', 4, 1, 'admin', NOW(), 'admin', 0),
    ('2#-2-3矿块', 2, 1, 'admin', NOW(), 'admin', 0),
    ('2#-2-4矿块', 2, 1, 'admin', NOW(), 'admin', 0),
    ('2#-2-7凿岩巷', 5, 1, 'admin', NOW(), 'admin', 0),
    ('2#-2-7号充填巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('2#-2-7矿块', 2, 1, 'admin', NOW(), 'admin', 0),
    ('2#-2-7矿块（出矿巷）', 2, 1, 'admin', NOW(), 'admin', 0),
    ('2#-20-11出矿巷', 2, 1, 'admin', NOW(), 'admin', 0),
    -- 已处理 150 条记录
    ('2#-20-11凿岩巷（计划外）', 5, 1, 'admin', NOW(), 'admin', 0),
    ('2#-20-11矿块', 5, 1, 'admin', NOW(), 'admin', 0),
    ('2#-20-12凿岩巷', 2, 1, 'admin', NOW(), 'admin', 0),
    ('2#-20-12凿岩巷', 5, 1, 'admin', NOW(), 'admin', 0),
    ('2#-20-1号充填巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('2#-20-2号充填巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('2#-20-3凿岩巷', 5, 1, 'admin', NOW(), 'admin', 0),
    ('2#-20-3号充填巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('2#-20-6探矿硐室', 2, 1, 'admin', NOW(), 'admin', 0),
    ('2#-20-9出矿巷', 2, 1, 'admin', NOW(), 'admin', 0),
    -- 已处理 160 条记录
    ('2#-20-9凿岩巷', 5, 1, 'admin', NOW(), 'admin', 0),
    ('2#-20-9矿块', 2, 1, 'admin', NOW(), 'admin', 0),
    ('2#-20采场联巷', 5, 1, 'admin', NOW(), 'admin', 0),
    ('2#-22-11出矿巷', 2, 1, 'admin', NOW(), 'admin', 0),
    ('2#-22-11凿岩巷', 5, 1, 'admin', NOW(), 'admin', 0),
    ('2#-22-11矿块', 2, 1, 'admin', NOW(), 'admin', 0),
    ('2#-22-12凿岩巷', 2, 1, 'admin', NOW(), 'admin', 0),
    ('2#-22-12凿岩巷', 5, 1, 'admin', NOW(), 'admin', 0),
    ('2#-22-1号充填巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('2#-22-2号充填巷', 4, 1, 'admin', NOW(), 'admin', 0),
    -- 已处理 170 条记录
    ('2#-22-3号充填巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('2#-22-3矿块', 4, 1, 'admin', NOW(), 'admin', 0),
    ('2#-22-6探矿硐', 5, 1, 'admin', NOW(), 'admin', 0),
    ('2#-22-9出矿巷', 2, 1, 'admin', NOW(), 'admin', 0),
    ('2#-22-9矿块', 2, 1, 'admin', NOW(), 'admin', 0),
    ('2#-22-9矿块（计划外）', 2, 1, 'admin', NOW(), 'admin', 0),
    ('2#-22采场联巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('2#-22采场联巷', 5, 1, 'admin', NOW(), 'admin', 0),
    ('2#-4-5凿岩巷', 5, 1, 'admin', NOW(), 'admin', 0),
    ('2#-4-5矿块', 2, 1, 'admin', NOW(), 'admin', 0),
    -- 已处理 180 条记录
    ('2#-4充填联巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('2#-4出矿联巷', 2, 1, 'admin', NOW(), 'admin', 0),
    ('2#-5-3号充填巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('2#-5-3矿块', 2, 1, 'admin', NOW(), 'admin', 0),
    ('2#-5-7凿岩巷', 5, 1, 'admin', NOW(), 'admin', 0),
    ('2#-5-7矿块', 2, 1, 'admin', NOW(), 'admin', 0),
    ('2#-5-9凿岩巷', 2, 1, 'admin', NOW(), 'admin', 0),
    ('2#-5-9矿块（计划外）', 2, 1, 'admin', NOW(), 'admin', 0),
    ('2#主井皮带道', 6, 1, 'admin', NOW(), 'admin', 0),
    ('2#主井皮带道硐室', 6, 1, 'admin', NOW(), 'admin', 0),
    -- 已处理 190 条记录
    ('2#主井粉矿回收巷', 6, 1, 'admin', NOW(), 'admin', 0),
    ('2#措施溜井联巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('2#措施溜井联巷', 3, 1, 'admin', NOW(), 'admin', 0),
    ('2-12-11矿块（计划外）', 5, 1, 'admin', NOW(), 'admin', 0),
    ('2-2-11凿岩巷', 5, 1, 'admin', NOW(), 'admin', 0),
    ('2-2-11矿块（计划外）', 5, 1, 'admin', NOW(), 'admin', 0),
    ('2-20-11矿块（计划外', 2, 1, 'admin', NOW(), 'admin', 0),
    ('2-22-11矿块（计划外）', 2, 1, 'admin', NOW(), 'admin', 0),
    ('2-22-11矿块（计划外）', 5, 1, 'admin', NOW(), 'admin', 0),
    ('2-22-3充填巷', 4, 1, 'admin', NOW(), 'admin', 0),
    -- 已处理 200 条记录
    ('2-4采场联巷（计划外）', 2, 1, 'admin', NOW(), 'admin', 0),
    ('2-7溜井', 7, 1, 'admin', NOW(), 'admin', 0),
    ('4#穿', 6, 1, 'admin', NOW(), 'admin', 0),
    ('4#穿溜井操作硐室', 6, 1, 'admin', NOW(), 'admin', 0),
    ('4#穿高段溜井及操作硐室', 6, 1, 'admin', NOW(), 'admin', 0),
    ('4-1溜井', 7, 1, 'admin', NOW(), 'admin', 0),
    ('4-1溜井卸矿硐室', 2, 1, 'admin', NOW(), 'admin', 0),
    ('4-1溜井振动放矿机硐室', 6, 1, 'admin', NOW(), 'admin', 0),
    ('4-2溜井', 7, 1, 'admin', NOW(), 'admin', 0),
    ('4-4溜井硐室', 7, 1, 'admin', NOW(), 'admin', 0),
    -- 已处理 210 条记录
    ('6#穿', 2, 1, 'admin', NOW(), 'admin', 0),
    ('6#穿', 4, 1, 'admin', NOW(), 'admin', 0),
    ('6#穿', 5, 1, 'admin', NOW(), 'admin', 0),
    ('6#穿上盘侧', 6, 1, 'admin', NOW(), 'admin', 0),
    ('6#穿副井侧', 6, 1, 'admin', NOW(), 'admin', 0),
    ('6#穿探矿', 2, 1, 'admin', NOW(), 'admin', 0),
    ('6#穿探矿巷', 5, 1, 'admin', NOW(), 'admin', 0),
    ('6#穿措施井侧', 6, 1, 'admin', NOW(), 'admin', 0),
    ('6#穿脉', 2, 1, 'admin', NOW(), 'admin', 0),
    ('6#穿脉', 4, 1, 'admin', NOW(), 'admin', 0),
    -- 已处理 220 条记录
    ('6-1溜井', 7, 1, 'admin', NOW(), 'admin', 0),
    ('6-3溜井硐室', 7, 1, 'admin', NOW(), 'admin', 0),
    ('6-4溜井', 7, 1, 'admin', NOW(), 'admin', 0),
    ('960m中转成品矿仓联巷', 8, 1, 'admin', NOW(), 'admin', 0),
    ('960m胶带斜井头部硐室联巷', 8, 1, 'admin', NOW(), 'admin', 0),
    ('960胶带斜井', 8, 1, 'admin', NOW(), 'admin', 0),
    ('下盘沿脉至0#改', 2, 1, 'admin', NOW(), 'admin', 0),
    ('下盘沿脉至0#改', 4, 1, 'admin', NOW(), 'admin', 0),
    ('下盘沿脉（往0#穿改）', 2, 1, 'admin', NOW(), 'admin', 0),
    ('下盘沿脉（往0#穿改）', 4, 1, 'admin', NOW(), 'admin', 0),
    -- 已处理 230 条记录
    ('下盘沿脉（往6#穿）', 6, 1, 'admin', NOW(), 'admin', 0),
    ('中转成品矿仓联巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('中转成品矿仓联巷', 8, 1, 'admin', NOW(), 'admin', 0),
    ('倒段溜井联巷（计划外）', 4, 1, 'admin', NOW(), 'admin', 0),
    ('回风井联巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('回风井联巷（计划外）', 4, 1, 'admin', NOW(), 'admin', 0),
    ('回风井联络巷（计划外）', 4, 1, 'admin', NOW(), 'admin', 0),
    ('回风巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('措施井车场', 8, 1, 'admin', NOW(), 'admin', 0),
    ('无轨维修其他硐室', 2, 1, 'admin', NOW(), 'admin', 0),
    -- 已处理 240 条记录
    ('无轨维修硐室', 2, 1, 'admin', NOW(), 'admin', 0),
    ('无轨维修硐室联巷', 2, 1, 'admin', NOW(), 'admin', 0),
    ('有轨维修硐室', 6, 1, 'admin', NOW(), 'admin', 0),
    ('混凝土搅拌硐室', 4, 1, 'admin', NOW(), 'admin', 0),
    ('混凝土搅拌硐室联巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('混凝土搅拌站其他硐室', 4, 1, 'admin', NOW(), 'admin', 0),
    ('混凝土搅拌站其他硐室', 4, 1, 'admin', NOW(), 'admin', 0),
    ('胶带斜井', 8, 1, 'admin', NOW(), 'admin', 0),
    ('胶带斜井', 3, 1, 'admin', NOW(), 'admin', 0),
    ('胶带斜井及联巷', 8, 1, 'admin', NOW(), 'admin', 0),
    -- 已处理 250 条记录
    ('胶带斜井头部硐室联巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('胶带斜井头部硐室联巷', 8, 1, 'admin', NOW(), 'admin', 0),
    ('胶带斜井尾部硐室联巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('胶带斜井尾部硐室联巷', 8, 1, 'admin', NOW(), 'admin', 0),
    ('胶带斜井联巷', 8, 1, 'admin', NOW(), 'admin', 0),
    ('进风井联巷', 4, 1, 'admin', NOW(), 'admin', 0),
    ('进风井（计划外）', 4, 1, 'admin', NOW(), 'admin', 0),
    ('通风井联巷（计划外）', 4, 1, 'admin', NOW(), 'admin', 0)
    -- 已处理 258 条记录
ON CONFLICT (stope_name, working_face_id) DO NOTHING;

-- 处理总结:
-- 总配对数量: 258
-- 成功处理: 258
-- 跳过数量: 0

-- 验证插入结果:
-- SELECT COUNT(*) as inserted_count FROM public.base_stope WHERE create_by = 'admin';

-- 查看插入的记录:
-- SELECT bs.stope_name, bwf.working_face_name, bs.create_time
-- FROM public.base_stope bs
-- JOIN public.base_working_face bwf ON bs.working_face_id = bwf.id
-- WHERE bs.create_by = 'admin'
-- ORDER BY bs.create_time DESC;