-- 支护数据表
CREATE TABLE IF NOT EXISTS data_support (
    id bigserial primary key,
    operation_date date NOT NULL,   -- 作业日期
    project_department_id bigint NOT NULL,   -- 项目部门ID
    working_face_id bigint,   -- 工作面ID (中段)
    stope_id bigint,   -- 采场ID
    working_period_id bigint NOT NULL,   -- 作业时段ID
    support_type varchar(10) NOT NULL,   -- 支护类型：'锚网支护'、'喷浆支护'
    support_length numeric(8,2),   -- 支护长度(m)
    support_volume numeric(10,2),   -- 支护体积(m³)
    remarks varchar(500),   -- 备注
    create_by varchar(64),
    create_time timestamp(6) DEFAULT now(),
    update_by varchar(64),
    update_time timestamp(6) DEFAULT now(),
    FOREIGN KEY (project_department_id) REFERENCES base_project_department(project_department_id),
    FOREIGN KEY (stope_id) REFERENCES base_stope(stope_id),
    FOR<PERSON>GN KEY (working_face_id) REFERENCES base_working_face(working_face_id),
    FOREIGN KEY (working_period_id) REFERENCES base_working_period(working_period_id)
);

COMMENT ON TABLE data_support IS '支护数据表';
COMMENT ON COLUMN data_support.id IS '支护数据ID';
COMMENT ON COLUMN data_support.operation_date IS '作业日期';
COMMENT ON COLUMN data_support.project_department_id IS '项目部门ID';
COMMENT ON COLUMN data_support.stope_id IS '采场ID';
COMMENT ON COLUMN data_support.working_face_id IS '工作面ID';
COMMENT ON COLUMN data_support.working_period_id IS '作业时段ID';
COMMENT ON COLUMN data_support.support_type IS '支护类型：锚网支护、喷浆支护';
COMMENT ON COLUMN data_support.support_length IS '支护长度(m)';
COMMENT ON COLUMN data_support.support_volume IS '支护体积(m³)';
COMMENT ON COLUMN data_support.remarks IS '备注';
COMMENT ON COLUMN data_support.create_by IS '创建人';
COMMENT ON COLUMN data_support.create_time IS '创建时间';
COMMENT ON COLUMN data_support.update_by IS '更新人';
COMMENT ON COLUMN data_support.update_time IS '更新时间';

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_data_support_date ON data_support (operation_date);
CREATE INDEX IF NOT EXISTS idx_data_support_department ON data_support (project_department_id);
CREATE INDEX IF NOT EXISTS idx_data_support_stope ON data_support (stope_id);
CREATE INDEX IF NOT EXISTS idx_data_support_face ON data_support (working_face_id);
CREATE INDEX IF NOT EXISTS idx_data_support_period ON data_support (working_period_id);
CREATE INDEX IF NOT EXISTS idx_data_support_type ON data_support (support_type);
