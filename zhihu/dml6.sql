-- 支护数据插入语句
-- 生成时间: 2025-07-30 21:26:38

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-29', 2, 1, '2', 24.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-29', 3, 1, '2', 3.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-29', 4, 1, '2', 3.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-30', 2, 2, '1', 12.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-30', 2, 1, '2', 13.5, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-30', 3, 2, '1', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-30', 3, 1, '2', 26.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-30', 4, 1, '1', 5.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-30', 4, 1, '2', 3.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-31', 2, 1, '1', 13.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-31', 2, 2, '1', 12.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-31', 2, 1, '2', 16.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-31', 2, 2, '2', 19.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-31', 3, 1, '1', 14.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-31', 3, 2, '1', 14.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-31', 4, 1, '2', 3.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-01', 2, 2, '1', 24.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-01', 3, 2, '1', 14.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-01', 3, 1, '2', 20.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-01', 4, 2, '1', 6.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-02', 2, 1, '2', 11.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-02', 3, 2, '1', 9.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-02', 3, 1, '2', 18.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-02', 4, 1, '2', 5.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-03', 2, 2, '1', 16.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-03', 2, 1, '2', 17.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-03', 3, 2, '1', 17.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-03', 3, 2, '2', 13.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-03', 4, 1, '1', 11.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-03', 4, 2, '1', 6.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-04', 2, 1, '1', 16.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-04', 2, 2, '1', 14.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-04', 2, 1, '2', 27.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-04', 2, 2, '2', 26.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-04', 3, 1, '1', 9.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-04', 3, 2, '1', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-05', 3, 1, '2', 15.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-06', 2, 2, '2', 17.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-06', 3, 2, '1', 12.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-06', 3, 2, '2', 13.5, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-06', 4, 1, '2', 3.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-07', 3, 2, '1', 10.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-07', 3, 2, '2', 26.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-07', 4, 1, '2', 16.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-07', 4, 2, '2', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-08', 2, 2, '1', 18.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-08', 2, 1, '2', 30.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-08', 4, 1, '2', 10.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-09', 2, 1, '1', 12.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-09', 2, 2, '1', 20.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-09', 2, 2, '2', 27.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-09', 3, 2, '1', 5.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-09', 3, 2, '2', 18.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-10', 2, 1, '1', 14.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-10', 2, 1, '2', 19.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-10', 3, 2, '1', 20.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-10', 3, 2, '2', 12.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-10', 4, 1, '1', 3.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-10', 4, 1, '2', 12.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-11', 2, 1, '1', 14.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-11', 2, 2, '1', 17.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-11', 2, 1, '2', 25.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-11', 3, 2, '1', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-11', 3, 1, '2', 15.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-11', 3, 2, '2', 16.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-11', 4, 1, '2', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-12', 2, 2, '1', 5.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-12', 2, 1, '2', 27.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-12', 2, 2, '2', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-12', 3, 1, '1', 6.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-12', 3, 2, '1', 13.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-12', 3, 2, '2', 21.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-12', 4, 1, '2', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-13', 2, 2, '1', 15.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-13', 2, 2, '2', 21.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-13', 3, 1, '1', 10.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-13', 3, 2, '1', 19.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-13', 3, 2, '2', 17.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-14', 2, 2, '1', 28.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-14', 2, 2, '2', 20.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-14', 3, 1, '1', 9.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-14', 3, 2, '1', 7.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-14', 3, 1, '2', 30.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-14', 3, 2, '2', 18.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-14', 4, 1, '2', 13.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-15', 2, 1, '1', 5.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-15', 2, 2, '1', 26.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-15', 2, 2, '2', 15.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-15', 3, 1, '1', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-15', 3, 2, '1', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-15', 3, 2, '2', 42.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-15', 4, 1, '2', 3.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-16', 2, 1, '1', 13.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-16', 2, 2, '1', 12.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-16', 2, 2, '2', 17.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-16', 3, 1, '1', 11.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-16', 3, 2, '1', 18.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-16', 3, 2, '2', 30.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-17', 2, 2, '1', 14.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-17', 2, 1, '2', 17.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-17', 3, 1, '1', 10.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-17', 3, 2, '1', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-17', 3, 2, '2', 30.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-17', 4, 1, '2', 6.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-18', 2, 1, '1', 16.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-18', 2, 2, '1', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-18', 2, 2, '2', 23.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-18', 3, 1, '1', 9.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-18', 3, 2, '1', 10.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-18', 3, 2, '2', 30.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-18', 4, 1, '2', 3.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-19', 2, 2, '1', 15.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-19', 3, 1, '1', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-19', 3, 2, '1', 15.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-19', 3, 2, '2', 37.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-19', 4, 1, '2', 6.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-20', 2, 2, '1', 12.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-20', 2, 1, '2', 14.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-20', 3, 1, '1', 9.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-20', 3, 2, '1', 17.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-20', 3, 2, '2', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-20', 4, 1, '1', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-20', 4, 1, '2', 5.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-21', 2, 1, '1', 16.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-21', 2, 2, '1', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-21', 2, 2, '2', 12.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-21', 3, 2, '1', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-21', 3, 2, '2', 12.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-21', 4, 1, '2', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-22', 2, 1, '1', 5.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-22', 2, 2, '1', 10.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-22', 2, 2, '2', 12.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-22', 3, 2, '1', 5.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-22', 3, 1, '2', 20.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-22', 3, 2, '2', 24.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-22', 4, 1, '2', 10.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-23', 2, 1, '1', 14.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-23', 2, 2, '1', 10.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-23', 3, 1, '1', 6.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-23', 3, 2, '1', 12.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-23', 4, 1, '2', 10.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-24', 2, 1, '1', 14.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-24', 2, 2, '1', 22.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-24', 2, 2, '2', 15.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-24', 3, 1, '1', 5.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-24', 3, 2, '1', 13.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-24', 4, 1, '2', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-25', 2, 1, '1', 14.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-25', 2, 2, '1', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-25', 2, 1, '2', 30.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-25', 2, 2, '2', 12.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-25', 3, 1, '2', 32.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-25', 4, 1, '2', 9.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-26', 2, 1, '1', 14.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-26', 2, 2, '1', 14.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-26', 3, 1, '1', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-26', 3, 2, '1', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-26', 3, 1, '2', 45.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-26', 4, 1, '2', 12.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-27', 2, 1, '1', 10.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-27', 2, 1, '2', 49.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-27', 3, 1, '1', 7.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-27', 3, 2, '1', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-27', 3, 1, '2', 17.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-27', 3, 2, '2', 33.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-27', 4, 1, '2', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-28', 2, 2, '1', 20.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-28', 2, 1, '2', 17.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-28', 3, 1, '1', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-28', 3, 2, '1', 5.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-28', 4, 1, '2', 8.0, NULL, 'system', 'system');

