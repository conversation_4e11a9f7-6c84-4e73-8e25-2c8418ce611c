-- 支护数据插入语句
-- 生成时间: 2025-07-30 21:23:37

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-29', 2, 2, '1', 25.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-29', 3, 1, '1', 9.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-29', 3, 2, '1', 3.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-29', 3, 1, '2', 49.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-29', 4, 1, '2', 26.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-30', 2, 2, '1', 21.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-30', 3, 1, '1', 5.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-30', 3, 2, '1', 13.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-30', 3, 1, '2', 16.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-30', 4, 2, '2', 6.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-01', 2, 2, '1', 20.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-01', 2, 1, '2', 26.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-01', 2, 2, '2', 27.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-01', 3, 2, '1', 13.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-01', 3, 1, '2', 22.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-01', 4, 1, '2', 3.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-02', 2, 2, '1', 41.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-02', 2, 2, '2', 12.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-02', 3, 2, '1', 15.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-02', 3, 1, '2', 40.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-02', 4, 1, '2', 10.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-03', 2, 2, '1', 36.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-03', 3, 1, '1', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-03', 3, 2, '1', 13.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-03', 3, 1, '2', 80.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-03', 4, 1, '1', 3.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-03', 4, 1, '2', 5.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-03', 4, 2, '2', 11.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-04', 2, 1, '1', 16.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-04', 2, 2, '1', 42.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-04', 2, 1, '2', 18.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-04', 2, 2, '2', 35.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-04', 3, 1, '1', 9.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-04', 3, 2, '1', 6.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-04', 3, 1, '2', 38.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-04', 4, 1, '2', 18.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-04', 4, 2, '2', 9.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-05', 2, 1, '1', 6.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-05', 2, 2, '1', 14.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-05', 2, 1, '2', 3.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-05', 2, 2, '2', 11.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-05', 3, 1, '2', 45.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-05', 4, 1, '2', 3.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-05', 4, 2, '2', 5.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-06', 2, 2, '1', 36.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-06', 2, 2, '2', 30.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-06', 3, 1, '1', 7.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-06', 3, 1, '2', 29.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-06', 4, 1, '2', 15.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-07', 2, 1, '1', 11.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-07', 2, 2, '1', 22.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-07', 2, 1, '2', 17.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-07', 3, 1, '1', 5.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-07', 3, 2, '1', 12.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-07', 3, 1, '2', 38.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-07', 4, 1, '2', 11.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-08', 2, 1, '1', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-08', 2, 2, '1', 26.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-08', 2, 1, '2', 39.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-08', 3, 1, '1', 5.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-08', 3, 2, '1', 17.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-08', 3, 1, '2', 40.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-08', 4, 1, '1', 15.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-08', 4, 1, '2', 10.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-09', 2, 1, '1', 14.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-09', 2, 2, '1', 17.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-09', 2, 1, '2', 25.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-09', 3, 1, '1', 3.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-09', 3, 2, '1', 13.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-09', 3, 1, '2', 20.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-10', 2, 2, '1', 30.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-10', 2, 1, '2', 23.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-10', 2, 2, '2', 26.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-10', 3, 1, '1', 5.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-10', 3, 2, '1', 12.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-10', 3, 1, '2', 45.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-10', 4, 1, '2', 10.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-11', 3, 1, '1', 6.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-11', 3, 2, '1', 23.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-11', 3, 1, '2', 70.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-12', 2, 2, '1', 15.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-12', 2, 1, '2', 23.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-12', 3, 1, '1', 6.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-12', 3, 2, '1', 14.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-12', 3, 1, '2', 19.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-12', 4, 1, '2', 6.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-13', 2, 1, '1', 17.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-13', 2, 2, '1', 12.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-13', 2, 1, '2', 32.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-13', 3, 1, '1', 7.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-13', 3, 2, '1', 11.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-13', 3, 1, '2', 21.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-13', 4, 1, '1', 3.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-13', 4, 2, '2', 6.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-14', 2, 1, '1', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-14', 2, 2, '1', 29.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-14', 3, 1, '1', 5.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-14', 3, 2, '1', 17.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-14', 3, 1, '2', 43.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-14', 4, 1, '2', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-15', 2, 1, '1', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-15', 2, 2, '1', 14.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-15', 2, 1, '2', 20.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-15', 3, 1, '1', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-15', 3, 2, '1', 23.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-15', 3, 1, '2', 21.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-15', 4, 1, '2', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-16', 2, 2, '1', 14.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-16', 2, 1, '2', 21.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-16', 2, 2, '2', 20.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-16', 3, 1, '1', 3.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-16', 3, 1, '2', 30.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-16', 4, 1, '2', 10.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-17', 2, 1, '1', 11.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-17', 2, 2, '1', 15.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-17', 3, 1, '1', 7.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-17', 3, 2, '1', 16.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-17', 3, 1, '2', 41.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-17', 4, 1, '2', 10.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-18', 2, 2, '1', 22.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-18', 2, 1, '2', 15.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-18', 3, 1, '1', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-18', 3, 2, '1', 26.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-18', 3, 1, '2', 22.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-19', 2, 1, '1', 9.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-19', 2, 2, '1', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-19', 2, 1, '2', 27.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-19', 2, 2, '2', 20.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-19', 3, 1, '1', 7.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-19', 3, 2, '1', 16.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-19', 3, 1, '2', 41.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-20', 2, 1, '1', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-20', 2, 2, '1', 19.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-20', 2, 2, '2', 18.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-20', 3, 1, '1', 5.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-20', 3, 1, '2', 33.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-20', 4, 1, '1', 2.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-20', 4, 1, '2', 20.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-21', 2, 2, '1', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-21', 2, 1, '2', 12.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-21', 3, 1, '2', 29.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-21', 4, 1, '1', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-21', 4, 2, '2', 12.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-22', 2, 2, '1', 26.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-22', 2, 1, '2', 23.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-22', 2, 2, '2', 15.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-22', 3, 2, '1', 12.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-22', 3, 1, '2', 48.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-23', 2, 1, '1', 9.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-23', 2, 2, '1', 36.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-23', 3, 1, '1', 2.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-23', 3, 2, '1', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-23', 3, 1, '2', 27.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-23', 4, 2, '1', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-23', 4, 1, '2', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-24', 2, 1, '1', 14.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-24', 2, 2, '1', 13.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-24', 2, 1, '2', 12.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-24', 3, 1, '1', 3.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-24', 3, 2, '1', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-24', 3, 1, '2', 15.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-24', 4, 1, '2', 5.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-25', 2, 2, '1', 16.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-25', 2, 1, '2', 15.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-25', 3, 1, '1', 3.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-25', 3, 2, '1', 14.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-25', 3, 1, '2', 29.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-25', 4, 1, '2', 2.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-25', 4, 2, '2', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-26', 3, 2, '1', 11.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-26', 3, 1, '2', 21.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-26', 4, 2, '1', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-27', 2, 2, '1', 11.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-27', 2, 1, '2', 18.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-27', 2, 2, '2', 24.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-27', 3, 2, '1', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-27', 3, 1, '2', 30.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-27', 4, 1, '1', 3.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-28', 2, 1, '1', 15.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-28', 2, 2, '1', 16.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-28', 2, 1, '2', 26.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-28', 3, 1, '2', 12.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-05-28', 4, 1, '2', 9.0, NULL, 'system', 'system');

