python "c:\Users\<USER>\doc\5\extract_excel.py" "c:\Users\<USER>\doc\5\统计\2025年1月生产及建设统计表.xlsx" --start-row 154 --end-row 177 --start-col A --end-col H --output "C:\Users\<USER>\doc\5\zhihu\extracted_data1.xlsx"

python c:\Users\<USER>\doc\5\zhihu\generate_sql_inserts.py c:\Users\<USER>\doc\5\zhihu\extracted_data1.csv -o c:\Users\<USER>\doc\5\zhihu\dml1.sql

python "c:\Users\<USER>\doc\5\extract_excel.py" "c:\Users\<USER>\doc\5\统计\2025年2月生产及建设统计表.xlsx" --start-row 160 --end-row 185 --start-col A --end-col H --output "C:\Users\<USER>\doc\5\zhihu\extracted_data2.xlsx"

python c:\Users\<USER>\doc\5\zhihu\generate_sql_inserts.py c:\Users\<USER>\doc\5\zhihu\extracted_data2.csv -o c:\Users\<USER>\doc\5\zhihu\dml2.sql

python "c:\Users\<USER>\doc\5\extract_excel.py" "c:\Users\<USER>\doc\5\统计\2025年3月份生产及建设统计表.xlsx" --start-row 252 --end-row 263 --start-col A --end-col E --output "C:\Users\<USER>\doc\5\zhihu\extracted_data3.xlsx"

python c:\Users\<USER>\doc\5\zhihu\generate_sql_inserts.py c:\Users\<USER>\doc\5\zhihu\extracted_data3.csv -o c:\Users\<USER>\doc\5\zhihu\dml3.sql

python "c:\Users\<USER>\doc\5\extract_excel.py" "c:\Users\<USER>\doc\5\统计\2025年4月生产及建设统计表.xlsx" --start-row 284 --end-row 295 --start-col A --end-col E --output "C:\Users\<USER>\doc\5\zhihu\extracted_data4.xlsx"

python c:\Users\<USER>\doc\5\zhihu\generate_sql_inserts.py c:\Users\<USER>\doc\5\zhihu\extracted_data4.csv -o c:\Users\<USER>\doc\5\zhihu\dml4.sql

python "c:\Users\<USER>\doc\5\extract_excel.py" "c:\Users\<USER>\doc\5\统计\2025年5月份生产及建设统计表.xlsx" --start-row 295 --end-row 306 --start-col A --end-col E --output "C:\Users\<USER>\doc\5\zhihu\extracted_data5.xlsx"

python c:\Users\<USER>\doc\5\zhihu\generate_sql_inserts.py c:\Users\<USER>\doc\5\zhihu\extracted_data5.csv -o c:\Users\<USER>\doc\5\zhihu\dml5.sql

python "c:\Users\<USER>\doc\5\extract_excel.py" "c:\Users\<USER>\doc\5\统计\2025年6月份生产及建设统计表.xlsx" --start-row 291 --end-row 299 --start-col A --end-col E --output "C:\Users\<USER>\doc\5\zhihu\extracted_data6.xlsx"

python c:\Users\<USER>\doc\5\zhihu\generate_sql_inserts.py c:\Users\<USER>\doc\5\zhihu\extracted_data6.csv -o c:\Users\<USER>\doc\5\zhihu\dml6.sql

python "c:\Users\<USER>\doc\5\extract_excel.py" "c:\Users\<USER>\doc\5\统计\2025年7月份生产及建设统计表.xlsx" --start-row 291 --end-row 299 --start-col A --end-col E --output "C:\Users\<USER>\doc\5\zhihu\extracted_data7.xlsx"

python c:\Users\<USER>\doc\5\zhihu\generate_sql_inserts.py c:\Users\<USER>\doc\5\zhihu\extracted_data7.csv -o c:\Users\<USER>\doc\5\zhihu\dml7.sql


python c:\Users\<USER>\doc\5\zhihu\collect_stope_names.py


-1020m(\d)-(\d+-\d+(/\d+)?)采场

$1#-$2