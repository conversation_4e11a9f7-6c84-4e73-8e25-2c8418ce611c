-- 支护数据视图（整体统计）

-- 创建视图：按日统计支护数据（总体）
CREATE OR REPLACE VIEW vdata_support_daily_total_stats AS
SELECT 
    operation_date,
    SUM(support_length) AS total_support_length,
    SUM(support_volume) AS total_support_volume
FROM 
    data_support
GROUP BY 
    operation_date
ORDER BY 
    operation_date;

-- 创建视图：按日统计支护数据（按作业时段、项目部门、采场、工作面）
CREATE OR REPLACE VIEW vdata_support_daily_stats AS
SELECT 
    operation_date,
    working_period_id,
    project_department_id,
    stope_id,
    working_face_id,
    SUM(support_length) AS total_support_length,
    SUM(support_volume) AS total_support_volume
FROM 
    data_support
GROUP BY 
    operation_date,
    working_period_id,
    project_department_id,
    stope_id,
    working_face_id
ORDER BY 
    operation_date, working_period_id, project_department_id, stope_id, working_face_id;

-- 创建视图：按日期和作业时段统计支护数据
CREATE OR REPLACE VIEW vdata_support_daily_period_stats AS
SELECT 
    t.operation_date,
    t.working_period_id,
    wp.working_period_name,
    SUM(t.support_length) AS total_support_length,
    SUM(t.support_volume) AS total_support_volume
FROM 
    data_support t
JOIN
    base_working_period wp ON t.working_period_id = wp.working_period_id
GROUP BY
    t.operation_date,
    t.working_period_id,
    wp.working_period_name
ORDER BY 
    t.operation_date, t.working_period_id;

-- 创建视图：按日期和项目部门统计支护数据
CREATE OR REPLACE VIEW vdata_support_daily_department_stats AS
SELECT 
    t.operation_date,
    t.project_department_id,
    pd.project_department_name,
    SUM(t.support_length) AS total_support_length,
    SUM(t.support_volume) AS total_support_volume
FROM 
    data_support t
JOIN
    base_project_department pd ON t.project_department_id = pd.project_department_id
GROUP BY
    t.operation_date,
    t.project_department_id,
    pd.project_department_name
ORDER BY 
    t.operation_date, t.project_department_id;

-- 创建视图：按日期和采场统计支护数据
CREATE OR REPLACE VIEW vdata_support_daily_stope_stats AS
SELECT 
    t.operation_date,
    t.stope_id,
    s.stope_name,
    SUM(t.support_length) AS total_support_length,
    SUM(t.support_volume) AS total_support_volume
FROM 
    data_support t
JOIN
    base_stope s ON t.stope_id = s.stope_id
GROUP BY
    t.operation_date,
    t.stope_id,
    s.stope_name
ORDER BY 
    t.operation_date, t.stope_id;

-- 创建视图：按日期和工作面统计支护数据
CREATE OR REPLACE VIEW vdata_support_daily_face_stats AS
SELECT 
    t.operation_date,
    t.working_face_id,
    wf.working_face_name,
    SUM(t.support_length) AS total_support_length,
    SUM(t.support_volume) AS total_support_volume
FROM 
    data_support t
JOIN
    base_working_face wf ON t.working_face_id = wf.working_face_id
GROUP BY
    t.operation_date,
    t.working_face_id,
    wf.working_face_name
ORDER BY 
    t.operation_date, t.working_face_id;

-- 创建视图：按周统计支护数据（总体）（周四到周三为一周）
CREATE OR REPLACE VIEW vdata_support_weekly_total_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number AS week,
    wk.week_start_date,
    wk.week_end_date,
    SUM(support_length) AS total_support_length,
    SUM(support_volume) AS total_support_volume
FROM 
    data_support
CROSS JOIN LATERAL get_week_thu_to_wed(operation_date) AS wk
GROUP BY 
    wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date
ORDER BY 
    wk.week_year, wk.week_number;

-- 创建视图：按周统计支护数据（按作业时段）
CREATE OR REPLACE VIEW vdata_support_weekly_period_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number AS week,
    wk.week_start_date,
    wk.week_end_date,
    t.working_period_id,
    wp.working_period_name,
    SUM(t.support_length) AS total_support_length,
    SUM(t.support_volume) AS total_support_volume
FROM 
    data_support t
CROSS JOIN LATERAL get_week_thu_to_wed(t.operation_date) AS wk
JOIN
    base_working_period wp ON t.working_period_id = wp.working_period_id
GROUP BY 
    wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date, t.working_period_id, wp.working_period_name
ORDER BY 
    year, week, t.working_period_id;

-- 创建视图：按周统计支护数据（按项目部门）
CREATE OR REPLACE VIEW vdata_support_weekly_department_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number AS week,
    wk.week_start_date,
    wk.week_end_date,
    t.project_department_id,
    pd.project_department_name,
    SUM(t.support_length) AS total_support_length,
    SUM(t.support_volume) AS total_support_volume
FROM 
    data_support t
CROSS JOIN LATERAL get_week_thu_to_wed(t.operation_date) AS wk
JOIN
    base_project_department pd ON t.project_department_id = pd.project_department_id
GROUP BY 
    wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date, t.project_department_id, pd.project_department_name
ORDER BY 
    year, week, t.project_department_id;

-- 创建视图：按周统计支护数据（按采场）
CREATE OR REPLACE VIEW vdata_support_weekly_stope_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number AS week,
    wk.week_start_date,
    wk.week_end_date,
    t.stope_id,
    s.stope_name,
    SUM(t.support_length) AS total_support_length,
    SUM(t.support_volume) AS total_support_volume
FROM 
    data_support t
CROSS JOIN LATERAL get_week_thu_to_wed(t.operation_date) AS wk
JOIN
    base_stope s ON t.stope_id = s.stope_id
GROUP BY 
    wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date, t.stope_id, s.stope_name
ORDER BY 
    year, week, t.stope_id;

-- 创建视图：按周统计支护数据（按工作面）
CREATE OR REPLACE VIEW vdata_support_weekly_face_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number AS week,
    wk.week_start_date,
    wk.week_end_date,
    t.working_face_id,
    wf.working_face_name,
    SUM(t.support_length) AS total_support_length,
    SUM(t.support_volume) AS total_support_volume
FROM 
    data_support t
CROSS JOIN LATERAL get_week_thu_to_wed(t.operation_date) AS wk
JOIN
    base_working_face wf ON t.working_face_id = wf.working_face_id
GROUP BY 
    wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date, t.working_face_id, wf.working_face_name
ORDER BY 
    year, week, t.working_face_id;

-- 创建视图：按月统计支护数据（总体）（月定义为前一个月的29号到当月28号）
CREATE OR REPLACE VIEW vdata_support_monthly_total_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    SUM(support_length) AS total_support_length,
    SUM(support_volume) AS total_support_volume
FROM 
    data_support
CROSS JOIN LATERAL get_financial_month(operation_date) AS fm
GROUP BY 
    fm.financial_year, fm.financial_month
ORDER BY 
    fm.financial_year, fm.financial_month;

-- 创建视图：按月统计支护数据（按作业时段）
CREATE OR REPLACE VIEW vdata_support_monthly_period_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    t.working_period_id,
    wp.working_period_name,
    SUM(t.support_length) AS total_support_length,
    SUM(t.support_volume) AS total_support_volume
FROM 
    data_support t
CROSS JOIN LATERAL get_financial_month(t.operation_date) AS fm
JOIN
    base_working_period wp ON t.working_period_id = wp.working_period_id
GROUP BY 
    fm.financial_year, fm.financial_month, t.working_period_id, wp.working_period_name
ORDER BY 
    year, month, t.working_period_id;

-- 创建视图：按月统计支护数据（按项目部门）
CREATE OR REPLACE VIEW vdata_support_monthly_department_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    t.project_department_id,
    pd.project_department_name,
    SUM(t.support_length) AS total_support_length,
    SUM(t.support_volume) AS total_support_volume
FROM 
    data_support t
CROSS JOIN LATERAL get_financial_month(t.operation_date) AS fm
JOIN
    base_project_department pd ON t.project_department_id = pd.project_department_id
GROUP BY 
    fm.financial_year, fm.financial_month, t.project_department_id, pd.project_department_name
ORDER BY 
    year, month, t.project_department_id;

-- 创建视图：按月统计支护数据（按采场）
CREATE OR REPLACE VIEW vdata_support_monthly_stope_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    t.stope_id,
    s.stope_name,
    SUM(t.support_length) AS total_support_length,
    SUM(t.support_volume) AS total_support_volume
FROM 
    data_support t
CROSS JOIN LATERAL get_financial_month(t.operation_date) AS fm
JOIN
    base_stope s ON t.stope_id = s.stope_id
GROUP BY 
    fm.financial_year, fm.financial_month, t.stope_id, s.stope_name
ORDER BY 
    year, month, t.stope_id;

-- 创建视图：按月统计支护数据（按工作面）
CREATE OR REPLACE VIEW vdata_support_monthly_face_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    t.working_face_id,
    wf.working_face_name,
    SUM(t.support_length) AS total_support_length,
    SUM(t.support_volume) AS total_support_volume
FROM 
    data_support t
CROSS JOIN LATERAL get_financial_month(t.operation_date) AS fm
JOIN
    base_working_face wf ON t.working_face_id = wf.working_face_id
GROUP BY 
    fm.financial_year, fm.financial_month, t.working_face_id, wf.working_face_name
ORDER BY 
    year, month, t.working_face_id;

-- 创建视图：按年统计支护数据（总体）
CREATE OR REPLACE VIEW vdata_support_yearly_total_stats AS
SELECT 
    fy.financial_year AS year,
    SUM(support_length) AS total_support_length,
    SUM(support_volume) AS total_support_volume
FROM 
    data_support
CROSS JOIN LATERAL get_financial_year(operation_date) AS fy
GROUP BY 
    fy.financial_year
ORDER BY 
    fy.financial_year;

-- 创建视图：按年统计支护数据（按作业时段）
CREATE OR REPLACE VIEW vdata_support_yearly_period_stats AS
SELECT 
    fy.financial_year AS year,
    t.working_period_id,
    wp.working_period_name,
    SUM(t.support_length) AS total_support_length,
    SUM(t.support_volume) AS total_support_volume
FROM 
    data_support t
CROSS JOIN LATERAL get_financial_year(t.operation_date) AS fy
JOIN
    base_working_period wp ON t.working_period_id = wp.working_period_id
GROUP BY 
    fy.financial_year, t.working_period_id, wp.working_period_name
ORDER BY 
    year, t.working_period_id;

-- 创建视图：按年统计支护数据（按项目部门）
CREATE OR REPLACE VIEW vdata_support_yearly_department_stats AS
SELECT 
    fy.financial_year AS year,
    t.project_department_id,
    pd.project_department_name,
    SUM(t.support_length) AS total_support_length,
    SUM(t.support_volume) AS total_support_volume
FROM 
    data_support t
CROSS JOIN LATERAL get_financial_year(t.operation_date) AS fy
JOIN
    base_project_department pd ON t.project_department_id = pd.project_department_id
GROUP BY 
    fy.financial_year, t.project_department_id, pd.project_department_name
ORDER BY 
    year, t.project_department_id;

-- 创建视图：按年统计支护数据（按采场）
CREATE OR REPLACE VIEW vdata_support_yearly_stope_stats AS
SELECT 
    fy.financial_year AS year,
    t.stope_id,
    s.stope_name,
    SUM(t.support_length) AS total_support_length,
    SUM(t.support_volume) AS total_support_volume
FROM 
    data_support t
CROSS JOIN LATERAL get_financial_year(t.operation_date) AS fy
JOIN
    base_stope s ON t.stope_id = s.stope_id
GROUP BY 
    fy.financial_year, t.stope_id, s.stope_name
ORDER BY 
    year, t.stope_id;

-- 创建视图：按年统计支护数据（按工作面）
CREATE OR REPLACE VIEW vdata_support_yearly_face_stats AS
SELECT 
    fy.financial_year AS year,
    t.working_face_id,
    wf.working_face_name,
    SUM(t.support_length) AS total_support_length,
    SUM(t.support_volume) AS total_support_volume
FROM 
    data_support t
CROSS JOIN LATERAL get_financial_year(t.operation_date) AS fy
JOIN
    base_working_face wf ON t.working_face_id = wf.working_face_id
GROUP BY 
    fy.financial_year, t.working_face_id, wf.working_face_name
ORDER BY 
    year, t.working_face_id;
