-- 支护数据插入语句
-- 生成时间: 2025-07-30 21:21:43

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-03-29', 2, 2, '1', 30.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-03-29', 3, 1, '1', 2.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-03-29', 3, 2, '1', 9.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-03-29', 3, 1, '2', 21.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-03-30', 2, 2, '1', 25.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-03-30', 2, 1, '2', 25.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-03-30', 3, 1, '1', 6.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-03-30', 3, 1, '2', 22.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-03-31', 2, 2, '1', 16.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-03-31', 2, 1, '2', 30.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-03-31', 3, 1, '1', 18.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-01', 2, 1, '1', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-01', 2, 1, '2', 40.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-01', 3, 1, '1', 17.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-02', 2, 2, '1', 23.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-02', 3, 1, '1', 20.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-02', 3, 2, '1', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-02', 3, 1, '2', 21.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-03', 2, 1, '1', 10.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-03', 2, 2, '1', 18.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-03', 2, 1, '2', 25.5, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-03', 3, 1, '1', 14.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-03', 3, 2, '1', 10.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-03', 3, 1, '2', 50.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-04', 2, 1, '1', 30.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-04', 2, 2, '1', 12.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-04', 2, 1, '2', 10.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-04', 2, 2, '2', 11.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-04', 3, 1, '1', 17.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-04', 3, 1, '2', 35.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-05', 2, 2, '1', 17.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-05', 3, 1, '1', 21.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-05', 3, 2, '1', 6.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-05', 3, 1, '2', 29.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-06', 2, 1, '2', 30.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-06', 3, 1, '1', 15.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-06', 3, 1, '2', 20.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-07', 2, 2, '1', 24.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-07', 2, 1, '2', 40.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-07', 3, 1, '1', 20.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-07', 3, 2, '1', 5.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-07', 3, 1, '2', 30.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-08', 2, 1, '2', 10.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-08', 2, 2, '2', 33.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-08', 3, 1, '1', 16.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-08', 3, 2, '1', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-08', 3, 1, '2', 29.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-08', 4, 2, '1', 2.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-08', 4, 2, '2', 12.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-09', 3, 1, '1', 16.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-09', 3, 2, '1', 10.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-09', 3, 1, '2', 135.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-09', 4, 1, '2', 6.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-10', 2, 2, '1', 20.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-10', 2, 1, '2', 33.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-10', 2, 2, '2', 46.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-10', 3, 1, '1', 18.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-10', 3, 2, '1', 6.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-10', 3, 1, '2', 43.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-11', 2, 2, '1', 30.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-11', 2, 2, '2', 76.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-11', 3, 1, '1', 17.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-11', 3, 2, '1', 6.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-11', 3, 1, '2', 22.5, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-12', 2, 2, '1', 27.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-12', 3, 1, '1', 12.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-12', 3, 2, '1', 6.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-12', 3, 1, '2', 36.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-12', 4, 1, '1', 5.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-12', 4, 1, '2', 3.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-12', 4, 2, '2', 2.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-13', 2, 2, '1', 48.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-13', 2, 1, '2', 35.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-13', 2, 2, '2', 24.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-13', 3, 1, '1', 7.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-13', 3, 1, '2', 31.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-13', 4, 1, '2', 2.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-13', 4, 2, '2', 2.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-14', 2, 2, '1', 35.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-14', 2, 2, '2', 25.5, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-14', 3, 1, '1', 13.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-14', 3, 2, '1', 9.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-14', 3, 1, '2', 38.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-14', 4, 2, '2', 10.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-15', 2, 2, '1', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-15', 3, 1, '1', 7.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-15', 3, 2, '1', 5.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-15', 3, 1, '2', 95.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-15', 4, 2, '1', 2.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-16', 2, 2, '1', 20.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-16', 2, 1, '2', 32.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-16', 2, 2, '2', 11.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-16', 3, 1, '1', 18.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-16', 3, 2, '1', 3.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-16', 3, 1, '2', 33.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-16', 4, 2, '1', 7.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-17', 2, 1, '1', 10.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-17', 2, 2, '1', 16.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-17', 3, 1, '1', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-17', 3, 2, '1', 3.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-17', 3, 1, '2', 36.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-17', 4, 2, '1', 3.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-17', 4, 1, '2', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-17', 4, 2, '2', 10.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-18', 2, 2, '1', 13.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-18', 3, 1, '1', 10.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-18', 3, 2, '1', 2.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-18', 3, 1, '2', 9.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-18', 4, 1, '2', 5.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-19', 2, 2, '1', 60.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-19', 2, 1, '2', 31.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-19', 3, 1, '1', 18.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-19', 3, 2, '1', 7.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-19', 3, 1, '2', 37.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-19', 4, 1, '1', 2.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-19', 4, 2, '1', 5.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-19', 4, 1, '2', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-20', 2, 2, '1', 11.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-20', 3, 1, '1', 14.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-20', 3, 2, '1', 14.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-20', 3, 1, '2', 38.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-20', 4, 1, '1', 3.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-20', 4, 2, '1', 6.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-20', 4, 2, '2', 7.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-21', 2, 2, '1', 22.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-21', 2, 1, '2', 22.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-21', 3, 1, '1', 10.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-21', 3, 2, '1', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-21', 3, 1, '2', 35.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-21', 4, 2, '1', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-21', 4, 1, '2', 5.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-21', 4, 2, '2', 7.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-22', 2, 2, '2', 42.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-22', 3, 1, '1', 15.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-22', 3, 2, '1', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-22', 3, 1, '2', 29.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-22', 4, 1, '1', 6.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-23', 2, 2, '1', 14.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-23', 2, 2, '2', 24.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-23', 3, 1, '1', 18.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-23', 3, 2, '1', 6.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-23', 3, 1, '2', 36.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-23', 4, 1, '2', 15.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-24', 2, 2, '1', 9.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-24', 3, 1, '1', 12.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-24', 3, 1, '2', 36.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-24', 3, 2, '2', 11.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-24', 4, 2, '1', 5.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-24', 4, 1, '2', 6.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-25', 2, 1, '2', 28.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-25', 2, 2, '2', 51.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-25', 3, 1, '1', 7.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-25', 3, 2, '1', 5.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-25', 3, 1, '2', 39.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-25', 4, 2, '1', 11.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-25', 4, 1, '2', 5.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-26', 2, 2, '2', 24.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-26', 3, 1, '1', 7.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-26', 3, 1, '2', 41.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-26', 4, 1, '2', 20.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-26', 4, 2, '2', 5.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-27', 2, 2, '2', 15.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-27', 3, 1, '2', 9.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-27', 4, 1, '2', 10.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-28', 2, 2, '2', 22.5, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-28', 3, 1, '1', 7.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-04-28', 3, 1, '2', 44.0, NULL, 'system', 'system');

