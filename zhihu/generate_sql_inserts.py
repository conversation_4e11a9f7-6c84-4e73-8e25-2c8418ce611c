#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
生成SQL插入语句脚本
从CSV文件生成PostgreSQL数据库的INSERT语句 - 支护数据
自动从数据库查询采场ID和工作面ID
"""

import pandas as pd
import re
from datetime import datetime
import os
import argparse
from decimal import Decimal, getcontext
import psycopg2
from psycopg2 import sql

# 设置decimal精度
getcontext().prec = 10  # 设置精度为10位

# 定义工作时段ID映射
WORKING_PERIOD_MAP = {
    '20-8时': 1,    # 20-8时对应时段1
    '8至20时': 2,   # 8至20时对应时段2
    '8-20时': 2     # 备用映射
}

def parse_date(date_str):
    """解析中文日期格式，转换为标准日期格式"""
    if not isinstance(date_str, str):
        return None
    
    try:
        # 提取月和日
        month_match = re.search(r'(\d+)月', date_str)
        day_match = re.search(r'(\d+)日', date_str)
        
        if month_match and day_match:
            month = int(month_match.group(1))
            day = int(day_match.group(1))
            # 假设年份是2025年
            return f"2025-{month:02d}-{day:02d}"
    except Exception as e:
        print(f"解析日期出错: {str(e)}")
    return None

def safe_value(value, default=0, as_decimal=True):
    """安全获取值，处理NaN和None，并确保返回数字类型"""
    if pd.isna(value) or value is None:
        return Decimal(default) if as_decimal else default
    
    try:
        if isinstance(value, str):
            value = value.replace(',', '').strip()
            if value == '':
                return Decimal(default) if as_decimal else default
            # 先尝试转换为浮点数，以处理科学计数法等格式
            num = float(value)
            if as_decimal:
                return Decimal(str(num))
            return num
        elif isinstance(value, (int, float)):
            return Decimal(str(value)) if as_decimal else value
        else:
            num = float(value)
            if as_decimal:
                return Decimal(str(num))
            return num
    except (ValueError, TypeError):
        print(f"警告: 无法转换值 '{value}'，使用默认值 {default}")
        return Decimal(default) if as_decimal else default

def connect_to_database(host='**********', port=5432, dbname='lxbi', user='postgres', password='admin321.'):
    """连接到PostgreSQL数据库"""
    try:
        conn = psycopg2.connect(
            host=host,
            port=port,
            dbname=dbname,
            user=user,
            password=password
        )
        print(f"成功连接到数据库: {dbname}@{host}:{port}")
        return conn
    except Exception as e:
        print(f"连接数据库失败: {e}")
        return None

def map_department_name(department_name):
    """映射部门名称到数据库中的名称"""
    department_mapping = {
        '中矿': '中矿建设',
        '涟邵': '涟邵建工',
        '中煤': '中煤三建'
    }
    return department_mapping.get(department_name, department_name)

def get_project_department_id(conn, department_name):
    """根据项目部门名称查询项目部门ID"""
    if not conn:
        print("数据库连接不可用，无法查询项目部门ID")
        return None
        
    try:
        with conn.cursor() as cursor:
            # 映射部门名称
            mapped_name = map_department_name(department_name)
            
            # 使用精确匹配查询项目部门ID
            cursor.execute(
                "SELECT project_department_id FROM base_project_department WHERE project_department_name = %s",
                (mapped_name,)
            )
            result = cursor.fetchone()
            if result:
                print(f"找到项目部门: {mapped_name} -> ID: {result[0]}")
                return result[0]
            else:
                print(f"未找到项目部门: {mapped_name}")
                return None
    except Exception as e:
        print(f"查询项目部门ID失败: {e}")
        return None

def get_default_stope_and_face(conn, department_name):
    """根据部门获取默认的采场和工作面ID"""
    if not conn:
        print("数据库连接不可用，无法查询采场和工作面ID")
        return None, None
        
    try:
        with conn.cursor() as cursor:
            # 查询该部门下的第一个采场和工作面
            cursor.execute("""
                SELECT s.stope_id, s.working_face_id 
                FROM base_stope s
                JOIN base_working_face wf ON s.working_face_id = wf.working_face_id
                WHERE s.status = 1 AND wf.status = 1
                ORDER BY s.stope_id
                LIMIT 1
            """)
            result = cursor.fetchone()
            if result:
                stope_id, working_face_id = result
                print(f"使用默认采场ID: {stope_id}, 工作面ID: {working_face_id}")
                return stope_id, working_face_id
            else:
                print("未找到可用的采场和工作面")
                return None, None
    except Exception as e:
        print(f"查询采场和工作面ID失败: {e}")
        return None, None

def extract_support_type(content):
    """从施工内容中提取支护类型"""
    if '锚网支护' in content:
        return '1'  # 锚网支护使用'1'
    elif '喷浆支护' in content:
        return '2'  # 喷浆支护使用'2'
    else:
        return None

def generate_sql_inserts(csv_file, output_file=None, encoding='utf-8', db_connect=True):
    """从CSV文件生成SQL插入语句"""
    
    # 连接数据库
    conn = None
    if db_connect:
        conn = connect_to_database()
        if not conn:
            print("无法连接数据库，将使用默认值")
    
    try:
        # 读取CSV文件
        df = pd.read_csv(csv_file, encoding=encoding)
        print(f"读取CSV文件: {csv_file}")
        print(f"数据行数: {len(df)}")
        print(f"列名: {list(df.columns)}")
        
        # 生成SQL语句列表
        sql_statements = []
        processed_rows = 0
        skipped_rows = 0
        last_department = None  # 记录上一行的部门
        
        for index, row in df.iterrows():
            try:
                # 检查是否为标题行或无效行
                date_str = str(row[0]) if not pd.isna(row[0]) else ""
                unit_str = str(row[1]) if len(row) > 1 and not pd.isna(row[1]) else ""
                content_str = str(row[2]) if len(row) > 2 and not pd.isna(row[2]) else ""
                
                # 处理部门名称，如果为空则使用上一行的部门
                if unit_str and unit_str.strip() and unit_str.strip() not in ["单位", "合计"]:
                    last_department = unit_str.strip()
                
                # 跳过包含标题信息的行
                if any(keyword in date_str for keyword in ["锚网喷支护统计表", "单位", "合计"]) or \
                   any(keyword in unit_str for keyword in ["单位", "合计"]) or \
                   any(keyword in content_str for keyword in ["施工内容", "20-8时", "8至20时"]) or \
                   content_str == "":
                    skipped_rows += 1
                    continue
                
                # 解析日期
                operation_date = parse_date(date_str)
                if not operation_date:
                    print(f"跳过无效日期行: {date_str}")
                    skipped_rows += 1
                    continue
                
                # 提取支护类型
                support_type = extract_support_type(content_str)
                if not support_type:
                    print(f"跳过无支护类型行: {content_str}")
                    skipped_rows += 1
                    continue
                
                # 使用当前部门或上一行的部门
                current_department = unit_str.strip() if unit_str and unit_str.strip() else last_department
                if not current_department:
                    print(f"跳过无部门信息行: {unit_str}")
                    skipped_rows += 1
                    continue
                
                # 获取项目部门ID
                project_department_id = None
                if conn:
                    project_department_id = get_project_department_id(conn, current_department)
                if not project_department_id:
                    project_department_id = 'NULL'  # 使用NULL
                
                # 处理不同时段的数据
                for time_period, col_index in [('20-8时', 3), ('8至20时', 4)]:
                    if len(row) > col_index:
                        length_value = safe_value(row[col_index], 0)
                        
                        # 只有长度大于0时才生成SQL
                        if length_value > 0:
                            working_period_id = WORKING_PERIOD_MAP.get(time_period, 1)
                            
                            sql = f"INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('{operation_date}', {project_department_id}, {working_period_id}, '{support_type}', {length_value}, NULL, 'system', 'system');"
                            sql_statements.append(sql)
                            processed_rows += 1
                            
            except Exception as e:
                print(f"处理第{index+1}行时出错: {str(e)}")
                skipped_rows += 1
                continue
        
        print(f"处理完成: 有效行数 {processed_rows}, 跳过行数 {skipped_rows}")
        
        # 输出SQL语句
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("-- 支护数据插入语句\n")
                f.write("-- 生成时间: " + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + "\n\n")
                for sql in sql_statements:
                    f.write(sql + "\n\n")
            print(f"SQL语句已保存到: {output_file}")
        else:
            print("生成的SQL语句:")
            for sql in sql_statements[:5]:  # 只显示前5条
                print(sql)
            if len(sql_statements) > 5:
                print(f"... 还有 {len(sql_statements) - 5} 条语句")
        
        return sql_statements
        
    except Exception as e:
        print(f"生成SQL语句时出错: {str(e)}")
        return []
    finally:
        if conn:
            conn.close()
            print("数据库连接已关闭")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='生成支护数据的SQL插入语句')
    parser.add_argument('csv_file', help='CSV文件路径')
    parser.add_argument('-o', '--output', help='输出SQL文件路径', default='zhihu_dml.sql')
    parser.add_argument('--encoding', help='CSV文件编码', default='utf-8')
    parser.add_argument('--no-db', action='store_true', help='不连接数据库，使用默认值')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.csv_file):
        print(f"错误: CSV文件不存在: {args.csv_file}")
        exit(1)
    
    generate_sql_inserts(
        csv_file=args.csv_file,
        output_file=args.output,
        encoding=args.encoding,
        db_connect=not args.no_db
    )
