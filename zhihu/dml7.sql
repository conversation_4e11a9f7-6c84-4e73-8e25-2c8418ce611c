-- 支护数据插入语句
-- 生成时间: 2025-07-30 21:27:36

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-29', 2, 2, '1', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-29', 3, 1, '1', 3.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-29', 3, 2, '1', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-29', 3, 1, '2', 24.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-29', 4, 1, '2', 10.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-30', 2, 1, '2', 17.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-30', 3, 1, '1', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-06-30', 3, 2, '1', 7.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-01', 2, 1, '1', 14.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-01', 2, 2, '1', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-01', 2, 1, '2', 10.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-01', 3, 1, '1', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-01', 3, 2, '1', 2.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-01', 3, 1, '2', 21.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-01', 3, 2, '2', 3.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-01', 4, 2, '1', 1.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-01', 4, 1, '2', 11.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-01', 4, 2, '2', 3.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-02', 3, 1, '1', 5.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-02', 3, 2, '1', 10.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-02', 3, 1, '2', 24.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-02', 3, 2, '2', 28.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-02', 4, 1, '2', 10.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-03', 2, 1, '2', 30.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-03', 3, 1, '1', 3.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-03', 3, 2, '1', 12.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-03', 3, 1, '2', 19.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-03', 3, 2, '2', 22.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-03', 4, 1, '2', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-04', 2, 1, '2', 24.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-04', 2, 2, '2', 16.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-04', 3, 1, '1', 7.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-04', 3, 2, '1', 6.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-04', 3, 2, '2', 27.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-05', 2, 2, '1', 20.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-05', 2, 1, '2', 14.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-05', 2, 2, '2', 16.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-05', 3, 1, '1', 1.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-05', 3, 2, '1', 12.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-05', 3, 1, '2', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-05', 3, 2, '2', 32.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-05', 4, 1, '2', 12.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-06', 2, 1, '2', 22.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-06', 3, 2, '1', 10.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-06', 3, 1, '2', 17.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-06', 3, 2, '2', 26.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-06', 4, 1, '1', 3.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-06', 4, 1, '2', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-07', 2, 1, '2', 16.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-07', 3, 2, '1', 11.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-07', 3, 2, '2', 21.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-07', 4, 1, '2', 9.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-08', 2, 2, '1', 24.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-08', 2, 1, '2', 24.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-08', 3, 2, '1', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-08', 3, 1, '2', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-08', 3, 2, '2', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-08', 4, 1, '2', 12.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-09', 2, 1, '1', 25.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-09', 2, 1, '2', 42.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-09', 3, 2, '1', 9.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-09', 3, 2, '2', 20.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-09', 4, 1, '2', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-10', 2, 2, '1', 15.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-10', 2, 1, '2', 9.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-10', 3, 1, '1', 2.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-10', 3, 2, '1', 3.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-10', 4, 1, '2', 9.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-11', 2, 2, '1', 14.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-11', 2, 1, '2', 34.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-11', 3, 1, '1', 7.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-11', 3, 2, '1', 6.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-11', 3, 1, '2', 40.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-11', 3, 2, '2', 26.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-11', 4, 1, '2', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-12', 2, 2, '1', 7.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-12', 2, 1, '2', 22.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-12', 3, 1, '1', 2.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-12', 3, 2, '1', 9.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-12', 3, 2, '2', 32.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-12', 4, 2, '1', 2.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-12', 4, 1, '2', 30.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-13', 2, 1, '2', 9.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-13', 2, 2, '2', 32.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-13', 3, 2, '1', 11.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-13', 4, 1, '2', 6.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-14', 2, 1, '2', 24.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-14', 3, 2, '1', 14.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-14', 3, 1, '2', 6.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-14', 3, 2, '2', 24.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-14', 4, 1, '2', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-14', 4, 2, '2', 3.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-15', 2, 2, '1', 20.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-15', 2, 1, '2', 30.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-15', 3, 1, '1', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-15', 3, 2, '1', 11.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-15', 3, 1, '2', 27.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-15', 3, 2, '2', 28.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-15', 4, 2, '1', 1.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-15', 4, 1, '2', 6.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-16', 2, 2, '1', 30.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-16', 2, 1, '2', 30.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-16', 2, 2, '2', 30.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-16', 3, 1, '1', 2.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-16', 3, 2, '1', 1.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-16', 4, 1, '2', 6.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-17', 2, 1, '1', 10.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-17', 2, 2, '1', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-17', 2, 1, '2', 32.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-17', 3, 1, '1', 3.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-17', 3, 2, '1', 7.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-17', 3, 1, '2', 60.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-17', 4, 2, '1', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-17', 4, 1, '2', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-18', 2, 1, '1', 8.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-18', 2, 1, '2', 24.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-18', 3, 1, '1', 4.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-18', 3, 1, '2', 24.0, NULL, 'system', 'system');

INSERT INTO data_support (operation_date, project_department_id, working_period_id, support_type, support_length, support_volume, create_by, update_by) VALUES ('2025-07-18', 4, 1, '2', 5.0, NULL, 'system', 'system');

