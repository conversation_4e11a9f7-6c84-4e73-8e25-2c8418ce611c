-- 按支护类型分组的支护数据视图

-- 创建视图：按日期和支护类型统计支护数据（总体）
CREATE OR REPLACE VIEW vdata_support_daily_type_stats AS
SELECT 
    operation_date,
    support_type,
    SUM(support_length) AS total_support_length,
    SUM(support_volume) AS total_support_volume
FROM 
    data_support
GROUP BY 
    operation_date, support_type
ORDER BY 
    operation_date, support_type;

-- 创建视图：按日期、支护类型和作业时段统计支护数据
CREATE OR REPLACE VIEW vdata_support_daily_type_period_stats AS
SELECT 
    t.operation_date,
    t.support_type,
    t.working_period_id,
    wp.working_period_name,
    SUM(t.support_length) AS total_support_length,
    SUM(t.support_volume) AS total_support_volume
FROM 
    data_support t
JOIN
    base_working_period wp ON t.working_period_id = wp.working_period_id
GROUP BY
    t.operation_date,
    t.support_type,
    t.working_period_id,
    wp.working_period_name
ORDER BY 
    t.operation_date, t.support_type, t.working_period_id;

-- 创建视图：按日期、支护类型和项目部门统计支护数据
CREATE OR REPLACE VIEW vdata_support_daily_type_department_stats AS
SELECT 
    t.operation_date,
    t.support_type,
    t.project_department_id,
    pd.project_department_name,
    SUM(t.support_length) AS total_support_length,
    SUM(t.support_volume) AS total_support_volume
FROM 
    data_support t
JOIN
    base_project_department pd ON t.project_department_id = pd.project_department_id
GROUP BY
    t.operation_date,
    t.support_type,
    t.project_department_id,
    pd.project_department_name
ORDER BY 
    t.operation_date, t.support_type, t.project_department_id;

-- 创建视图：按日期、支护类型和采场统计支护数据
CREATE OR REPLACE VIEW vdata_support_daily_type_stope_stats AS
SELECT 
    t.operation_date,
    t.support_type,
    t.stope_id,
    s.stope_name,
    SUM(t.support_length) AS total_support_length,
    SUM(t.support_volume) AS total_support_volume
FROM 
    data_support t
JOIN
    base_stope s ON t.stope_id = s.stope_id
GROUP BY
    t.operation_date,
    t.support_type,
    t.stope_id,
    s.stope_name
ORDER BY 
    t.operation_date, t.support_type, t.stope_id;

-- 创建视图：按日期、支护类型和工作面统计支护数据
CREATE OR REPLACE VIEW vdata_support_daily_type_face_stats AS
SELECT 
    t.operation_date,
    t.support_type,
    t.working_face_id,
    wf.working_face_name,
    SUM(t.support_length) AS total_support_length,
    SUM(t.support_volume) AS total_support_volume
FROM 
    data_support t
JOIN
    base_working_face wf ON t.working_face_id = wf.working_face_id
GROUP BY
    t.operation_date,
    t.support_type,
    t.working_face_id,
    wf.working_face_name
ORDER BY 
    t.operation_date, t.support_type, t.working_face_id;

-- 创建视图：按周和支护类型统计支护数据（总体）（周四到周三为一周）
CREATE OR REPLACE VIEW vdata_support_weekly_type_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number AS week,
    wk.week_start_date,
    wk.week_end_date,
    support_type,
    SUM(support_length) AS total_support_length,
    SUM(support_volume) AS total_support_volume
FROM 
    data_support
CROSS JOIN LATERAL get_week_thu_to_wed(operation_date) AS wk
GROUP BY 
    wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date, support_type
ORDER BY 
    wk.week_year, wk.week_number, support_type;

-- 创建视图：按周、支护类型和作业时段统计支护数据
CREATE OR REPLACE VIEW vdata_support_weekly_type_period_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number AS week,
    wk.week_start_date,
    wk.week_end_date,
    t.support_type,
    t.working_period_id,
    wp.working_period_name,
    SUM(t.support_length) AS total_support_length,
    SUM(t.support_volume) AS total_support_volume
FROM 
    data_support t
CROSS JOIN LATERAL get_week_thu_to_wed(t.operation_date) AS wk
JOIN
    base_working_period wp ON t.working_period_id = wp.working_period_id
GROUP BY 
    wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date, t.support_type, t.working_period_id, wp.working_period_name
ORDER BY 
    year, week, t.support_type, t.working_period_id;

-- 创建视图：按周、支护类型和项目部门统计支护数据
CREATE OR REPLACE VIEW vdata_support_weekly_type_department_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number AS week,
    wk.week_start_date,
    wk.week_end_date,
    t.support_type,
    t.project_department_id,
    pd.project_department_name,
    SUM(t.support_length) AS total_support_length,
    SUM(t.support_volume) AS total_support_volume
FROM 
    data_support t
CROSS JOIN LATERAL get_week_thu_to_wed(t.operation_date) AS wk
JOIN
    base_project_department pd ON t.project_department_id = pd.project_department_id
GROUP BY 
    wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date, t.support_type, t.project_department_id, pd.project_department_name
ORDER BY 
    year, week, t.support_type, t.project_department_id;

-- 创建视图：按周、支护类型和采场统计支护数据
CREATE OR REPLACE VIEW vdata_support_weekly_type_stope_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number AS week,
    wk.week_start_date,
    wk.week_end_date,
    t.support_type,
    t.stope_id,
    s.stope_name,
    SUM(t.support_length) AS total_support_length,
    SUM(t.support_volume) AS total_support_volume
FROM 
    data_support t
CROSS JOIN LATERAL get_week_thu_to_wed(t.operation_date) AS wk
JOIN
    base_stope s ON t.stope_id = s.stope_id
GROUP BY 
    wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date, t.support_type, t.stope_id, s.stope_name
ORDER BY 
    year, week, t.support_type, t.stope_id;

-- 创建视图：按周、支护类型和工作面统计支护数据
CREATE OR REPLACE VIEW vdata_support_weekly_type_face_stats AS
SELECT 
    wk.week_year AS year,
    wk.week_number AS week,
    wk.week_start_date,
    wk.week_end_date,
    t.support_type,
    t.working_face_id,
    wf.working_face_name,
    SUM(t.support_length) AS total_support_length,
    SUM(t.support_volume) AS total_support_volume
FROM 
    data_support t
CROSS JOIN LATERAL get_week_thu_to_wed(t.operation_date) AS wk
JOIN
    base_working_face wf ON t.working_face_id = wf.working_face_id
GROUP BY 
    wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date, t.support_type, t.working_face_id, wf.working_face_name
ORDER BY 
    year, week, t.support_type, t.working_face_id;

-- 创建视图：按月和支护类型统计支护数据（总体）（月定义为前一个月的29号到当月28号）
CREATE OR REPLACE VIEW vdata_support_monthly_type_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    support_type,
    SUM(support_length) AS total_support_length,
    SUM(support_volume) AS total_support_volume
FROM 
    data_support
CROSS JOIN LATERAL get_financial_month(operation_date) AS fm
GROUP BY 
    fm.financial_year, fm.financial_month, support_type
ORDER BY 
    fm.financial_year, fm.financial_month, support_type;

-- 创建视图：按月、支护类型和作业时段统计支护数据
CREATE OR REPLACE VIEW vdata_support_monthly_type_period_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    t.support_type,
    t.working_period_id,
    wp.working_period_name,
    SUM(t.support_length) AS total_support_length,
    SUM(t.support_volume) AS total_support_volume
FROM 
    data_support t
CROSS JOIN LATERAL get_financial_month(t.operation_date) AS fm
JOIN
    base_working_period wp ON t.working_period_id = wp.working_period_id
GROUP BY 
    fm.financial_year, fm.financial_month, t.support_type, t.working_period_id, wp.working_period_name
ORDER BY 
    year, month, t.support_type, t.working_period_id;

-- 创建视图：按月、支护类型和项目部门统计支护数据
CREATE OR REPLACE VIEW vdata_support_monthly_type_department_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    t.support_type,
    t.project_department_id,
    pd.project_department_name,
    SUM(t.support_length) AS total_support_length,
    SUM(t.support_volume) AS total_support_volume
FROM 
    data_support t
CROSS JOIN LATERAL get_financial_month(t.operation_date) AS fm
JOIN
    base_project_department pd ON t.project_department_id = pd.project_department_id
GROUP BY 
    fm.financial_year, fm.financial_month, t.support_type, t.project_department_id, pd.project_department_name
ORDER BY 
    year, month, t.support_type, t.project_department_id;

-- 创建视图：按月、支护类型和采场统计支护数据
CREATE OR REPLACE VIEW vdata_support_monthly_type_stope_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    t.support_type,
    t.stope_id,
    s.stope_name,
    SUM(t.support_length) AS total_support_length,
    SUM(t.support_volume) AS total_support_volume
FROM 
    data_support t
CROSS JOIN LATERAL get_financial_month(t.operation_date) AS fm
JOIN
    base_stope s ON t.stope_id = s.stope_id
GROUP BY 
    fm.financial_year, fm.financial_month, t.support_type, t.stope_id, s.stope_name
ORDER BY 
    year, month, t.support_type, t.stope_id;

-- 创建视图：按月、支护类型和工作面统计支护数据
CREATE OR REPLACE VIEW vdata_support_monthly_type_face_stats AS
SELECT 
    fm.financial_year AS year,
    fm.financial_month AS month,
    t.support_type,
    t.working_face_id,
    wf.working_face_name,
    SUM(t.support_length) AS total_support_length,
    SUM(t.support_volume) AS total_support_volume
FROM 
    data_support t
CROSS JOIN LATERAL get_financial_month(t.operation_date) AS fm
JOIN
    base_working_face wf ON t.working_face_id = wf.working_face_id
GROUP BY 
    fm.financial_year, fm.financial_month, t.support_type, t.working_face_id, wf.working_face_name
ORDER BY 
    year, month, t.support_type, t.working_face_id;

-- 创建视图：按年和支护类型统计支护数据（总体）
CREATE OR REPLACE VIEW vdata_support_yearly_type_stats AS
SELECT 
    fy.financial_year AS year,
    support_type,
    SUM(support_length) AS total_support_length,
    SUM(support_volume) AS total_support_volume
FROM 
    data_support
CROSS JOIN LATERAL get_financial_year(operation_date) AS fy
GROUP BY 
    fy.financial_year, support_type
ORDER BY 
    fy.financial_year, support_type;

-- 创建视图：按年、支护类型和作业时段统计支护数据
CREATE OR REPLACE VIEW vdata_support_yearly_type_period_stats AS
SELECT 
    fy.financial_year AS year,
    t.support_type,
    t.working_period_id,
    wp.working_period_name,
    SUM(t.support_length) AS total_support_length,
    SUM(t.support_volume) AS total_support_volume
FROM 
    data_support t
CROSS JOIN LATERAL get_financial_year(t.operation_date) AS fy
JOIN
    base_working_period wp ON t.working_period_id = wp.working_period_id
GROUP BY 
    fy.financial_year, t.support_type, t.working_period_id, wp.working_period_name
ORDER BY 
    year, t.support_type, t.working_period_id;

-- 创建视图：按年、支护类型和项目部门统计支护数据
CREATE OR REPLACE VIEW vdata_support_yearly_type_department_stats AS
SELECT 
    fy.financial_year AS year,
    t.support_type,
    t.project_department_id,
    pd.project_department_name,
    SUM(t.support_length) AS total_support_length,
    SUM(t.support_volume) AS total_support_volume
FROM 
    data_support t
CROSS JOIN LATERAL get_financial_year(t.operation_date) AS fy
JOIN
    base_project_department pd ON t.project_department_id = pd.project_department_id
GROUP BY 
    fy.financial_year, t.support_type, t.project_department_id, pd.project_department_name
ORDER BY 
    year, t.support_type, t.project_department_id;

-- 创建视图：按年、支护类型和采场统计支护数据
CREATE OR REPLACE VIEW vdata_support_yearly_type_stope_stats AS
SELECT 
    fy.financial_year AS year,
    t.support_type,
    t.stope_id,
    s.stope_name,
    SUM(t.support_length) AS total_support_length,
    SUM(t.support_volume) AS total_support_volume
FROM 
    data_support t
CROSS JOIN LATERAL get_financial_year(t.operation_date) AS fy
JOIN
    base_stope s ON t.stope_id = s.stope_id
GROUP BY 
    fy.financial_year, t.support_type, t.stope_id, s.stope_name
ORDER BY 
    year, t.support_type, t.stope_id;

-- 创建视图：按年、支护类型和工作面统计支护数据
CREATE OR REPLACE VIEW vdata_support_yearly_type_face_stats AS
SELECT 
    fy.financial_year AS year,
    t.support_type,
    t.working_face_id,
    wf.working_face_name,
    SUM(t.support_length) AS total_support_length,
    SUM(t.support_volume) AS total_support_volume
FROM 
    data_support t
CROSS JOIN LATERAL get_financial_year(t.operation_date) AS fy
JOIN
    base_working_face wf ON t.working_face_id = wf.working_face_id
GROUP BY 
    fy.financial_year, t.support_type, t.working_face_id, wf.working_face_name
ORDER BY 
    year, t.support_type, t.working_face_id;
